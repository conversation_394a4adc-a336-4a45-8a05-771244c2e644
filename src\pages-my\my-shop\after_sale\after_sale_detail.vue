<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '商户售后详情',
  },
}
</route>

<template>
  <view class="pb100rpx">
    <!--售后状态-->
    <view class="bg-#ff5704 text-34rpx flex items-center text-white gap20rpx p30rpx">
      <view class="i-carbon-warning-alt"></view>
      <view class="state-cont flex-1">
        <view class="state-txt d-b-c">
          <text class="desc text-34rpx">{{ detail?.statusText }}</text>
        </view>
      </view>
      <view class="dot-bg"></view>
    </view>

    <!--商品信息-->
    <view class="p30rpx mt20rpx bg-white">
      <view class="flex items-center gap30rpx pb20rpx">
        <view
          class="w170rpx h170rpx"
          @click.stop="gotoProduct(detail.orderProduct.productId, detail.order.storeId)"
        >
          <image
            :src="detail?.orderProduct && detail?.orderProduct?.imagePath"
            mode="aspectFit"
          ></image>
        </view>
        <view class="flex-1 mr20rpx">
          <view class="text-32rpx text-#666">
            {{ detail?.orderProduct && detail?.orderProduct?.productName }}
          </view>
          <view class="pt10rpx">
            <text class="text-24rpx">
              <!--斤:10斤; 颜色:红; 出产地:北方; 销量:大于1000; 大小:直径1分米; 口感:甜;-->
            </text>
          </view>
        </view>
      </view>
      <view class="pt20rpx flex items-center justify-end">
        <view class="text-28rpx">
          商品金额：
          <text class="text-red">¥{{ totalPrice }}</text>
        </view>
      </view>
      <view class="pt10rpx flex items-center justify-end">
        <view class="text-28rpx">
          订单实付金额：
          <text class="text-red">¥{{ totalPayPrice }}</text>
        </view>
      </view>
      <!-- <view class="pt10rpx lh-150rpx" v-if="detail.orderM && detail.orderM.orderSource == 80">
        <view class="text-28rpx" v-if="detail.orderM.advance">
          定金({{ detail.orderM.advance.moneyReturn ? '可退' : '不可退' }})：
          <text class="text-red">¥{{ detail.order.advance.payPrice }}</text>
        </view>
      </view> -->
    </view>

    <!-- 已退款金额 -->
    <view
      class="text-28rpx bg-white flex items-center justify-end px30rpx"
      v-if="
        (detail?.status == 20 && detail?.type == 10) || (detail.status == 20 && detail.type == 30)
      "
    >
      <text class="gray9">已退款金额：</text>
      <text class="text-red">￥{{ detail?.refundMoney }}</text>
    </view>

    <!--申请售后信息-->
    <view class="px30rpx mb20rpx box-border bg-white text-28rpx">
      <view class="py20rpx border-b text-34rpx">申请退货信息</view>
      <view class="py20rpx text-30rpx">
        <text class="text-#999">售后类型：</text>
        <text>{{ detail?.typeText }}</text>
      </view>
      <view class="py20rpx text-30rpx">
        <text class="text-#999">申请原因：</text>
        <text>
          {{ detail?.applyDesc || '' }}
        </text>
      </view>
      <view class="py20rpx text-30rpx flex items-center gap20rpx">
        <text class="text-#999">申请凭证：</text>
        <view class="flex flex-wrap">
          <block v-if="detail?.images && detail?.images.length > 0">
            <view
              style="border: 2rpx solid #dddddd"
              class=""
              v-for="(imgs, img_num) in detail.images"
              :key="img_num"
            >
              <wd-img
                :src="imgs?.filePath"
                :preview-src="imgs?.filePath"
                :width="102"
                :height="102"
                :enable-preview="true"
              />
            </view>
          </block>
          <block v-else>无</block>
        </view>
      </view>
    </view>

    <!-- 售后信息-->
    <view class="px30rpx bg-white">
      <view v-if="detail?.isAgree == 20" class="flex items-center">
        <view class="min-w220rpx py20rpx text-34rpx">商家拒绝理由:</view>
        <view class="px20rpx text-red text-28rpx break-all">
          {{ detail?.refuseDesc }}
        </view>
      </view>

      <view class="flex items-center">
        <view class="min-w220rpx py20rpx border-b text-34rpx">平台拒绝理由:</view>
        <view class="py20rpx">
          <text class="text-red text-30rpx break-all">{{ detail?.plateDesc }}</text>
        </view>
      </view>
    </view>

    <!--退货地址 v-if="detail.isAgree == 10 && detail.address"-->
    <view
      v-if="detail?.isAgree == 10 && detail.address"
      class="mb20rpx box-border px30rpx bg-white"
    >
      <view style="border-bottom: 1rpx solid #eeeeee" class="py20rpx text-34rpx">退货地址</view>
      <view class="pt30rpx text-28rpx">
        <text class="gray9">收货人：</text>
        <text class="break-all">{{ detail?.address?.name }}</text>
      </view>
      <view class="pt30rpx text-28rpx">
        <text class="gray9">联系电话：</text>
        <text class="break-all">{{ detail?.address?.phone }}</text>
      </view>
      <view class="pt30rpx text-28rpx">
        <text class="gray9">详情地址：</text>
        <text class="break-all">{{ detail?.address?.detail }}</text>
      </view>
      <view class="pt30rpx text-28rpx" v-if="detail?.expressNo">
        <text class="gray9">物流公司：</text>
        <text class="break-all">{{ detail?.express.expressName }}</text>
      </view>
      <view class="pt30rpx text-28rpx" v-if="detail?.expressNo">
        <text class="gray9">物流单号：</text>
        <text class="break-all">{{ detail?.expressNo }}</text>
      </view>
      <view class="pt30rpx text-28rpx" v-if="detail.isPlateSend">
        <text class="gray9">换货物流公司：</text>
        <text class="break-all">{{ detail?.sendExpressName }}</text>
      </view>
      <view
        class="pt30rpx text-28rpx"
        v-if="detail.isPlateSend"
        @click="gotoPage('/pages/order/express/refund-express?orderId=' + detail?.orderRefundId)"
      >
        <text class="gray9">换货物流单号：</text>
        <text class="break-all">{{ detail?.sendExpressNo }}</text>
      </view>
      <view style="border-top: 1rpx solid #eeeeee" class="mt20rpx pb20rpx text-24rpx text-#999">
        <view class="pt20rpx">· 未与卖家协商一致情况下，请勿寄到付或平邮</view>
        <view class="pt10rpx">· 请填写真实有效物流信息</view>
      </view>
    </view>
    <!-- 填写物流信息&& detail.isUserSend == 1 （0用户未发货，1用户已发货）-->
    <form
      @submit="confirmRefundMoney"
      v-if="
        detail.type == 20 && detail.isAgree == 10 && detail.isUserSend == 1 && detail.isReceipt == 0
      "
      report-submit
    >
      <view class="group bg-white">
        <view class="p30rpx border-b text-34rpx">填写换货物流信息</view>
        <view class="p-20-0 d-s-c">
          <!-- <view class="gray9">物流公司：</view> -->
          <view class="flex-1">
            <wd-picker
              :columns="columns"
              label="物流公司"
              placeholder="请选择物流公司"
              v-model="orderAfterSalesForm.sendExpressId"
              @confirm="handleConfirm"
            />
          </view>
        </view>
        <view class="px30rpx text-24rpx">
          <view class="flex-1">
            <wd-input
              type="text"
              label="物流单号："
              placeholder="请填写物流单号"
              v-model="orderAfterSalesForm.sendExpressNo"
              center
            ></wd-input>
          </view>
        </view>
        <view class="mt20rpx px30rpx py10rpx">
          <button
            style="background: #f6220c; color: #fff"
            class="!rounded-44rpx text-24rpx"
            @click="confirmRefundMoney(detail?.orderRefundId)"
          >
            确认发货
          </button>
        </view>
      </view>
    </form>
    <!-- 商家审核 && detail.orderM.orderStatus === 21 -->
    <form @submit="auditSubmit" v-if="isAudit(detail?.isAgree)" report-submit>
      <view class="text-34rpx fw-bold text-center py20rpx">商家审核{{ detail?.typeText }}</view>
      <wd-cell title="售后类型" :value="detail?.typeText" title-width="170rpx"></wd-cell>
      <wd-cell title="审核状态" title-width="170rpx">
        <template #default>
          <wd-radio-group
            custom-style="display:flex; gap:40rpx"
            shape="dot"
            v-model="orderAfterSalesForm.isAgree"
          >
            <wd-radio custom-style="" shape="dot" :value="10">同意</wd-radio>
            <wd-radio custom-style="" shape="dot" :value="20">拒绝</wd-radio>
          </wd-radio-group>
        </template>
      </wd-cell>
      <wd-select-picker
        v-if="orderAfterSalesForm?.isAgree == 10 && detail?.type != 30"
        type="radio"
        label-width="170rpx"
        label="退货地址"
        v-model="orderAfterSalesForm.addressId"
        :columns="addressColumns"
        @change="handleChangeAddress"
      ></wd-select-picker>

      <wd-select-picker
        v-if="detail?.isAgree == 10 && detail?.type == 20"
        label-width="170rpx"
        type="radio"
        label="换货地址"
        v-model="orderAfterSalesForm.addressId"
        :columns="addressColumns"
        @change="handleChangeAddress"
      ></wd-select-picker>
      <wd-cell
        v-if="orderAfterSalesForm?.isAgree == 10 && detail?.type == 30"
        title="退款金额"
        title-width="170rpx"
      >
        <template #default>
          <wd-input
            type="text"
            placeholder="请填写退款金额"
            v-model="orderAfterSalesForm.refundMoney"
            center
          ></wd-input>
        </template>
      </wd-cell>
      <!-- <div>
              请输入退款金额，最多{{ detail.orderProduct.totalPayPrice }}
              元。注：该操作将执行订单原路退款
              并关闭当前售后单，请确认并填写退款的金额（不能大于订单实付款）
            </div> -->
      <!-- 商家拒绝原因 -->
      <div class="w-full">
        <wd-cell v-if="orderAfterSalesForm?.isAgree == 20" title="拒绝原因" title-width="170rpx">
          <template #default>
            <wd-textarea
              v-model="orderAfterSalesForm.refuseDesc"
              auto-height
              placeholder="请填写拒绝原因"
            />
          </template>
        </wd-cell>
      </div>
      <view class="flex justify-center items-center p20rpx">
        <button class="m0 bg-#28a5ff text-28rpx text-white" @click="auditSubmit">确认审核</button>
      </view>
    </form>

    <!-- 确认收货并退款 -->
    <form
      v-if="
        detail?.type == 10 &&
        detail?.isAgree == 10 &&
        detail?.isUserSend == 1 &&
        detail?.isReceipt == 0
      "
      @submit="auditSubmit"
      report-submit
    >
      <view class="group bg-white">
        <view class="p30rpx border-b text-34rpx">确认收货并退款</view>
        <view class="flex-1 text-#999999 text-20rpx px20rpx box-border">
          注：该操作将执行订单原路退款
          并关闭当前售后单，请确认并填写退款的金额（不能大于订单实付款）
        </view>
        <wd-cell title="售后类型" :value="detail?.typeText" title-width="170rpx"></wd-cell>

        <wd-cell title="退款金额：" title-width="170rpx">
          <template #default>
            <wd-input
              type="number"
              placeholder="请填写退款金额"
              v-model="orderAfterSalesForm.refundMoney"
              center
            ></wd-input>
          </template>
        </wd-cell>
        <view class="px30rpx text-24rpx">
          <view class="flex-1 text-#999999 text-20rpx">
            请输入退款金额，最多{{ detail?.orderProduct?.totalPayPrice }} 元
          </view>
        </view>
        <view class="mt20rpx px30rpx py10rpx">
          <button
            style="color: #fff"
            class="bg-#28a5ff !rounded-44rpx text-24rpx"
            @click="confirmRefundMoney(detail?.orderRefundId)"
          >
            确认收货
          </button>
        </view>
      </view>
    </form>

    <!-- 确认收货并退款 -->
    <!-- <form
      v-if="
        detail.type == 10 && detail.isAgree == 10 && detail.isUserSend == 1 && detail.isReceipt == 0
      "
      @submit="auditSubmit"
      report-submit
    >
      <view class="group bg-white">
        <view class="p30rpx border-b text-34rpx">确认收货并退款</view>
        <view class="flex-1 text-#999999 text-20rpx">
          注：该操作将执行订单原路退款
          并关闭当前售后单，请确认并填写退款的金额（不能大于订单实付款）
        </view>
        <wd-cell title="售后类型" :value="detail.typeText" title-width="170rpx"></wd-cell>

        <wd-cell title="退款金额：" title-width="170rpx">
          <template #default>
            <wd-input
              type="number"
              placeholder="请填写退款金额"
              v-model="orderAfterSalesForm.refundMoney"
              center
            ></wd-input>
          </template>
        </wd-cell>
        <view class="px30rpx text-24rpx">
          <view class="flex-1 text-#999999 text-20rpx">
            请输入退款金额，最多{{ detail.orderProduct.totalPayPrice }} 元
          </view>
        </view>
        <view class="mt20rpx px30rpx py10rpx">
          <button
            style="color: #fff"
            class="bg-#28a5ff !rounded-44rpx text-24rpx"
            @click="confirmRefundMoney(detail.orderRefundId)"
          >
            确认收货
          </button>
        </view>
      </view>
    </form> -->
    <!-- <div
        v-if="
          detail.type == 10 &&
          detail.isAgree == 10 &&
          detail.isUserSend == 1 &&
          detail.isReceipt == 0
        "
      >
        <div class="common-form mt16">确认收货并退款</div>
        <el-form size="small" ref="money" :model="money" label-width="80px">
          <p>
            注：该操作将执行订单原路退款
            并关闭当前售后单，请确认并填写退款的金额（不能大于订单实付款）
          </p>
          <el-form-item label="售后类型">
            <span class="orange">{{ detail.typeText }}</span>
          </el-form-item>
          <el-form-item label="退款金额">
            <el-input v-model="money.refundMoney"></el-input>
            <p>
              请输入退款金额，最多{{ detail.orderProduct.totalPayPrice }} 元
            </p>
          </el-form-item>
        </el-form>
      </div> -->
  </view>
</template>

<script lang="ts" setup>
import {
  fetchStoreRefunDetail,
  storeRefundAudit,
  storeRefundReceipt,
  refundDelivery,
} from '@/service'
import { useUserSettingStore } from '@/store'
let detail = ref<any>(null)
let orderRefundId = ref('')
let source = ref('user')
let productId = ref('')
let orderId = ref('')
let totalPrice = ref('')
let totalPayPrice = ref('')
let columns = ref([])
const userSettingStore = useUserSettingStore()
const shopSupplierId = computed(() => userSettingStore?.userSetting?.supplierUser?.shopSupplierId)
//商家售后
let orderAfterSalesForm = ref({
  isAgree: 10,
  refuseDesc: '',
  orderRefundId: '',
  addressId: '',
  refundMoney: 0,
  sendExpressNo: '',
  sendExpressId: '',
})
const addressColumns = ref([
  // {
  //   value: '1',
  //   label: '花园小区',
  // },
])

onLoad((options) => {
  if (options.order_refund_id) {
    orderRefundId.value = options.order_refund_id
  }
  if (source.value) {
    source.value = options.source
  }
  if (options.productId) {
    productId.value = options.productId
  }
  if (options.orderId) {
    orderId.value = options.orderId
  }
})
onMounted(() => {
  fetchData()
})
const isAudit = (isAgree: any): boolean => {
  return isAgree == 0
}
const gotoProduct = (productId, storeId) => {
  uni.navigateTo({
    url: `/pages-sub/goods/detail/index?id=${productId}&storeId=${storeId}&from=mall`,
  })
}
const paging = ref()
const fetchData = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    const { data } = await fetchStoreRefunDetail({
      // orderRefundId: orderRefundId.value,
      // appId: import.meta.env.VITE_APPID,
      // orderId: orderId.value,
      // productId: productId.value,
      orderRefundId: orderRefundId.value,
      appId: import.meta.env.VITE_APPID,
      shopSupplierId: shopSupplierId.value,
    })
    detail.value = data
    console.log('   detail.value ', detail.value)

    totalPrice.value = data?.orderProduct?.totalPrice
    console.log('totalPrice.value', totalPrice.value)

    totalPayPrice.value = data?.orderProduct?.totalPayPrice
    //退款金额默认值
    orderAfterSalesForm.value.refundMoney = Number(data.orderProduct.totalPayPrice)

    if (data?.addressList && data?.addressList.length > 0) {
      addressColumns.value = data.addressList.map((ele) => {
        return {
          label: ele.detail,
          value: ele.addressId,
        }
      })
    }
    if (data?.expressList && data?.expressList.length > 0) {
      columns.value = data.expressList.map((ele) => {
        return {
          label: ele.expressName,
          value: ele.expressId,
        }
      })
    }
    console.log('expressList', columns.value)

    uni.hideLoading()
  } catch (err) {
    uni.hideLoading()
  }
}
//确认地址
const handleChangeAddress = () => {}
//选择物流公司
const handleConfirm = (e) => {}
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
// 退货退款审核
const auditSubmit = async () => {
  orderAfterSalesForm.value.orderRefundId = orderRefundId.value
  if (!orderAfterSalesForm.value.refundMoney) {
    uni.showToast({ title: '请输入退款金额！', icon: 'none' })
    return
  }
  if (orderAfterSalesForm.value.refundMoney > Number(detail.value.orderProduct.totalPayPrice)) {
    uni.showToast({ title: '退款金额超过订单金额！', icon: 'none' })
    return
  }
  if (orderAfterSalesForm.value.isAgree == 10) {
    orderAfterSalesForm.value.refuseDesc = ''
  }

  uni.showLoading({ title: '正在提交', mask: true })
  // 实际提交逻辑
  try {
    const res = await storeRefundAudit({
      ...orderAfterSalesForm.value,
      // orderId: orderId.value,
      appId: import.meta.env.VITE_APPID,
    })
    uni.hideLoading()
    fetchData()
    uni.showToast({ title: res.msg || '审核成功', duration: 1000, icon: 'none' })
  } catch (err) {
    uni.hideLoading()
    uni.showToast({ title: err.data.msg || '审核失败，请重试', duration: 1000, icon: 'none' })
  }
}

/*确认收货退款*/
const confirmRefundMoney = async (orderRefundId: any) => {
  orderAfterSalesForm.value.orderRefundId = orderRefundId
  console.log('orderRefundId.value', orderRefundId)

  if (
    !orderAfterSalesForm.value.sendExpressId &&
    orderAfterSalesForm.value.sendExpressId == '' &&
    detail.value.type == 20
  ) {
    uni.showToast({ title: '请选择物流公司', icon: 'none', duration: 1000 })
    return
  }
  if (
    !orderAfterSalesForm.value.sendExpressNo &&
    orderAfterSalesForm.value.sendExpressNo == '' &&
    detail.value.type == 20
  ) {
    uni.showToast({ title: '请填写物流单号', icon: 'none', duration: 1000 })
    return
  }

  uni.showLoading({ title: '正在提交', mask: true })
  // 实际提交逻辑
  try {
    const res = await storeRefundReceipt({
      ...orderAfterSalesForm.value,
      appId: import.meta.env.VITE_APPID,
    })
    uni.hideLoading()
    fetchData()
    uni.showToast({ title: res.msg || '操作成功', duration: 1000, icon: 'none' })
  } catch (err) {
    uni.hideLoading()
    uni.showToast({ title: err.data.msg || '操作失败，请重试', duration: 1000, icon: 'none' })
  }
}
const onExpressChange = () => {}
</script>

<style lang="scss" scoped>
//
:deep(.wd-picker__label) {
  padding-left: 30rpx !important;
}
:deep(.wd-radio) {
  margin: 0 !important;
  display: flex;
  gap: 30rpx;
}

:deep(.wd-cell__value) {
  text-align: left !important;
}
:deep(.wd-cell__wrapper) {
  display: flex !important;
  align-items: center !important;
}
</style>

<template>
  <wd-tabbar
    @change="onTabClick"
    fixed
    inactiveColor="#999999"
    activeColor="#999999"
    safeAreaInsetBottom
    :zIndex="10000"
    :custom-style="`${!bottom ? 'padding-bottom:30rpx' : ''}`"
    :bordered="false"
    placeholder
  >
    <!-- 首页按钮 -->
    <wd-tabbar-item
      @click="goToPage('/pages/index/index')"
      :custom-title-style="
        activeRoute === '/pages/index/index' ? 'color: #FF7D26' : 'color: #999999'
      "
    >
      <template #icon="{ active }">
        <div
          class="flex flex-col justify-center items-center"
          @click="goToPage('/pages/index/index')"
        >
          <wd-img
            height="43rpx"
            width="43rpx"
            :src="
              activeRoute === '/pages/index/index'
                ? 'https://file.shanqianqi.com/image/2025/06/13/788e5bbeca614c12895ff56b0f1746d7.png'
                : 'https://file.shanqianqi.com/image/2025/06/24/7738ff6e2d0a4b27a7dde1610884d7fd.png'
            "
          ></wd-img>
          <span class="text-#999999 text-22rpx mt-8rpx">首页</span>
        </div>
      </template>
    </wd-tabbar-item>

    <!-- 论坛按钮 -->
    <wd-tabbar-item
      @click="goToPage('/pages-category/forum/index')"
      custom-style="
        activeRoute === '/pages-category/forum/index'
          ? 'color: #FF7D26!important'
          : 'color: #999999!important'
      "
    >
      <template #icon="{ active }">
        <div
          class="flex flex-col justify-center items-center"
          @click="goToPage('/pages-category/forum/index')"
        >
          <wd-img
            height="43rpx"
            width="43rpx"
            :src="
              activeRoute === '/pages-category/forum/index'
                ? 'https://file.shanqianqi.com/image/2025/06/24/6ded52075b544249b89bff6ab10f83fb.png'
                : 'https://file.shanqianqi.com/image/2025/06/25/d4943696ea0540f9a57a913e5efcce18.png'
            "
          ></wd-img>
          <span
            class="text-#999999 text-22rpx mt-8rpx"
            :class="activeRoute === '/pages-category/forum/index' ? 'text-#FF7D26' : 'text-#999999'"
          >
            槽点
          </span>
        </div>
      </template>
    </wd-tabbar-item>

    <!-- 发布按钮 -->
    <wd-tabbar-item @click="togglePopup">
      <template #icon="{ active }">
        <div class="flex flex-col justify-center items-center" @click="togglePopup">
          <wd-img
            height="43rpx"
            width="43rpx"
            :src="
              activeRoute === '/pages-category/forum/publish/index'
                ? 'https://file.shanqianqi.com/image/2025/06/24/41ed3c4ffcb145d2af1874262cff4633.png'
                : 'https://file.shanqianqi.com/image/2025/06/25/da3aeb2d020c438aaba161b60c80560f.png'
            "
          ></wd-img>
          <span
            class="text-#999999 text-22rpx mt-8rpx"
            :class="
              activeRoute === '/pages-category/forum/publish/index'
                ? 'text-#FF7D26'
                : 'text-#999999'
            "
          >
            发布
          </span>
        </div>
      </template>
    </wd-tabbar-item>

    <!-- 作品集按钮 -->
    <wd-tabbar-item
      @click="goToPage('/pages-category/forum/portfolio/index')"
      :custom-class="
        activeRoute === '/pages-category/forum/portfolio/index' ? '!text-#FF7D26' : '!text-#999999'
      "
    >
      <template #icon="{ active }">
        <div
          class="flex flex-col justify-center items-center"
          @click="goToPage('/pages-category/forum/portfolio/index')"
        >
          <wd-img
            height="43rpx"
            width="43rpx"
            :src="
              activeRoute === '/pages-category/forum/portfolio/index'
                ? 'https://file.shanqianqi.com/image/2025/06/25/be9dc73a1bfa4a9e9cde6311733610f3.png'
                : 'https://file.shanqianqi.com/image/2025/06/24/b04cde7765f240a4b79597372376005a.png'
            "
          ></wd-img>
          <span
            class="text-22rpx mt-8rpx"
            :class="
              activeRoute === '/pages-category/forum/portfolio/index'
                ? 'text-#FF7D26'
                : 'text-#999999'
            "
          >
            作品集
          </span>
        </div>
      </template>
    </wd-tabbar-item>

    <!-- 弹出层 -->
    <wd-popup
      v-model="showPopup"
      position="bottom"
      custom-style="!rounded-10rpx !w-100% !h-10vh !z-999"
      @close="handleClose"
      closable
    >
      <view class="p-20rpx !h-500rpx">
        <div class="flex justify-center items-center mt-120rpx">
          <div class="flex justify-center items-center flex-col" @click="publish">
            <img
              src="https://file.shanqianqi.com/image/2025/06/24/9fee7a953588421aa546fa18512420f9.png"
              alt=""
              class="w-100rpx h-100rpx"
            />
            <span class="text-30rpx mt-30rpx font-500">发布内容</span>
          </div>
          <!-- <div class="flex justify-center items-center flex-col" @click="debunk">
            <img
              src="https://file.shanqianqi.com/image/2025/06/21/a9d4cc99ca2b4883ae771bc4bad52971.png"
              alt=""
              class="w-100rpx h-100rpx"
            />
            <span class="text-30rpx mt-30rpx font-500">吐个槽</span>
          </div> -->
        </div>
        <!-- <div class="flex items-center justify-center mt-110rpx" @click.stop="handleClose">
          <img
            src="https://file.shanqianqi.com/image/2025/06/25/30134347d0f44fc79fd6307eafbb9b64.png"
            alt=""
            class="w-80rpx h-80rpx"
          />
        </div> -->
      </view>
    </wd-popup>
  </wd-tabbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useUserStore } from '@/store'
import { useMessageStore } from '@/store/message'

// 获取安全区域信息
const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()
// 控制弹出层显示
const showPopup = ref(false)

// 获取当前路由
const activeRoute = computed(() => {
  const pages = getCurrentPages()
  // 如果页面栈为空，默认返回论坛路由
  return pages.length ? `/${pages[pages.length - 1].route}` : '/pages-category/forum/index'
})
// 页面跳转方法
const goToPage = (url: string) => {
  const pages = getCurrentPages()
  const currentRoute = pages[pages.length - 1].route
  if (currentRoute === url.replace(/^\//, '')) {
    return
  }
  if (url === '/pages/index/index' && currentRoute === 'pages/index') {
    return
  }

  // 使用重定向跳转而不是导航跳转
  uni.redirectTo({
    url: url,
    success: () => console.log('跳转成功'),
    fail: (err) => console.error('跳转失败:', err),
  })
}

// 切换弹出层
const togglePopup = () => {
  console.log('togglePopup 被调用')
  showPopup.value = !showPopup.value
}

// 关闭弹出层
const handleClose = () => {
  showPopup.value = false
}

// 发布内容
const publish = () => {
  uni.navigateTo({
    url: '/pages-category/forum/publish/index',
  })
  showPopup.value = false
}

// 吐槽
const debunk = () => {
  uni.navigateTo({
    url: '/pages-category/forum/debunk/index',
  })
  showPopup.value = false
}

// 初始化消息数据
onMounted(() => {
  const userStore = useUserStore()
  const messageStore = useMessageStore()
  if (messageStore.messageData.length === 0 && !!userStore.isLogined) {
    messageStore.getMessageData()
  }
})
</script>

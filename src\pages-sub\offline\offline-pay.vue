<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '收银台',
  },
}
</route>
<template>
  <view class="template-pay1 tn-safe-area-inset-bottom">
    <view class="tn-margin">
      <view
        :class="
          Number(userDetail.balance) != 0 ||
          Number(userDetail.points) != 0 ||
          Number(userDetail.redPacket) != 0
            ? 'button-vip'
            : 'button-vip1'
        "
      >
        <view class="card">
          <view
            v-if="
              Number(userDetail.balance) != 0 ||
              Number(userDetail.points) != 0 ||
              Number(userDetail.redPacket) != 0
            "
          >
            <view class="tn-color-white">我的红包</view>
            <view class="tn-color-white">
              <text class="" style="margin-left: -6rpx">￥</text>
              <text class="tn-text-bold" style="font-size: 80rpx">{{ userDetail.redPacket }}</text>
            </view>
          </view>

          <view class="tn-color-white" style="opacity: 0.6">商户名称：{{ shop_data.name }}</view>
          <view class="tn-color-white" style="opacity: 0.6">
            门店名称：{{ store_data.storeName }}
          </view>
        </view>
        <view v-if="userDetail.balance !== '0.00'" class="cardLeft">
          <!-- <view class="tn-padding-bottom-xs">
            <text class="tn-padding-right-xs" style="opacity: 0.3">善豆</text>
            ￥{{ userDetail.points }}
          </view> -->
          <view class="tn-padding-bottom-xs">
            <text class="tn-padding-right-xs" style="opacity: 0.3">余额</text>
            ￥{{ userDetail.balance }}
          </view>
        </view>
      </view>
    </view>

    <view class="inputMoney">
      <view class="tn-flex" style="border-radius: 10rpx; padding: 20rpx 30rpx">
        <text
          class="tn-flex tn-text-md tn-padding-right-xs tn-color-red--dark"
          style="margin-top: 22rpx"
        >
          消费金额:￥
        </text>
        <input
          class="inputStyle"
          v-model="payPrice"
          placeholder="请输入消费金额"
          name="payPrice"
          type="digit"
          placeholder-style="color:#AAAAAA"
          style="width: 75%"
        />
      </view>
    </view>

    <view class="payType" @click="changePayType(40)">
      <view class="justify-content-item">
        <view class="tn-text-xxl flex items-center gap-x-20rpx">
          <!-- #ifdef MP-WEIXIN  -->
          <div class="i-ri-wechat-pay-line text-#FF7D26 w-52rpx h-52rpx"></div>
          <span class="tn-padding-left-sm tn-text-lg">微信支付</span>
          <!-- #endif  -->
          <!-- #ifdef H5 -->
          <template v-if="isWx">
            <div class="i-ri-wechat-pay-line text-#FF7D26 w-52rpx h-52rpx"></div>
            <span class="tn-padding-left-sm tn-text-lg">微信支付</span>
          </template>
          <template v-else-if="isAli">
            <div class="i-ri-alipay-line text-#FF7D26 w-52rpx h-52rpx"></div>
            <!-- <text class="tn-padding-left-sm tn-text-lg">支付宝支付</text> -->
            <span class="tn-padding-left-sm tn-text-lg">支付宝支付</span>
          </template>
          <template v-else>
            <wd-img
              src="https://file.shanqianqi.com/image/2025/06/16/0c473971274a489c9abc241aac1d1339.png"
              width="52rpx"
              height="52rpx"
              mode="aspectFill"
            />
            <text class="tn-padding-left-sm tn-text-lg">拉卡拉聚合支付</text>
          </template>
          <!-- #endif -->
          <!-- <wd-icon name="money-circle" color="#CC851E" size="22px"></wd-icon>
          <text class="tn-padding-left-sm tn-text-lg">拉卡拉聚合支付</text> -->
        </view>
      </view>
      <wd-icon
        :name="payType == 40 ? 'check-outline' : 'circle1'"
        :color="payType == 40 && !disableWxPay ? '#27B93A' : '#e8e8e8'"
        size="22px"
      ></wd-icon>
    </view>

    <!-- <view class="payType" @click="changePayType(20)">
      <view class="justify-content-item">
        <view class="tn-text-xxl">
          <text class="icon iconfont icon-weixin tn-color-green--dark"></text>
          <text class="tn-padding-left-sm tn-text-lg">微信支付</text>
        </view>
      </view>
      <view :class="[payType == 20 ?'justify-content-item tn-text-xl ':'justify-content-item tn-text-xl ']">
        <view class=""
          :class="[payType == 20 ? 'icon iconfont icon-xuanze tn-color-green--dark':'icon iconfont icon-yunhangchenggong tn-color-grey--disabled']">
        </view>
      </view>
    </view> -->

    <!-- <view class="payType" @click="changePayType(30)">
      <view class="justify-content-item">
        <view class="tn-text-xxl">
          <text class="icon iconfont icon-zhifubao tn-color-blue--dark"></text>
          <text class="tn-padding-left-sm tn-text-lg">支付宝</text>
        </view>
      </view>
      <view :class="[payType == 30 ? 'justify-content-item tn-text-xl ':'justify-content-item tn-text-xl ']">
        <view class=""
          :class="[payType == 30 ? 'icon iconfont icon-xuanze tn-color-green--dark':'icon iconfont icon-yunhangchenggong tn-color-grey--disabled']">
        </view>
      </view>
    </view> -->

    <view v-if="hasBanlance" class="payType">
      <view class="justify-content-item">
        <view class="tn-text-xxl flex items-center">
          <div class="i-hugeicons-coins-01 text-#FF7D26 w-52rpx h-52rpx"></div>
          <text class="tn-padding-left-sm tn-text-lg">余额支付抵扣</text>
        </view>
      </view>
      <view
        :class="[
          payType == 10
            ? 'justify-content-item tn-text-xl tn-color-green--dark'
            : 'justify-content-item tn-text-xl tn-color-grey--disabled',
        ]"
      >
        <switch
          style="transform: scale(0.7); margin-right: -20rpx"
          :checked="balanceMoneyType"
          @change="balanceSwitch2Change"
        />
      </view>
    </view>

    <!-- <view v-if="hasShanDou" class="payType">
      <view class="justify-content-item">
        <view class="tn-text-xxl">
          <text class="icon iconfont icon-jifen tn-color-aquablue--dark"></text>
          <text class="tn-padding-left-sm tn-text-lg">善豆支付抵扣</text>
        </view>
      </view>
      <view
        :class="[
          payType == 50
            ? 'justify-content-item tn-text-xl tn-color-green--dark'
            : 'justify-content-item tn-text-xl tn-color-grey--disabled',
        ]"
      >
        <switch
          style="transform: scale(0.7); margin-right: -20rpx"
          :checked="shanDouMoneyType"
          @change="shanDouSwitch2Change"
        />
      </view>
    </view> -->

    <view v-if="hasHongBao" class="payType">
      <view class="justify-content-item">
        <view class="tn-text-xxl flex items-center">
          <wd-img
            src="https://file.shanqianqi.com/image/2025/06/16/83317643ec6d4e5ab36a97daba7e5c5d.png"
            width="52rpx"
            height="52rpx"
            mode="aspectFill"
          />
          <text class="tn-padding-left-sm tn-text-lg">红包支付抵扣</text>
        </view>
      </view>
      <view
        :class="[
          payType == 60
            ? 'justify-content-item tn-text-xl tn-color-green--dark'
            : 'justify-content-item tn-text-xl tn-color-grey--disabled',
        ]"
      >
        <switch
          style="transform: scale(0.7); margin-right: -20rpx"
          :checked="hongBaoMoneyType"
          @change="hongBaoSwitch2Change"
        />
      </view>
    </view>

    <pay-pop
      txt="支付密码"
      :pwdlength="6"
      ref="payPopRef"
      :is_rpwd="isSetPwd"
      @pwd_reset="handleSetPassword"
      @reset-change="handleResetChange"
      @pwd_e="handlePasswordConfirm"
    />

    <!-- 悬浮按钮-->
    <view class="tn-flex tn-footerfixed">
      <view class="tn-flex-1 justify-content-item tn-margin tn-text-center">
        <view class="tn-padding-xl">
          <text class="icon iconfont icon-22222_huaban1 tn-padding-right-xs tn-color-gray"></text>
          <text class="tn-color-gray">点击支付即表示你已同意</text>
          <text class="tn-color-blue--disabled">《支付协议》</text>
        </view>
        <button
          style="background-color: #3646ff"
          @click="handlePrePay"
          padding="40rpx 0"
          width="100%"
          shadow
        >
          <text class="tn-color-white">立 即 支 付</text>
        </button>
      </view>
    </view>
  </view>
</template>

<!-- <script setup>
import { isWeixin, isAlipay } from '@/utils'
</script> -->
<script>
import kfPopupDengl from '@/components/kf-popup-dengl/kf-popup-dengl.vue'
import {
  bindMobile1,
  getIndex,
  getSchemeUrl,
  offlinePay,
  storeDetail,
  supplierDetail,
  setUserPaymentPassword,
  checkUserPaymentPassword,
} from '@/service'
import { getPlatform, doLogin } from '@/utils'
import { useUserStore } from '@/store'
import { isWeixin, isAlipay, gotoPage, lklPay } from '@/utils'

const userStore = useUserStore()
export default {
  name: 'TemplatePay1',
  components: {
    kfPopupDengl,
  },
  data() {
    return {
      appId: '',
      bainPhone: false,
      form: {
        phone: '',
        password: '',
        rpassword: '',
      },
      loading: false,
      payType: 40,
      balanceMoneyType: false,
      shanDouMoneyType: false,
      hongBaoMoneyType: false,
      userId: 0,
      userInfo: {},
      sid: 0,
      storeId: 0,
      paySource: '',
      payPrice: '',
      shop_data: {},
      store_data: {},
      userDetail: {},
      checkedPay: [],
      schemeUrl: '',
      isWx: false,
      isAli: false,
      isSetPwd: false,
    }
  },
  onShow() {
    // this.isLogin()
    this.getData()
    this.payPrice = ''
    this.getSupplier()
    this.getStore()
  },
  async onLoad(e) {
    this.isSetPwd = !userStore?.userInfo?.paymentPassword

    if (e.invitationId) {
      uni.setStorageSync('invitationId', e.invitationId)
    }
    if (e.refereeId) {
      uni.setStorageSync('refereeId', e.refereeId)
    }
    if (e.sid) {
      uni.setStorageSync('shopSupplierId', e.sid)
    }
    if (e.storeId) {
      uni.setStorageSync('storeId', e.storeId)
    }
    this.appId = uni.getStorageSync('appId')
    this.paySource = getPlatform()

    // #ifdef H5

    if (isWeixin()) {
      this.isWx = true
      this.getSchemeUrl()
      // window.location.href = this.schemeUrl
    } else if (isAlipay()) {
      this.isAli = true
    }
    // #endif

    if (e?.token) {
      userStore.setToken(e?.token)
      await userStore.fetchUserCenterConfig()
      this.getData()
    }
  },
  computed: {
    disableWxPay() {
      const pay = Number(this.payPrice)
      const balance = Number(this.userDetail?.balance)
      const redPacket = Number(this.userDetail?.redPacket)

      let total = 0

      // 余额抵扣

      if (this.balanceMoneyType) {
        total += Math.min(Number.isFinite(balance) ? balance : 0, pay)
      }

      // 红包抵扣

      if (this.hongBaoMoneyType) {
        total += Number.isFinite(redPacket) ? redPacket : 0
      }

      return total >= pay
    },
    hasBanlance() {
      if (this.userDetail.balance == '' || this.userDetail.balance == 0) {
        return false
      }
      let n = this.checkedPay.indexOf(10)
      if (n == -1) {
        return false
      } else {
        return true
      }
    },
    hasShanDou() {
      if (
        this.userDetail.points == '' ||
        this.userDetail.points == 0 ||
        this.shop_data.supplierShanBeanRate == 0
      ) {
        return false
      }
      let n = this.checkedPay.indexOf(50)
      if (n == -1) {
        return false
      } else {
        return true
      }
    },
    hasHongBao() {
      if (this.userDetail.redPacket == '' || this.userDetail.redPacket == 0) {
        return false
      }
      let n = this.checkedPay.indexOf(60)
      if (n == -1) {
        return false
      } else {
        return true
      }
    },
  },
  methods: {
    async handleSetPassword(password) {
      await setUserPaymentPassword({ paymentPassword: password })
      await userStore.fetchUserCenterConfig()
      uni.showToast({ title: '设置成功', icon: 'none' })
    },
    async handlePasswordConfirm(password) {
      const { data } = await checkUserPaymentPassword({ password })
      if (data) {
        this.toPay()
      } else {
        uni.showToast({ title: '密码错误', icon: 'none' })
      }
    },
    handleResetChange() {
      if (!userStore?.userInfo?.paymentPassword) {
        return
      }
      this.isSetPwd = !this.isSetPwd
    },
    handlePrePay() {
      if (!this.payPrice) {
        uni.showToast({ title: '请输入金额', icon: 'none' })
        return
      }
      if (this.disableWxPay) {
        this.isSetPwd = !userStore?.userInfo?.paymentPassword

        this.$refs.payPopRef?.Open()

        // payPopRef.value?.Open()
      } else {
        this.toPay()
      }
    },
    isLogin() {
      if (!userStore.isLogined) {
        doLogin()
      } else {
        this.getData()
        this.payPrice = ''
      }
    },
    // 获取验证码开关状态
    async getData() {
      let that = this
      try {
        const { data } = await getIndex({
          source: getPlatform(),
        })
        if (data.user.mobile == '' || data.user.mobile == 'null') {
          this.bainPhone = true
          // // #ifdef H5
          // if (isAlipay()){
          //   that.$refs.refpopup3.open({
          //     title: '绑定手机号',
          //     cancelText: '取消',
          //     confirmText: '确定',
          //     success: (e) => {
          //       console.log('e', e)
          //       this.form.phone = e.phoneNumber
          //       this.bainMobile()
          //     },
          //     fail: () => {},
          //     complete: () => {},
          //   })
          // }
          // // #endif
        }
        this.userDetail = data.user
      } catch (error) {}
    },
    async bainMobile() {
      let self = this
      if (this.form.phone !== '') {
        let type = 'xcx'
        //#ifdef H5
        if (isWeixin()) {
          type = 'gzh'
        } else if (isAlipay()) {
          type = 'zfb'
        }
        //#endif
        //#ifdef APP
        type = 'app'
        //#endif
        // 绑定手机号

        try {
          const { data } = await bindMobile1({
            phone: this.form.phone,
            password: this.form.password,
            rpassword: this.form.rpassword,
            type: type,
          })
          console.log('data', data)
          if (data == '绑定成功') {
            self.$refs.refpopup3.close()
            this.bainPhone = false
            uni.removeStorageSync('token')
            uni.removeStorageSync('userId')
            uni.removeStorageSync('mobile')
            // 获取登录前页面
            let url = '/' + uni.getStorageSync('currentPage')
            let pageOptions = uni.getStorageSync('currentPageOptions')
            if (Object.keys(pageOptions).length > 0) {
              url += '?'
              for (let i in pageOptions) {
                url += i + '=' + pageOptions[i] + '&'
              }
              url = url.substring(0, url.length - 1)
            }
            // 执行回调函数
            gotoPage(url, 'reLaunch')
          }
        } catch (error) {
          console.log(error)
        } finally {
          uni.hideLoading()
        }
      }
    },
    /*关闭弹窗*/
    hidePopupFunc(e) {
      this.bainPhone = false
      this.$emit('close')
    },
    // 获取店铺成交数据
    async getSupplier() {
      let self = this
      this.sid = uni.getStorageSync('shopSupplierId') ? uni.getStorageSync('shopSupplierId') : 0
      if (this.sid != 0 && this.sid != undefined) {
        console.log('this.sid', this.sid)
        try {
          const { data } = await supplierDetail({
            shopSupplierId: this.sid,
            paySource: this.paySource,
          })
          self.shop_data = data.detail
          let list = []
          data.payTypes.forEach((item) => {
            list.push(item * 1)
          })
          self.checkedPay = list
          if (self.checkedPay[0] != 10) {
            self.pay_type = self.checkedPay[0] || self.checkedPay[1] || 40
          } else {
            self.pay_type = self.checkedPay[1] || self.checkedPay[0]
          }
        } catch (error) {}
      }
    },
    async getStore() {
      let self = this
      this.storeId = uni.getStorageSync('storeId') ? uni.getStorageSync('storeId') : 0
      if (this.storeId != 0 && this.storeId != undefined) {
        console.log('this.storeId', this.storeId)
        try {
          const { data } = await storeDetail({
            storeId: this.storeId,
          })
          self.store_data = data
        } catch (error) {}
      }
    },
    changePayType(e) {
      this.payType = e
    },
    balanceSwitch2Change(e) {
      this.balanceMoneyType = e.detail.value
    },
    shanDouSwitch2Change(e) {
      this.shanDouMoneyType = e.detail.value
    },
    hongBaoSwitch2Change(e) {
      this.hongBaoMoneyType = e.detail.value
    },
    async getSchemeUrl() {
      let params = {
        // path: "/pages/index/index",
        // query: "?uid=1",
      }
      const { data } = await getSchemeUrl(params)
      console.log('data', data)
      if (data) {
        this.schemeUrl = data
      }
    },
    toXcx() {
      window.location.href = this.schemeUrl
      return
    },
    async toPay() {
      let self = this
      if (self.payPrice == 0 || self.payPrice == '') {
        uni.showToast({
          icon: 'none',
          title: '请输入正确金额!',
        })
        return
      }
      self.loading = true
      uni.showLoading({
        title: '加载中',
      })
      let use_balance = self.balanceMoneyType == true ? 1 : 0
      let use_shanDou = self.shanDouMoneyType == true ? 1 : 0
      let use_hongBao = self.hongBaoMoneyType == true ? 1 : 0
      let params = {
        payType: self.payType,
        paySource: self.paySource,
        payPrice: self.payPrice,
        shopSupplierId: self.sid,
        storeId: self.storeId,
        useBalanceMoney: use_balance,
        useShanDouMoney: use_shanDou,
        useHongBaoMoney: use_hongBao,
      }
      console.log(params)
      try {
        const { data } = await offlinePay(params)
        if (data?.payment) {
          lklPay(data?.payment?.resp_data, () => {
            uni.navigateTo({ url: `/pages-sub/goods/pay-success/index1?orderId=${data.orderId}` })

            if (isWeixin()) {
              this.toXcx()
            }
          })
        } else {
          uni.navigateTo({ url: `/pages-sub/goods/pay-success/index1?orderId=${data.orderId}` })

          if (isWeixin()) {
            this.toXcx()
          }
        }
      } catch (error) {
        uni.showToast({ title: '支付失败', icon: 'none' })
      } finally {
        self.loading = false
        uni.hideLoading()
      }
    },
    paySuccess(result) {
      let self = this
      self.gotoPage('/pages/order/pay-success/pay-success', 'reLaunch')
    },
    payError(result) {
      let self = this
      self.navigateBack()
    },
  },
}
</script>

<style lang="scss" scoped>
.tn-margin {
  margin: 15px;
}

/* 卡 */
.button-vip {
  width: 100%;
  height: 300rpx;
  border-radius: 15rpx;
  position: relative;
  z-index: 1;

  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  position: relative;
  background-image: repeating-linear-gradient(45deg, #3d7eff, #31c9e8);
  color: #ffffff;

  &::after {
    content: ' ';
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    left: 0;
    bottom: 0;
    border-radius: inherit;
    opacity: 1;
    transform: scale(1, 1);
    background-size: 100% 100%;
    background-image: url(https://tnuiimage.tnkjapp.com/cool_bg_image/icon_bg4.png);
  }
}

.button-vip1 {
  width: 100%;
  height: 150rpx;
  border-radius: 15rpx;
  position: relative;
  z-index: 1;

  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  position: relative;
  background-image: repeating-linear-gradient(45deg, #3d7eff, #31c9e8);
  color: #ffffff;

  &::after {
    content: ' ';
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    left: 0;
    bottom: 0;
    border-radius: inherit;
    opacity: 1;
    transform: scale(1, 1);
    background-size: 100% 100%;
    background-image: url(https://tnuiimage.tnkjapp.com/cool_bg_image/icon_bg4.png);
  }
}

.card {
  margin-left: 15px;
  margin-top: 20px;

  .tn-color-white {
    color: #ffffff !important;
  }

  .tn-text-bold {
    font-weight: bold;
  }
}

.cardLeft {
  color: #ffffff !important;
  margin-right: 15px;
  margin-top: 26px;

  .tn-padding-bottom-xs {
    padding-bottom: 5px;
  }

  .tn-padding-right-xs {
    padding-right: 5px;
  }
}

.inputMoney {
  margin: 15px;
  padding-top: 26px;
  background-color: #f8f7f8 !important;

  .tn-flex {
    display: -webkit-flex;
    display: flex;
  }

  .tn-color-red--dark {
    color: #ba2e26 !important;
  }

  .tn-text-md {
    font-size: 14px;
  }

  .tn-padding-right-xs {
    padding-right: 5px;
  }
}

.payType {
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  padding: 15px;
  // width: 100%;
  border-bottom: 0.5px solid #f8f9fb;

  .tn-text-xxl {
    font-size: 20px;
  }

  .tn-padding-left-sm {
    padding-left: 10px;
  }

  .tn-text-lg {
    font-size: 16px;
  }

  .tn-color-orange--dark {
    color: #cc851e !important;
  }

  .tn-text-xl {
    font-size: 18px;
  }

  .tn-color-green--dark {
    color: #27b93a !important;
  }

  .tn-color-grey--disabled {
    color: #e8e8e8 !important;
  }

  .tn-color-blue--dark {
    color: #00aaff !important;
  }

  .tn-color-aquablue--dark {
    color: #2b38cc !important;
  }
}

.tn-footerfixed {
  display: flex;
  position: fixed;
  width: 100%;
  bottom: calc(31px + env(safe-area-inset-bottom));
  z-index: 10;
  box-shadow: 0 0.5px 3px rgba(0, 0, 0, 0);

  .tn-flex-1 {
    flex: 1;
  }

  .tn-margin {
    margin: 15px;
  }

  .tn-text-center {
    text-align: center;
  }

  .tn-padding-xl {
    padding: 26px;
  }

  .tn-padding-right-xs {
    padding-right: 5px;
  }

  .tn-color-gray {
    color: #aaaaaa !important;
  }

  .tn-color-blue--disabled {
    color: #9ebeff !important;
  }
}

.tn-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  -webkit-transform: translate(0px, 0px);
  transform: translate(0px, 0px);
  border-radius: 6px;
  margin: 0;
}

.tn-color-white {
  color: #ffffff !important;
}

/* 间隔线 start*/
.tn-strip-bottom-min {
  width: 100%;
  border-bottom: 1rpx solid #f8f9fb;
}

.tn-strip-bottom {
  width: 100%;
  border-bottom: 20rpx solid rgba(241, 241, 241, 0.8);
}

/* 间隔线 end*/

.inputStyle {
  cursor: auto;
  display: block;
  font-family: UICTFontTextStyleBody;
  height: 80rpx;
  min-height: 80rpx;
  overflow: hidden;
  text-overflow: clip;
  white-space: nowrap;
  font-size: 40rpx;
}

/* 父容器需设置flex布局 */
.popup-container {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}

/* 弹窗容器 */
.popup-content {
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  width: 80vw;
}

/* 表单项 */
.form-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

/* 输入框 */
.form-input {
  flex: 1;
  padding: 0 20rpx;
  font-size: 28rpx;
}

/* 绑定按钮 */
.bind-btn {
  margin-top: 40rpx;
  background: #01beff;
  color: #080808;
  border-radius: 50rpx;
}
</style>

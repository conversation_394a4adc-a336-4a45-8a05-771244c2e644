import { defineStore } from 'pinia'
import { userIndex } from '@/service'

export const useUserSettingStore = defineStore(
  'user-setting',
  () => {
    const userSetting = ref<Api.User.IndexData>()

    const fetchUserSetting = async () => {
      const { data } = await userIndex()
      setUserSettingInfo(data)
    }

    const setUserSettingInfo = (info: Api.User.IndexData) => {
      userSetting.value = info
    }

    const clearUserSettingInfo = () => {
      userSetting.value = undefined
    }

    return {
      userSetting,
      fetchUserSetting,
      setUserSettingInfo,
      clearUserSettingInfo,
    }
  },
  { persist: true },
)

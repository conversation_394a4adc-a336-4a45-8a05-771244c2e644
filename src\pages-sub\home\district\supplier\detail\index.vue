<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full flex flex-col bg-[linear-gradient(-180deg,_#394263_0%,_rgba(255,255,255,0)_100%)]"
  >
    <SimpleNavBar title="商户详情" />
    <div class="p-10px box-border flex-1 of-hidden flex flex-col gap-y-10px">
      <!-- 商铺信息 -->
      <div class="flex items-center justify-between bg-#ffffff p-10px box-border rd-15rpx">
        <div class="flex items-center gap-x-10px">
          <wd-img
            width="102rpx"
            height="102rpx"
            :src="shopInfo?.detail?.logoFilePath"
            radius="10"
            mode="aspectFit"
          ></wd-img>
          <div class="flex flex-col gap-y-5px">
            <div class="flex items-center gap-x-5px">
              <span class="text-30rpx font-bold">
                {{ shopInfo?.detail?.name }} ({{ shopInfo?.store?.storeName }})
              </span>
              <div class="i-carbon-chevron-right text-14px text-white font-bold"></div>
            </div>
            <div class="flex items-center gap-x-5px text-#666666 text-24rpx">
              <wd-rate :modelValue="getScore(shopInfo?.detail?.serverScore, 1)" readonly />
              <span>{{ shopInfo?.detail?.serverScore ?? '' }}分</span>
              <span>{{ shopInfo?.detail?.favCount ?? 0 }}人关注</span>
            </div>
          </div>
        </div>
        <wd-button @click="hanldeAddFav" custom-class="!m-unset !min-w-unset">
          {{ shopInfo?.detail?.isfollow ? '已关注' : '+关注' }}
        </wd-button>
      </div>
      <!-- 商铺简介 -->
      <div class="grid grid-cols-1 gap-y-40rpx bg-#ffffff px-10px py20px box-border rd-15rpx">
        <div class="flex items-center justify-between">
          <span class="text-28rpx text-#333333">商户简介</span>
          <span class="text-28rpx text-#999999">{{ shopInfo?.detail?.description ?? '' }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-28rpx text-#333333">所在地区</span>
          <span class="text-28rpx text-#999999">{{ shopInfo?.detail?.address ?? '' }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-28rpx text-#333333">开店时间</span>
          <span class="text-28rpx text-#999999">{{ shopInfo?.detail?.createTime ?? '' }}</span>
        </div>
      </div>
      <!-- 商铺证件 -->
      <div class="grid grid-cols-1 gap-y-40rpx bg-#ffffff px-10px py20px box-border rd-15rpx">
        <div class="flex items-center justify-between">
          <span class="text-28rpx text-#333333">证件信息</span>
          <div
            @click="() => handlePreview(shopInfo?.detail?.businessFilePath)"
            class="flex items-center gap-x-3px"
          >
            <div class="i-carbon-certificate text-32rpx text-#333333"></div>
            <div class="i-carbon-chevron-right text-32rpx text-#333333"></div>
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-28rpx text-#333333">保证金</span>
          <span class="text-28rpx text-#999999">
            {{ shopInfo?.detail?.status === 0 ? '已缴纳' : '未缴纳' }}
          </span>
        </div>
      </div>
      <wd-button
        @click="() => goSupplierIndex(shopInfo?.detail?.shopSupplierId)"
        custom-class="!min-w-unset !w-full !rd-20rpx !h92rpx"
      >
        查看全部商品
      </wd-button>
    </div>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { fetchSuppliersIndex, userFavAdd } from '@/service'
import { getScore, getVisitcode } from '@/utils'

const storeId = ref('')
const storeName = ref('')
const shopId = ref('')
onLoad((option) => {
  if (option) {
    shopId.value = option?.shopId
    storeId.value = option?.storeId
  }
})

onMounted(() => {
  fetchShopInfo()
})

const shopInfo = ref<Api.Supplier.SupplierHome>()
const fetchShopInfo = async () => {
  const { data } = await fetchSuppliersIndex({
    appId: import.meta.env.VITE_APPID,
    shopSupplierId: shopId.value,
    storeId: storeId.value,
    visitcode: getVisitcode(),
  })
  shopInfo.value = data
}

//防抖处理
let favTimeout: ReturnType<typeof setTimeout> | null = null
const hanldeAddFav = async () => {
  if (favTimeout) clearTimeout(favTimeout)
  favTimeout = setTimeout(async () => {
    try {
      await userFavAdd({
        appId: import.meta.env.VITE_APPID,
        pid: shopId.value,
        storeId: storeId.value,
        type: 10,
      })
      fetchShopInfo()
    } catch (error) {}
  }, 300)
}

const handlePreview = (url: string) => {
  if (!url) {
    uni.showToast({ title: '暂无证件', icon: 'none' })
    return
  }
  uni.previewImage({ urls: [url], indicator: 'default' })
}

const goSupplierIndex = (id: number) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${id}&storeId=${storeId.value}`,
  })
}
</script>

<style lang="scss" scoped>
//
</style>

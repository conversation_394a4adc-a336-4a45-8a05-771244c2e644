<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full bg-#f6f6f6">
    <z-paging ref="paging" v-model="dataList" :default-page-size="20" fixed @query="queryList">
      <template #top>
        <SimpleNavBar title="推广详情" />
        <div class="w-full flex flex-col items-center justify-center">
          <wd-img
            enable-preview
            src="https://file.shanqianqi.com/image/2025/06/28/a4348bc773df495f84c5e51db51a5984.png"
            width="100%"
            height="440rpx"
            mode="aspectFill"
          ></wd-img>
          <wd-datetime-picker type="year-month" v-model="date" />
        </div>
      </template>
      <div class="grid grid-cols-1 h-full bg-#ffffff rd-lt-20rpx rd-rt-20rpx">
        <div
          v-for="item in dataList"
          :key="item.shopSupplierId"
          class="px30rpx py20rpx flex items-center gap-y-10px box-border"
        >
          <div class="flex items-start justify-between gap-x-10px w-full">
            <wd-img
              width="80rpx"
              height="80rpx"
              radius="50%"
              :src="item?.avatarUrl"
              mode="aspectFill"
            ></wd-img>

            <div
              class="flex-1 flex items-center justify-between border-b border-b-solid border-b-#eeeeee pb40rpx box-border"
            >
              <div class="flex flex-col gap-y-12rpx">
                <span class="text-28rpx text-#333333">
                  {{ (item as Api.User.MyteamShopItem)?.name }}
                </span>
                <span class="text-20rpx text-#999999">
                  {{ item?.createTime ? `${item?.createTime}受邀` : '' }}
                </span>
              </div>
              <div class="flex flex-col gap-y-12rpx">
                <span class="text-20rpx text-#999999">
                  总流水：{{ (item as Api.User.MyteamShopItem)?.salesMoney }}
                </span>
                <span class="text-20rpx text-#999999">
                  总业绩：{{ (item as Api.User.MyteamShopItem)?.sysMoney }}
                </span>
                <span class="text-20rpx text-#999999">
                  抽点：{{ (item as Api.User.MyteamShopItem)?.supplierCommissionRate }}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { userTeamSupplierLists } from '@/service'
import dayjs from 'dayjs'

const date = ref<number>(Date.now())

watch(
  () => date.value,
  () => {
    paging.value?.reload()
  },
)
const totalShop = ref(0)
const paging = ref()
const dataList = ref<Api.User.MyteamShopItem[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await userTeamSupplierLists({
      pageIndex: pageNo,
      pageSize,
      level: 1,
      lastMonth: false,
      startDate: dayjs(date.value).startOf('month').format('YYYY-MM-DDTHH:mm:ss'),
      endDate: dayjs(date.value).endOf('month').format('YYYY-MM-DDTHH:mm:ss'),
    })
    totalShop.value = data?.list?.total
    paging.value.complete(data?.list?.records ?? [])
  } catch (err) {
    paging.value.complete(false)
  }
}

// const tabs = ref([
//   { label: '会员', key: 1 },
//   { label: '商户', key: 2 },
// ])

// const handleTabChange = ({ name }) => {
//   type.value = name
//   paging.value.reload()
// }

const handleShop = (userId: number) => {
  uni.navigateTo({ url: `/pages-my/my-shop/shop-apply/index?userId=${userId}` })
}
</script>

<style lang="scss" scoped>
:deep(.wd-picker__cell) {
  background: transparent !important;
  color: #333333 !important;
  font-size: 28rpx !important;
}
</style>

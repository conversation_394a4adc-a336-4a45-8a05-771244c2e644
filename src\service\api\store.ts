import { http, httpPostForm } from '@/utils/http'
export const fetchStoreOrderDetail = (params: { appId: number; orderNo: string | number }) => {
  return http.get<any>('/api/front/store/order/detail', params)
}
export const storeOrderExtract = (data: { orderId: any; appId: number; source: string }) => {
  return http.post<any>('/api/front/store/order/extract', data, data)
}

//商户售后订单
export const fetchUserStoreRefundOrderList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  shopSupplierId: any
  state: any
  type: any
}) => {
  return http.post<any>('/api/front/supplier/order/refund', data)
}
//单包裹物流详情
export const fetchUserSingleDataExpressDetail = (params: { appId: number; orderId: any }) => {
  return http.get<any>('/api/front/user/order/express', params)
}
//多包裹物流详情
export const fetchUserMultiExpressDetail = (data: {
  appId: number
  orderId: any
  expressNo: string
  expressId: string
}) => {
  return http.post<any>('/api/front/user/order/multiExpress', data)
}

//商户订单编辑物流
export const editExpressGoodsStoreOrder = (data: {
  expressId: any
  appId: number
  orderId: any
  expressNo: string
  deliveryType?: string
  orderDeliveryId?: number
}) => {
  return http.post<any>('/api/front/order/order/editExpress', data)
}

//商户订单发货
export const deliverGoodsStoreOrder = (data: {
  expressId: any
  appId: number
  orderId: any
  expressNo: string
}) => {
  return http.post<any>('/api/front/supplier/order/delivery', data)
}

export const fetchStoreRefunDetail = (params: {
  appId: number
  orderRefundId: any
  shopSupplierId: any
}) => {
  return http.get<any>('/api/front/order/refund/detail', params)
}
//商户售后审核
export const storeRefundAudit = (data: {
  isAgree: number
  appId: any
  orderRefundId: any
  refuseDesc: string
  addressId?: any
  // sendExpressId?: any
  // sendExpressNo?: any
  refundMoney?: number
}) => {
  return http.post<any>('/api/front/order/refund/audit', data)
}
//确认收货并退款
export const storeRefundReceipt = (data: {
  isAgree: number
  appId: any
  orderRefundId: any
  refuseDesc: string
  addressId?: any
  sendExpressId?: any
  sendExpressNo?: any
  refundMoney?: number
}) => {
  return http.post<any>('/api/front/order/refund/receipt', data)
}

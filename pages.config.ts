import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: '善新年',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  workers: 'static/workers',
  // 如果不需要tabBar，可以注释掉这个配置，或者直接删除
  // tabBar: {
  //   color: '#999999',
  //   selectedColor: '#018d71',
  //   height: '0px',
  //   list: [
  //     {
  //       pagePath: 'pages/index/index',
  //     },
  //     {
  //       pagePath: 'pages/message/index',
  //     },
  //     {
  //       pagePath: 'pages/cart/index',
  //     },
  //     {
  //       pagePath: 'pages/my/index',
  //     },
  //   ],
  // },
})

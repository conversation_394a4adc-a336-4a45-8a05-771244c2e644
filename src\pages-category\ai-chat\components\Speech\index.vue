<template>
  <div class="size-full grid grid-cols-1 grid-rows-[auto_1fr_auto] bg-#ffffff pb-100rpx box-border">
    <wd-navbar
      fixed
      placeholder
      safeAreaInsetTop
      :bordered="false"
      left-arrow
      @click-left="handleClickLeft"
    >
      <template #title>智能语音</template>
    </wd-navbar>
    <div class="flex items-center justify-center">
      <wd-img
        src="https://file.shanqianqi.com/image/2025/06/24/30659dcedbe44d7889903529faec0a26.png"
        width="500rpx"
        height="500rpx"
        mode="aspectFill"
      ></wd-img>
    </div>
    <div class="flex flex-col items-center gap-y-20rpx">
      <div @click="onStop" class="flex flex-col gap-y-20rpx items-center">
        <wd-img
          src="https://file.shanqianqi.com/image/2025/06/30/214afb2b895f464498eae0afe7d4a8e8.png"
          width="100rpx"
          height="100rpx"
          mode="aspectFill"
        ></wd-img>
        <span class="text-28rpx text-#5B5EE8">挂断</span>
      </div>
      <div class="voice-box flex items-center justify-center text-28rpx text-#ffffff">
        通话中：{{ formattedTime }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useSpeechTranscription } from '@/hooks/useSpeechTranscription'
import { useTextToSpeech } from '@/hooks/useTextToSpeech'
import { useTimer } from '@/hooks/useTimer'
import { addAiChat } from '@/service'
import { useUserStore } from '@/store'

const userStore = useUserStore()

const { formattedTime, pause } = useTimer({
  initialSeconds: 0,
  autoStart: true,
})

// tts配置
const URL = import.meta.env.VITE_APP_ALI_SOCKET_URL
const AKID = import.meta.env.VITE_APP_ALI_AKID
const AKKEY = import.meta.env.VITE_APP_ALI_AKKEY
const APPKEY = import.meta.env.VITE_APP_ALI_APPKEY

const { stResult, initST, startST, stopST, stStart } = useSpeechTranscription({
  AKID,
  AKKEY,
  APPKEY,
  URL,
})

const { startTTS, ttsStart, stopPlayback } = useTextToSpeech({
  AKID,
  AKKEY,
  APPKEY,
  URL,
})

const aiChatId = ref<string | number>('')

watch(
  () => stResult.value,
  async (v) => {
    try {
      uni.showLoading({ title: '思考中...', mask: true })
      const { data } = await addAiChat({
        aiChatId: aiChatId.value,
        audio_txt: v,
        userId: userStore?.userInfo?.userId,
        session_id: userStore?.userInfo?.userId,
        model: 'deepseek-r1:7b',
      })
      console.log('🚀 ~ data:', data)

      uni.hideLoading()

      aiChatId.value = data?.aiChatId

      startTTS(data?.content)
    } catch (error) {
      uni.hideLoading()
    }
  },
)

watch(
  () => ttsStart.value,
  (start) => {
    if (start) {
      stopST()
    } else {
      initST()

      setTimeout(() => {
        startST()
      }, 500)
    }
  },
)

onMounted(() => {
  initST()

  setTimeout(() => {
    startST()
  }, 500)
})

onUnmounted(() => {
  stopST()
  pause()
  stopPlayback()
})

const emits = defineEmits<{
  handleStop: []
}>()

const onStop = () => {
  emits('handleStop')
}

// 返回
const handleClickLeft = () => {
  uni.navigateBack()
}
</script>
<style scoped>
.voice-box {
  width: 590rpx;
  height: 90rpx;
  border-radius: 30rpx;
  background: #5b5ee8;
  animation: voicePulse 1.2s ease-in-out infinite;
  box-shadow:
    0rpx 0rpx 0rpx 0rpx #def2ff,
    0rpx 8rpx 20rpx #00000033;
}

@keyframes voicePulse {
  0% {
    box-shadow:
      0rpx 0rpx 0rpx 0rpx #def2ff,
      0rpx 8rpx 20rpx #00000033;
  }
  50% {
    box-shadow:
      0rpx 0rpx 0rpx 12rpx #def2ff80,
      0rpx 12rpx 24rpx #00000055;
  }
  100% {
    box-shadow:
      0rpx 0rpx 0rpx 0rpx #def2ff,
      0rpx 8rpx 20rpx #00000033;
  }
}
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full grid grid-rows-[auto_1fr]"
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
  >
    <SimpleNavBar title="商品管理" />
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="10"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <wd-tabs @change="handleTabChange" auto-line-width>
          <block v-for="item in tabs" :key="item.key">
            <wd-tab :title="`${item.label}`" :name="item.key"></wd-tab>
          </block>
        </wd-tabs>
      </template>
      <div class="w-full px20px py10px box-border grid grid-cols-1 grid-auto-rows-min gap-y-10px">
        <div
          v-for="item in dataList"
          :key="item?.productId"
          class="w-full bg-#ffffff p20rpx box-border flex items-center gap-x-20px rd-15rpx"
        >
          <wd-img width="235rpx" height="235rpx" :src="item?.productImage"></wd-img>

          <div class="flex flex-col h-full flex-1 justify-between py20px box-border">
            <span class="text-28rpx line-clamp-2">{{ item?.productName }}</span>
            <span class="text-#e2231a text-28rpx">
              ¥
              <span class="text-32rpx">{{ item?.productPrice }}</span>
            </span>
            <div class="flex items-center justify-between w-full">
              <span class="text-24rpx text-#333333">库存:{{ item?.productStock }}</span>
              <wd-button
                v-if="type === 'sell'"
                @click="() => handleProduct(item?.productId, 20)"
                custom-class="!m-unset !p-unset !line-height-unset !w-140rpx !min-w-unset !rd-10px"
              >
                下架
              </wd-button>
              <wd-button
                v-if="item?.addSource === 20"
                custom-class="!m-unset !p-unset !line-height-unset !w-140rpx !min-w-unset !rd-10px"
              >
                编辑
              </wd-button>
              <wd-button
                v-if="type === 'audit' && item?.addSource !== 20"
                disabled
                custom-class="!m-unset !p-unset !line-height-unset !w-140rpx !min-w-unset !rd-10px"
              >
                审核中
              </wd-button>
              <wd-button
                v-if="type === 'lower'"
                @click="() => handleProduct(item?.productId, 10)"
                custom-class="!m-unset !p-unset !line-height-unset !w-140rpx !min-w-unset !rd-10px"
              >
                上架
              </wd-button>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
    <!-- 
    <div class="w-full px16px box-border">
      <wd-button block @click="handleAddProduct" custom-class="!h92rpx">
        <div class="flex items-center gap-x-5px">
          <div class="i-hugeicons-plus-sign text-20px"></div>
          添加商品
        </div>
      </wd-button>
    </div> -->
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { fetchUserStoreProduct, productModify } from '@/service'

const { safeAreaInsets } = uni.getSystemInfoSync()

const shopSupplierId = ref('')
onLoad((e) => {
  shopSupplierId.value = e?.shopSupplierId ?? ''
})

const tabs = ref([
  { label: '在售', key: 'sell' },
  // { label: '审核中', key: 'audit' },
  { label: '已下架', key: 'lower' },
])

const type = ref('sell')

const paging = ref()
const dataList = ref<Api.User.UserStoreProductDataItem[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await fetchUserStoreProduct({
      pageIndex: pageNo,
      pageSize,
      shopSupplierId: shopSupplierId.value,
      type: type.value,
    })
    paging.value.complete(data?.records)
  } catch (err) {
    paging.value.complete(false)
  }
}

const handleTabChange = ({ name }) => {
  type.value = name
  nextTick(() => {
    paging.value?.reload()
  })
}

const handleAddProduct = () => {
  uni.navigateTo({ url: '/pages-my/my-shop/add-product/index' })
}

const handleProduct = async (id: number, status: number) => {
  uni.showLoading({ title: '操作中' })

  await productModify({
    productId: id,
    productStatus: status,
    shopSupplierId: shopSupplierId.value,
  })

  nextTick(() => {
    uni.hideLoading()
    paging.value?.reload()
  })
}
</script>

<style lang="scss" scoped>
//
</style>

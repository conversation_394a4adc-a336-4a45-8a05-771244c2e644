import { http, httpPostForm } from '@/utils/http'

export const fetchAddress = (data: Api.Home.AddressBody) => {
  return http.post<string>('/api/front/map/getArea', data)
}

export const fetchRecGoods = (data: Api.Home.GoodsListBody, params: { appId: number }) => {
  return http.post<Api.Home.GoodsListData>('/api/front/product/product/lists', data, params)
}
export const fetchVisitHistory = (data: {
  pageIndex: number
  pageSize: number
  userId: number
  visitType: number
}) => {
  return http.post<any>('/api/front/user/visit/index', data)
}
//通过父子类别查商品
export const fetchRecGoodsByCategoryld = (
  data: {
    pageIndex: number
    keyWord?: string
    pageSize: number
    appId: number
    type?: string
    shopSupplierId?: string
    categoryId: number
    latitude?: number
    longitude?: number
    cityName?: string
  },
  params: { appId: number },
) => {
  return http.post<Api.Home.GoodsListData>(
    '/api/front/product/product/selectProductListByCategoryId',
    data,
    params,
  )
}

export const fetchIndexGoods = (data: { source: string }) => {
  return http.get<Api.Home.AppSetting>('/api/front/index/index', data)
}

export const getProductCategoryListByPId = (data: { parentId: number | string }) => {
  return http.get<Api.Home.SupplierCategoryListItem[]>(
    '/api/front/supplier/category/getSupplierCategoryListByPid',
    data,
  )
}

export const selectUnderLineProductListByCategoryId = (data: {
  pageIndex: number
  pageSize: number
  longitude: number | string
  latitude: number | string
  cityName: string
  categoryId: number | string
  keyWord: string
}) => {
  return http.post<Api.Home.UnderLineProductListByCategoryIdData>(
    '/api/front/product/product/selectUnderLineProductListByCategoryId',
    data,
  )
}

export const fetchIndexStore = (data: {
  longitude?: number
  latitude?: number
  cityName?: string
  pageIndex: number
  pageSize: number
  categoryId?: number | string
  distance?: string
  keyWord?: string
  sortType?: number | string
}) => {
  return http.post<Api.Home.HomeStoreData>('/api/front/index/getAllUnderLineProducts', data)
}
export const fetchSelectProductListByCategoryId = (data: {
  longitude?: number
  latitude?: number
  cityName?: string
  keyWord?: string
  categoryId?: number
  appId: number
  pageIndex: number
  pageSize: number
}) => {
  return http.post<any>('/api/front/product/product/selectProductListByCategoryId', data)
}

export const fetchIndexMall = (data: {
  longitude?: number
  latitude?: number
  cityName?: string
  pageIndex: number
  pageSize: number
}) => {
  return http.post<Api.Home.HomeStoreData>('/api/front/index/getAllNoUnderLineStoreList', data)
}

export const fetchAllCitySelfStoreList = (data: Api.Store.StoreBody) => {
  return http.post<Api.Store.SelfListData>('/api/front/store/store/getAllCitySelfStoreList', data)
}

export const fetchRecGoodsDetail = (params: {
  appId: number
  productId: string
  storeId?: any
  visitcode?: string
}) => {
  return http.get<Api.Home.GoodDetail>('/api/front/product/product/detail', params)
}

export const toggleFav = (params: {
  appId: number
  productId: string
  token: string
  storeId?: any
}) => {
  return httpPostForm('/api/front/user/favorite/fav', params)
}

export const preBuy = (data: Api.Order.PreBuyBody) => {
  return http.post<Api.Order.PreBuyData>('/api/front/order/order/toBuy', data)
}

export const fetchStoreList = (data: Api.Order.StoreListBody) => {
  return httpPostForm<Api.Order.StoreListItem[]>('/api/front/store/store/lists', data)
}

export const orderBuy = (data: Api.Order.OrderBuy) => {
  return http.post<string>('/api/front/order/order/buy', data)
}

export const fetchCommentList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  productId: string
  score: number
}) => {
  return http.post<Api.Order.CommentList>('/api/front/product/comment/lists', data)
}

export const cartOrderBuy = (data: Api.Order.OrderBuy) => {
  return http.post<string>('/api/front/order/order/cart', data)
}

export const orderPay = (data: {
  appId: number
  orderId: string
  paySource: string
  useBalance: number
}) => {
  return http.post<{
    orderId: string
    orderType: number
    payType: number
    payment: {
      code: string
      msg: string
      resp_data: {
        channel_id: string
        counter_url: string
        merchant_no: string
        order_create_time: string
        order_efficient_time: string
        out_order_no: string
        pay_order_no: string
        total_amount: string
      }
      resp_time: string
    }
  }>('/api/front/user/order/pay', data)
}

export const balanceOrderPay = (data: {
  appId: number
  orderId: string
  paySource: string
  useBalance: number
}) => {
  return http.post<{
    orderId: string
    orderType: number
    payType: number
    payment: {
      code: string
      msg: string
      resp_data: {
        channel_id: string
        counter_url: string
        merchant_no: string
        order_create_time: string
        order_efficient_time: string
        out_order_no: string
        pay_order_no: string
        total_amount: string
      }
      resp_time: string
    }
  }>('/api/front/balance/plan/pay', data)
}

export const paySuccessDetail = (data: { appId: number; orderId: string; token: string }) => {
  return httpPostForm<{
    payPrice: string
    pointsBonus: number
    showOrderId: number
    showTable: boolean
  }>('/api/front/user/order/paySuccessDetail', data)
}

export const toPay = (params: { orderId: string; paySource: string; appId: number }) => {
  return http.get<{
    balance: string
    payPrice: string
    payTypes: number[]
    points: number
    redPacket: string
    totalPointsMoney: string
  }>('/api/front/user/order/toPay', params)
}

export const balanceToPay = (params: { orderId: string; paySource: string; appId: number }) => {
  return http.get<{
    balance: string
    payPrice: string
    payTypes: number[]
    points: number
    redPacket: string
    totalPointsMoney: string
  }>('/api/front/balance/plan/toPay', params)
}

export const fetchSuppliers = (data: {
  appId: number
  name?: string
  pageIndex: number
  pageSize: number
  sortType?: string
  categoryId?: number
}) => {
  return http.post<Api.Supplier.SupplierData>('/api/front/supplier/index/list', data)
}

export const fetchAllCityStoreList = (data: Api.Store.StoreBody) => {
  return http.post<Api.Store.StoreListData>('/api/front/store/store/getAllCityStoreList', data)
}

export const fetchSuppliersIndex = (data: {
  appId: number
  shopSupplierId: string
  storeId?: string
  visitcode: string
}) => {
  return http.post<Api.Supplier.SupplierHome>('/api/front/supplier/index/index', data)
}
//查询店铺身份信息
export const fetchSupplierslsHaveStore = (params: { supplierId: any; appId: number }) => {
  return http.get<any>('/api/front/store/store/supplierIsHaveStore', params)
}
export const fetchSuppliersNewIndex = (data: {
  appId: number
  shopSupplierId: string
  storeId: string
  visitcode: string
  longitude: number
  latitude: number
  userId?: any
}) => {
  return http.post<Api.Supplier.SupplierHome>('/api/front/supplier/index/newIndex', data)
}

export const userFavAdd = (data: { appId: number; pid: string; storeId: any; type: number }) => {
  return http.post('/api/front/user/favorite/add', data)
}

export const signIn = (params: { token: string; appId: number }) => {
  return http.get<Api.User.SignData>('/api/front/plus/sign/sign/index', params)
}

export const onSignIn = (params: { token: string; appId: number }) => {
  return http.get<{ coupon: unknown[]; couponNum: number; points: number }>(
    '/api/front/plus/sign/sign/add',
    params,
  )
}

export const fetchGoodsCategory = () => {
  return http.get<Api.Home.GoodCategory>('/api/front/product/category/index')
}

export const getProductCategoryListBylsVisible = (params: { appId: any; isVisible: number }) => {
  return http.get<any>('/api/front/product/category/getProductCategoryListByIsVisible', params)
}

export const fetchSupplierCategory = () => {
  return http.post<Api.Home.SupplierCategoryListItem[]>('/api/front/supplier/category/index')
}

export const fetchShopCategory = (params: { token: string; appId: number }) => {
  return http.post<Api.Home.ShopCategory>('/api/front/supplier/apply/category', params)
}

export const checkIsSelf = (data: { shopSupplierId: number; appId: number }) => {
  return http.post<boolean>('/api/front/supplier/index/supplierIsType', data)
}

export const fetchRecommendProduct = (data: { location: number }) => {
  return httpPostForm<Api.Order.RecData>('/api/front/product/product/recommendProduct', data)
}

export const fetchSupplierStoreList = () => {
  return http.get<Api.Supplier.SupplierData>('/api/front/supplier/index/storelist')
}

export const playDeviceVoice = (data: { voice: string; storeId: number }) => {
  return httpPostForm('/api/front/supplier/index/voice', data)
}

export const settingStoreDeviceId = (data: { announcerId: string | number; storeId: number }) => {
  return http.post('/api/front/store/store/updateById', data)
}

export const voiceSelectStore = (data: { datatype: string; searchWord: string }) => {
  return http.post<any[]>('/api/front/AI/search/selectStore', data)
}

export const voiceSelectProduct = (data: { datatype: string; searchWord: string }) => {
  return http.post<any[]>('/api/front/AI/search/selectProduct', data)
}

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '论坛',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f6f6f6 overflow-hidden box-border h-full">
    <z-paging
      ref="paging"
      v-model="listData"
      :default-page-size="10"
      fixed
      @query="queryList"
      :show-scrollbar="false"
      :auto-clean-list-when-reload="false"
      :auto-scroll-to-top-when-reload="false"
    >
      <template #top>
        <div class="s-bg">
          <div class="tit">
            <span class="tit-lun text-#333333 text-36rpx">槽点</span>
          </div>
          <div class="tab">
            <div
              v-for="(item, index) in tabs"
              :key="item.mainCategoryId"
              class="tab-item"
              :class="{ active: currentTab === index }"
              @click="switchTab(index)"
            >
              <span :class="{ 'active-text': currentTab === index }">{{ item.name }}</span>
              <span class="underline"></span>
            </div>
          </div>
        </div>
        <!-- Tab content -->
        <view class="tab-content">
          <view class="content-item pb-30rpx">
            <!-- 根据当前tab显示不同内容 -->
            <div class="flex items-center">
              <image class="w-40rpx h-40rpx mr-10rpx" :src="getTabIcon(currentTab)"></image>
              <span class="text-#333333 text-28rpx mr-12rpx">
                {{ tabs[currentTab]?.name || '正在加载' }}
              </span>
              <span class="text-#999999 text-24rpx">每小时更新</span>
            </div>
          </view>
        </view>
      </template>
      <div class="list pl-25rpx pr-25rpx pb-0">
        <div
          class="li mb-20rpx"
          v-for="(item, index) in listData"
          :key="index"
          @click="details(item.postId)"
        >
          <div class="li-top flex justify-between">
            <div class="li-top-l flex">
              <img :src="item.avatarUrl" class="w-70rpx h-70rpx rounded-20rpx mr-20rpx" />
              <div class="flex flex-col justify-center">
                <span class="text-24rpx mb-5rpx">{{ item.nickName || '匿名用户' }}</span>
                <div class="flex">
                  <span class="text-22rpx text-#999999 mr-20rpx">
                    关注：{{ item.follower || 0 }}
                  </span>
                  <span class="text-22rpx text-#999999 mr-20rpx">
                    粉丝：{{ item.followee || 0 }}
                  </span>
                  <span class="text-22rpx text-#999999">
                    {{ formatTime(item.createTime) }}
                  </span>
                </div>
              </div>
            </div>
            <div
              class="li-one-r flex justify-center items-center"
              @click.stop="handleFollow(item)"
              v-if="defaultPage.myId != item.userId"
              :class="item?.isFollow ? 'text-#999999 bg-#F2F2F2' : 'text-#FF7D26 bg-#ff7d2633'"
            >
              {{ item.isFollow ? '已关注' : '+关注' }}
            </div>
          </div>

          <div class="li-cont flex flex-col">
            <span class="mb-10rpx">{{ item.title }}</span>
            <span class="text-26rpx">
              {{ item.content }}
            </span>
          </div>
          <div class="li-img mt-15rpx" v-if="item.images && item.images.length > 0">
            <image
              v-for="(img, imgIndex) in item.images"
              :key="imgIndex"
              :src="img"
              class="w-160rpx h-160rpx rounded-16rpx mr-15rpx mb-15rpx"
              mode="aspectFill"
            ></image>
          </div>
          <div class="li-more mt-15rpx flex justify-around items-center">
            <div class="flex items-center">
              <image
                @click.stop="shoucang(item)"
                class="w-40rpx h-40rpx"
                :src="
                  item.isCollect
                    ? 'https://file.shanqianqi.com/image/2025/06/24/2730dbe75bcf489b86bdc8a17577489b.png'
                    : 'https://file.shanqianqi.com/image/2025/06/24/b17beaacc78944b69ec4f85d17876218.png'
                "
              ></image>
              <span class="text-#666666 ml-6rpx">{{ formatCount(item.collectCount) }}</span>
            </div>
            <div class="flex items-center">
              <image
                @click.stop="details(item.postId)"
                class="w-40rpx h-40rpx"
                src="https://file.shanqianqi.com/image/2025/06/24/35a0af52d1f444ff9b67413b8e85159f.png"
              ></image>
              <span class="text-#666666 ml-6rpx">{{ formatCount(item.commentsCount) }}</span>
            </div>
            <div class="flex items-center">
              <image
                @click.stop="dianzan(item)"
                class="w-40rpx h-40rpx"
                :src="
                  item.isLike
                    ? 'https://file.shanqianqi.com/image/2025/06/24/37ec1f17f8864efdb3d7108233fa0217.png'
                    : 'https://file.shanqianqi.com/image/2025/06/24/91d17d7940f84f96b477ee4f31c11494.png'
                "
              ></image>
              <span class="text-#666666 ml-6rpx">{{ formatCount(item.likesCount) }}</span>
            </div>
          </div>
        </div>
      </div>
      <template #bottom>
        <Tabbar></Tabbar>
      </template>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import Tabbar from '@/components/ForumTabBar/index.vue'
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store'
import {
  discourseLists,
  categoryList,
  discourseFollower,
  discourseCancelFollower,
  discourseAddLikes,
  discourseCancelLikes,
} from '@/service'
// 论坛分类类型定义
interface CategoryItem {
  mainCategoryId: number
  name: string
  parentId: number
  sort: number
  isVisible: string
  isDelete: number
  appId: number
}
// 论坛帖子/话题类型定义
interface DiscourseItem {
  postId: string | number
  title?: string
  content: string
  nickName: string
  userId: string | number
  images: string[]
  authorAvatar?: string
  createTime: string | number
  updateTime?: string | number
  likesCount?: number
  commentsCount?: number
  collectCount?: number
  follower?: number
  followee?: number
  isLike?: boolean
  imageList?: string[]
  isFollow?: boolean
  isCollect?: boolean
}

const defaultPage = reactive({
  myId: 0,
})

onShow(() => {
  paging.value?.reload()
})

// 列表数据
const listData = ref<DiscourseItem[]>([])
const tabs = ref<CategoryItem[]>([])
const currentTab = ref(0)
const paging = ref()
const scrollHeight = ref(0)
const userStore = useUserStore()
// 获取分类图标
const getTabIcon = (index: number) => {
  const icons = [
    'https://file.shanqianqi.com/image/2025/06/24/85ecda1bf59b435b9d15b6397f5e1322.png',
    'https://file.shanqianqi.com/image/2025/06/24/3f5292353a7d45cb95e5fd809dc3955c.png',
    'https://file.shanqianqi.com/image/2025/06/24/1e44880ff01f4a2191e1157cce31484e.png',
  ]
  return icons[index] || icons[0]
}

// 获取分类列表
const fetchCategoryList = async () => {
  try {
    const { data } = await categoryList({})
    if (data && Array.isArray(data)) {
      tabs.value = data
        .filter((item) => item.isVisible === 'Y' && item.isDelete === 0)
        .sort((a, b) => a.sort - b.sort)
      console.log('tabs.value', tabs.value)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    uni.showToast({
      title: '获取分类失败',
      icon: 'none',
    })
  }
}

// 格式化时间
const formatTime = (time: string | number | Date): string => {
  if (!time) return '刚刚'
  let date: Date
  if (typeof time === 'string') {
    date = new Date(time.replace(/-/g, '/'))
  } else if (typeof time === 'number') {
    date = new Date(time)
  } else {
    date = time
  }

  if (isNaN(date.getTime())) return '未知时间'
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  if (diff < 60 * 1000) return '刚刚'
  if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}分钟前`
  if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  if (diff < 48 * 60 * 60 * 1000) return '昨天'
  if (diff < 7 * 24 * 60 * 60 * 1000) return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 切换标签页
const switchTab = (index: number) => {
  currentTab.value = index
  paging.value.reload()
}

// 格式化数字显示
const formatCount = (count: number | undefined) => {
  if (!count) return '0'
  if (count >= 10000) return (count / 10000).toFixed(1) + 'w'
  return count.toString()
}

// 处理关注/取消关注操作
const handleFollow = async (item: DiscourseItem) => {
  try {
    uni.showLoading({ title: '处理中...', mask: true })
    const isFollow = !item.isFollow
    const api = isFollow ? discourseFollower : discourseCancelFollower
    await api({ userId: item.userId })
    // 更新列表中该用户的所有帖子
    listData.value = listData.value.map((post) => {
      if (post.userId === item.userId) {
        return { ...post, isFollow }
      }
      return post
    })
    uni.showToast({
      title: isFollow ? '关注成功' : '已取消关注',
      icon: 'none',
    })
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}
//收藏
const shoucang = async (item) => {
  try {
    uni.showLoading({ title: '处理中...', mask: true })

    const isCollect = !item.isCollect
    const api = isCollect ? discourseAddLikes : discourseCancelLikes

    await api({
      postId: item.postId,
      source: 1,
    })

    // 直接更新状态
    item.isCollect = isCollect
    item.collectCount = isCollect
      ? (item.collectCount || 0) + 1
      : Math.max(0, (item.collectCount || 0) - 1)

    uni.showToast({
      title: isCollect ? '收藏成功' : '已取消收藏',
      icon: 'none',
    })
  } catch (error) {
    console.error('操作失败:', error)

    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
    // 移除 paging.value.reload()
  }
}
//点赞
const dianzan = async (item) => {
  try {
    uni.showLoading({ title: '处理中...', mask: true })

    const isLike = !item.isLike
    const api = isLike ? discourseAddLikes : discourseCancelLikes

    await api({
      postId: item.postId,
      source: 2,
    })

    // 直接更新状态
    item.isLike = isLike
    item.likesCount = isLike ? (item.likesCount || 0) + 1 : Math.max(0, (item.likesCount || 0) - 1)

    uni.showToast({
      title: isLike ? '点赞成功' : '已取消点赞',
      icon: 'none',
    })
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
    // paging.value.reload()
  }
}

onMounted(() => {
  fetchCategoryList()

  const systemInfo = uni.getSystemInfoSync()
  const windowHeight = systemInfo.windowHeight
  const query = uni.createSelectorQuery()
  query.select('.s-bg').boundingClientRect()
  query.select('.tabbar').boundingClientRect()
  query.exec((res) => {
    const bgHeight = res[0]?.height || 350
    const tabbarHeight = res[1]?.height || 50
    scrollHeight.value = windowHeight - bgHeight - tabbarHeight
  })
})
const myId = ref(0)

onLoad(async (e) => {
  defaultPage.myId = userStore?.userInfo?.userId
  console.log('myId.value', myId.value)
})
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    if (tabs.value.length === 0) {
      await fetchCategoryList()
      if (tabs.value.length === 0) {
        paging.value.complete(false)
        return
      }
    }

    const currentCategory = tabs.value[currentTab.value]
    const { data } = await discourseLists({
      pageIndex: pageNo,
      pageSize: pageSize,
      mainCategoryId: currentCategory.categoryId,
      follow: currentCategory.categoryId != 4 ? false : true,
    })
    console.log('datadata', data)

    paging.value.complete(data?.records)
  } catch (err) {
    console.error('获取论坛列表失败:', err)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
    paging.value.complete(false)
  }
}

//列表详情
const details = (e) => {
  uni.navigateTo({
    url: `/pages-category/forum/details/index?postId=${e}`,
  })
}
</script>

<style lang="scss" scoped>
/* 样式保持不变 */
.content-container {
  width: 100%;
  position: relative;
  z-index: 1;
}

.scroll-content {
  width: 100%;
  box-sizing: border-box;
}
.s-bg {
  padding: 105rpx 0 0 0;
  background: linear-gradient(180deg, #ffe5d4 0%, #f6f6f6 99.04%);
  width: 100%;
  height: 350rpx;
  .tit {
    display: flex;
    align-content: center;
    justify-content: center;
    span {
      font-size: 35rpx;
      font-weight: bold;
    }
    .tit-lun {
      font-family: 'PingFang SC';
    }
  }
  .tab {
    //width: 100%;
    display: flex;
    padding: 0 60rpx;
    justify-content: space-around;
    text-align: center;
    margin-top: 35rpx;
    .active-text {
      color: #ff7d26;
      font-size: 32rpx;
    }

    .tab-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15rpx 0;

      span:first-child {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 10rpx;
        transition: color 0.3s;
      }

      .underline {
        width: 40rpx;
        height: 6rpx;
        background: transparent;
        border-radius: 2rpx;
        transition: all 0.3s;
        margin-top: 15rpx;
      }

      &.active {
        span:first-child {
          color: #ff7d26 !important;
          font-weight: bold;
        }

        .underline {
          background: #ff7d26;
        }
      }
    }
  }
}

.tab-content {
  padding: 0 25rpx;
  margin-top: -160rpx;
  .content-item {
    color: #666;
  }
}
.list {
  .li {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx 40rpx;
    .li-top-l {
    }
    .li-one-r {
      border-radius: 60rpx;
      height: 50rpx;
      line-height: 50rpx;
      font-size: 22rpx;
      width: 110rpx;
    }
    .li-cont {
      margin-top: 10rpx;
    }
    .li-img {
      display: flex;
      flex-wrap: wrap;
    }
    .li-more {
      div {
        span {
          margin-left: 10rpx;
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '充值',
  },
}
</route>

<template>
  <view v-if="!loading">
    <view class="index-head">
      <view class="index-head-top">
        <view class="f32">请选择充值金额:</view>
      </view>

      <view class="index-head-bottom">
        <view
          :class="
            active === item.planId ? 'index-head-bottom-item-active' : 'index-head-bottom-item'
          "
          @click="select(item.planId)"
          v-for="(item, index) in tableData"
          :key="item.planId"
        >
          <text class="text-40rpx">{{ item.money }}</text>
          <text class="text-22rpx">元</text>
          <view class="givemoney" v-if="item.giveMoney > 0">送{{ item.giveMoney }}元</view>
          <view class="giveShanBean" v-if="Number(item.giveShanBean) > 0">
            送{{ item.giveShanBean }}善豆
          </view>
        </view>
      </view>

      <view class="head-top-botm">注：充值金额只能消费，不能提现;</view>
    </view>

    <view class="index-body">
      <template v-if="settings.isPlan === 1">
        <view class="index-head-top">
          <view class="text-32rpx pl-20rpx pt-20rpx">可自定义充值金额:</view>
        </view>

        <view class="index-body-top" @click="selectOther">
          <view class="text-28rpx">其他金额</view>
          <input type="number" v-model.number="userMoney" placeholder="请输入您要充值的金额" />
        </view>

        <view class="gray pl-20rpx">最低充值金额：{{ settings.minMoney }}元</view>
      </template>

      <view class="index-body-bottom">
        <view class="index-body-bottom-info">
          <view class="f32 mb23rpx">充值说明</view>
          <text class="gray text-26rpx">{{ settings.describe }}</text>
        </view>
      </view>
    </view>

    <view class="btn">
      <button type="default" @click="payFunc">确认支付</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { fetchUserBalancePlanIndex, submitUserBalancePlan } from '@/service'
import { onMounted, ref, watch } from 'vue'
import { getPlatform } from '@/utils'

// 响应式状态
const tableData = ref<Api.User.UserBalancePlanLists[]>([])
const settings = ref<Api.User.UserBalancePlanSettings>({} as Api.User.UserBalancePlanSettings)
const active = ref<number>(-1)
const planId = ref<number>(0)
const userMoney = ref<number>(0)
const loading = ref<boolean>(true)

// 监听自定义金额变化
// watch(userMoney, (val) => {
//   if (val !== 0) {
//     active.value = -1
//     planId.value = 0
//   }
// })

// 生命周期 - 挂载完成
onMounted(() => {
  fetchData()
})

// 获取充值套餐数据
const fetchData = async () => {
  loading.value = true
  uni.showLoading({ title: '加载中' })

  try {
    const { data } = await fetchUserBalancePlanIndex(
      { appId: import.meta.env.VITE_APPID },
      { paySource: getPlatform() },
    )
    loading.value = false
    uni.hideLoading()
    tableData.value = data.list
    settings.value = data.settings
    console.log('settings', settings.value)
  } catch (e) {
    uni.hideLoading()
    loading.value = false
  }
}

// 选择套餐
const select = (planid: number) => {
  console.log('22')
  active.value = planid
  planId.value = planid
  let data = [...tableData.value]
  const obj = data.find((ele) => ele.planId === planid)
  userMoney.value = obj.money
}
const selectOther = () => {
  active.value = -1
  planId.value = 0
}
// 支付函数
const payFunc = async () => {
  uni.showLoading({ title: '加载中', mask: true })

  // 处理选中套餐或自定义金额
  if (active.value !== -1) {
    planId.value = active.value
  }

  // 校验输入
  // 修改 payFunc 中的判断逻辑
  if (
    (planId.value === 0 && userMoney.value === 0) ||
    userMoney.value < Number(settings.value.minMoney)
  ) {
    uni.showToast({
      icon: 'none',
      title:
        planId.value === 0 && userMoney.value === 0
          ? '请选择充值套餐或输入金额'
          : `最低充值金额不得低于${settings.value.minMoney}元`,
    })
    uni.hideLoading()
    return
  }

  console.log('planId2', planId.value)
  try {
    const { data } = await submitUserBalancePlan(
      {
        appId: import.meta.env.VITE_APPID,
        planId: planId.value,
        userMoney: userMoney.value,
      },
      {
        appId: import.meta.env.VITE_APPID,
      },
    )

    uni.hideLoading()
    uni.showToast({
      icon: 'success',
      title: '充值成功',
    })
    uni.navigateTo({
      url: `/pages-sub/goods/pay-order/index?orderId=${data.orderId}&orderType=30`,
    })
  } catch (e) {
    uni.hideLoading()
    uni.showToast({
      icon: 'error',
      title: '充值失败,请重试',
    })
  }
}
</script>

<style langg="scss" scoped>
page {
  background: #ffffff;
}

.gray {
  color: #808080;
}

.font-28 {
  font-size: 28rpx;
}

.font-36 {
  font-size: 36rpx;
}

.index {
  width: 750rpx;
}

.index-head {
  width: 750rpx;
  margin: 0 auto;
  padding: 30rpx 20rpx;
  padding-bottom: 50rpx;
  box-sizing: border-box;
  background-color: white;
  border-top: 16rpx solid #f2f2f2;
}

.index-head-top {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  flex-direction: column;
}

.head-top-botm {
  color: #999999;
  font-size: 26rpx;
  margin-top: 20rpx;
}

.index-head-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.index-head-bottom-item {
  position: relative;
  width: 220rpx;
  height: 128rpx;
  border: 1rpx solid #999999;
  background-color: #ffffff;
  color: #333333;
  text-align: center;
  border-radius: 15rpx;
  line-height: 128rpx;
  margin-top: 20rpx;
  font-size: 48rpx;
}

.index-head-bottom-item-active {
  position: relative;
  width: 220rpx;
  height: 128rpx;
  color: #323333;
  background: #ffe6e3;
  border: 1rpx solid #f6220c;
  text-align: center;
  border-radius: 12rpx;
  line-height: 128rpx;
  margin-top: 20rpx;
  font-size: 48rpx;
}

.givemoney {
  position: absolute;
  top: 0;
  left: 0;
  width: 90rpx;
  height: 33rpx;
  background-color: #f6220c;
  color: #ffffff;
  font-size: 20rpx;
  line-height: 33rpx;
  text-align: center;
  border-top-left-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
}

.giveShanBean {
  position: absolute;
  top: 0;
  right: 0;
  /* width: 90rpx; */
  height: 33rpx;
  background-color: #f6220c;
  color: #ffffff;
  font-size: 20rpx;
  line-height: 33rpx;
  text-align: center;
  border-top-left-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
}
.index-body {
  width: 750rpx;
}

.index-body-top {
  /* width: 660rpx; */
  height: 100rpx;
  padding: 0 20rpx;
  border: 1rpx solid #f7f7f7;
  border-radius: 15rpx;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4b4b4b;
  margin-bottom: 15rpx;
}

.active {
  background-color: #fde34880;
  color: #323333;
}

.index-body-top view {
  width: 20%;
}

.index-body-top input {
  width: 80%;
  text-align: right;
}

.index-body-bottom {
  width: 750rpx;
  padding: 30rpx 20rpx;
  background-color: white;
  border-top: 16rpx solid #f2f2f2;
  margin-top: 20rpx;
}

.checkbox {
  display: flex;
  align-items: center;
}

.index-body-bottom-info {
  /* margin-top: 65rpx; */
}

.index-body-bottom-info view {
  margin-top: 10rpx;
}

.btn {
  margin-top: 50rpx;
}

.btn button {
  width: 710rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  margin: 0 auto;
  color: white;
  font-size: 32rpx;
  background: linear-gradient(90deg, #ff6b6b 4%, #f6220c 100%);
}

.rule text {
  margin-left: 15rpx;
  color: #88b5d1;
}
</style>

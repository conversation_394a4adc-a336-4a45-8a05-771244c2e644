import { http, httpPostForm } from '@/utils/http'

export const fetchMyArticle = (data: {
  pageIndex: number
  pageSize: number
  mainCategoryId: string
}) => {
  return http.post<Api.Forum.MyArticleData>('/api/front/discourse/center/myArticle', data)
}

export const fetchMyComment = (data: { pageIndex: number; pageSize: number }) => {
  return http.post<Api.Forum.MyCommentData>('/api/front/discourse/center/myComment', data)
}

export const deleteComment = (data: { commentId: string,appId:string }) => {
  return http.get<boolean>('/api/front/discourse/discourse/deleteComment', data)
}
//删除评价
export const deleteProductComment = (data: { commentId: string }) => {
  return http.post<any>('/api/front/product/comment/delete', data,data)
}
export const fetchUserLikeAndCollect = (data: {
  pageIndex: number
  pageSize: number
  source: 1 | 2
}) => {
  return http.post<Api.Forum.LikeAndCollectionData>(
    '/api/front/discourse/center/likeAndCollect',
    data,
  )
}

export const fetchUserFootprints = (data: { pageIndex: number; pageSize: number }) => {
  return http.post<Api.Forum.FootprintsData>('/api/front/discourse/center/footprints', data)
}

export const clearFootprints = () => {
  return http.get<boolean>('/api/front/discourse/center/clearFootprints')
}

//点赞/收藏
export const discourseAddLikes = (data: { postId: string; source: any }) => {
  return http.get('/api/front/discourse/discourse/addLikes', data)
}
//取消点赞/收藏
export const discourseCancelLikes = (data: { postId: string; source: any }) => {
  return http.get('/api/front/discourse/discourse/cancelLikes', data)
}
//删除评论
export const discourseDeleteDiscourse = (data: { postId: string }) => {
  return http.get('/api/front/discourse/discourse/deleteDiscourse', data)
}
//个人中心
export const centerCenter = (data: {}) => {
  return http.get('/api/front/discourse/center/center', data)
}
//评论粉丝列表
export const centerMyFollow = (data: { pageIndex: number; pageSize: number; type: string }) => {
  return http.post<Api.Forum.MyFollow>('/api/front/discourse/center/myFollow', data)
}
//评论点赞
export const addCommentLike = (data: { commentId: string }) => {
  return http.get('/api/front/discourse/discourse/addCommentLike', data)
}
//取消评论点赞
export const cancelCommentLike = (data: { commentId: string }) => {
  return http.get('/api/front/discourse/discourse/cancelCommentLike', data)
}

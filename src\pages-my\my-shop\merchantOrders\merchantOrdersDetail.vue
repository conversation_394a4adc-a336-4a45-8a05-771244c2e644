<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '订单详情',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 pb90rpx">
    <!--详情状态-->
    <view class="p30rpx bg-#ff5704 text-white flex items-center gap20rpx">
      <view class="">
        <span
          v-if="detail.stateText == '已付款,待发货'"
          class="i-carbon-checkbox-indeterminate"
        ></span>
        <span v-if="detail.stateText == '待付款'" class="i-carbon-checkbox-indeterminate"></span>
        <span
          v-if="detail.stateText == '已发货,待收货'"
          class="i-carbon-checkbox-indeterminate"
        ></span>
        <span v-if="detail.stateText == '已完成'" class="i-carbon-checkbox-indeterminate"></span>
        <span v-if="detail.stateText == '已取消'" class="i-carbon-checkbox-indeterminate"></span>
      </view>
      <view class="state-cont flex-1 flex flex-col gap10rpx">
        <view class="text-34rpx">
          <text class="">{{ detail.stateText }}</text>
        </view>
        <view class="text-24rpx" v-if="detail.payStatus == 10">
          应付金额：¥ {{ detail.payPrice }}
        </view>
        <view class="text-24rpx px4rpx py8rpx" v-if="detail.payEndTimeText">
          <text style="border-radius: 4rpx; background: rgba(0, 0, 0, 0.4)">
            剩{{ detail.payEndTimeText }}自动关闭
          </text>
        </view>
      </view>
      <view class="dot-bg"></view>
    </view>
    <!--物流地址-->
    <view
      class="flex items-center gap20rpx mx20rpx mt20rpx p30rpx rounded-30rpx bg-white"
      v-if="detail.deliveryType == 10"
    >
      <view class="icon-box">
        <image
          style="width: 42rpx; height: 42rpx"
          src="https://file.shanqianqi.com/image/2025/06/19/4316db5a07b749469d722df67a44faa5.png"
          mode=""
        ></image>
      </view>
      <view class="flex-1 text-30rpx">
        <view class="express-text text-32rpx">
          {{ detail.address.name }}
          <text class="text-26rpx">{{ detail.address.phone }}</text>
        </view>
        <view class="gray3 text-26rpx pt10rpx">
          {{ detail.address.region.province }}{{ detail.address.region.city
          }}{{ detail.address.region.region }}{{ detail.address.detail }}
        </view>
      </view>
      <view class="i-carbon-chevron-right"></view>
    </view>
    <!-- 上门自提：自提门店 -->
    <view
      class="flex gap20rpx mx20rpx mt20rpx p30rpx rounded-30rpx bg-white"
      v-if="detail.deliveryType == 20"
    >
      <view class="text-#ccc">
        <image
          style="width: 42rpx; height: 42rpx"
          src="https://file.shanqianqi.com/image/2025/06/19/4316db5a07b749469d722df67a44faa5.png"
          mode=""
        ></image>
        自提门店
      </view>
      <view class="flex-1 text-30rpx">
        <view class="express-text text-26rpx">
          <view class="flex gap20rpx">
            <text>{{ detail.extractStore.storeName }}</text>
            <text>{{ detail.extractStore.phone }}</text>
          </view>
          <view class="text-26rpx pt10rpx">
            {{ detail.extractStore.province }} {{ detail.extractStore.city }}
            {{ detail.extractStore.region }}
            {{ detail.extractStore.address }}
          </view>
        </view>
      </view>
    </view>
    <!--购物列表-->
    <view class="shops group bg-white mx20rpx mt20rpx px30rpx py10rpx rounded-30rpx">
      <!-- <view
        class="h90rpx flex items-center"
        style="border-bottom: 1rpx solid #eeeeee"
        @tap="gotoPage('/pages/shop/shop?shopSupplierId=' + detail.shopSupplierId)"
      >
        <view
          class="left text-32rpx gap10rpx flex"
        >
          @click="goSupplierIndex(detail.shopSupplierId, detail.extractStoreId)"
          <text class="i-carbon-store"></text>
          <text class="min-name">{{ detail.supplierName }}</text>
          {{ detail.extractStore?.storeName ? `(${detail.extractStore?.storeName})` : '' }}

          <text class="i-carbon-chevron-right"></text>
        </view>
      </view> -->

      <view class="list">
        <view
          class=""
          v-for="(item, index) in detail.product"
          :key="index"
          @click="gotoProductDetail(item.productId, detail.extractStoreId)"
        >
          <view class="flex items-center gap30rpx py20rpx">
            <view class="w160rpx h160rpx">
              <image :src="item.productImage" mode="aspectFit"></image>
            </view>
            <view class="flex-1">
              <view class="p-0-30 text-ellipsis-2 gray3 f30">{{ item.productName }}</view>
              <view class="pt10rpx text-24rpx">{{ item.productAttr }}</view>
              <view class="pt10rpx flex justify-between">
                <template v-if="item.isUserGrade != 1">
                  <view class="price text-22rpx text-red">
                    ¥
                    <text class="text-40rpx">{{ item.productPrice }}</text>
                  </view>
                </template>
                <template v-else>
                  <view class="text-red text-22rpx">
                    ¥
                    <text class="text-40rpx">{{ item.productPrice }}</text>
                  </view>
                </template>
                <view class="text-24rpx gray9">x{{ item.totalNum }}</view>
              </view>
              <view class="mt10 tr f28" v-if="item.isUserGrade == 1">
                <text class="red">会员折扣价：</text>
                <text class="red">{{ item.gradeProductPrice }}</text>
              </view>
            </view>
          </view>
          <view v-if="item.tableId > 0 && item.tableRecordId > 0" class="supplement-box">
            <view
              class="p20rpx"
              v-for="(table_item, table_index) in item.tableData"
              :key="table_index"
            >
              <view class="d-s-s" v-if="table_item.type == 'image'">
                <text class="gray6 mr15">{{ table_item.name }}:</text>
                <image
                  style="width: 80rpx; height: 80rpx"
                  :src="table_item.value"
                  mode="aspectFill"
                ></image>
              </view>
              <template v-else>
                <text class="gray6 mr15">{{ table_item.name }}:</text>
                <text>{{ table_item.value }}</text>
              </template>
            </view>
          </view>
          <!-- 申请售后 -->
          <view class="pt10rpx d-e-c">
            <view class="flex justify-end items-center gap10rpx">
              <text v-if="item.refund" class="text-24rpx text-red">
                {{ item.refundState }}
              </text>
              <!-- <view
                v-if="detail.isAllowRefund && item.refund === false"
                class="flex justify-end items-center gap10rpx"
                @click="onApplyRefund(item.orderProductId)"
              >
                <button class="rounded-30rpx text-black text-28rpx m0">申请售后</button>
              </view> -->
              <!--售后拒绝后允许重复申请    refundStatus 售后单状态(0进行中 10已拒绝 20已完成 30已取消)-->
              <!-- <view
                class="flex justify-end items-center gap10rpx"
                v-if="detail.isAllowRefund && item.refund && item.refundStatus === 10"
                @click="onApplyRefund(item.orderProductId)"
              >
                <button class="rounded-30rpx text-black text-28rpx m0">再次申请售后</button>
              </view> -->
            </view>
          </view>
          <!-- 补充表单 -->
          <!-- <view class="" v-if="item.tableId > 0 && item.tableRecordId == 0">
						<view class="m-top20 dis-flex flex-x-end">
							<view @click="onSaveTable(item.tableId, item.orderProductId)"><button
									class="theme-btn">补充信息</button></view>
						</view>
					</view> -->
        </view>
      </view>
    </view>

    <!--订单信息-->
    <view class="bg-white mx20rpx mt20rpx px30rpx py10rpx rounded-30rpx text-26rpx">
      <view class="py20rpx">
        <text class="">订单编号：</text>
        <text selectable>{{ detail.orderNo }}</text>
        <text class="text-blue ml20rpx" @click="copyTexts(detail.orderNo)">复制</text>
      </view>
      <view class="py20rpx" v-if="detail.createTime">
        <text class="">下单时间：</text>
        <text>{{ detail.createTime }}</text>
      </view>
      <view class="py20rpx" v-if="detail.payTime">
        <text class="">支付时间：</text>
        <text>{{ detail.payTime }}</text>
      </view>
      <view class="py20rpx" v-if="detail.deliveryTime">
        <text class="">发货时间：</text>
        <text>{{ detail.deliveryTime }}</text>
      </view>
      <view class="py20rpx" v-if="detail.receiptTime">
        <text class="">完成时间：</text>
        <text>{{ detail.receiptTime }}</text>
      </view>
      <view class="py20rpx">
        <text class="">支付方式：</text>
        <text>{{ detail.payTypeText }}</text>
      </view>

      <view class="py20rpx">
        <text class="">配送方式：</text>
        <text>{{ detail.deliveryTypeText }}</text>
      </view>
      <view
        class="py20rpx"
        v-if="detail.deliveryType == 30 && detail.orderStatus == 30 && detail.virtualContent != ''"
      >
        <text class="">发货信息：</text>
        <text class="copys" @click="copyTexts(detail.virtualContent)">
          {{ detail.virtualContent }}
        </text>
      </view>
      <view class="py20rpx">
        <text class="">备注：</text>
        <text>{{ detail.buyerRemark }}</text>
      </view>
      <view class="py20rpx" v-if="detail.orderStatus == 20 && detail.cancelRemark != ''">
        <text class="">商家备注：</text>
        <text>{{ detail.cancelRemark }}</text>
      </view>
      <view
        v-if="service_open && detail.serviceUserId != 0"
        style="border-top: 1rpx solid #cacaca"
        class="py20rpx gap10rpx flex justify-center items-center"
        @click="tochat"
      >
        <text class="i-carbon-chat"></text>
        <text class="">联系卖家</text>
      </view>
    </view>
    <!-- 运费 实付 -->
    <view class="bg-white mx20rpx mt20rpx px30rpx py10rpx rounded-30rpx text-26rpx">
      <view class="py20rpx text-34rpx font-bold flex justify-end" @click="handleShow">
        实付金额：
        <text class="text-red" v-if="detail.orderSource == 80">
          <template
            v-if="detail.advance && detail.advance.payStatus == 20 && detail.payStatus == 10"
          >
            ¥{{ detail.advance && detail.advance.payPrice }}
          </template>
          <template v-else-if="detail.payStatus == 20">
            ¥{{ parseFloat(detail.advance.payPrice) + parseFloat(detail.payPrice) }}
          </template>
          <template v-else-if="detail.payStatus == 10">¥0</template>
        </text>
        <text class="text-red" v-else>¥ {{ detail.payPrice }}</text>
        <view class="i-carbon-chevron-right"></view>
      </view>
    </view>
    <!-- 商家发货 -->
    <view
      class="py30rpx px10rpx m20rpx rounded-30rpx bg-white box-border"
      v-if="
        detail.payStatus == 20 &&
        detail.deliveryType == 10 &&
        detail.orderStatus == 10 &&
        detail.deliveryStatus == 10
      "
    >
      <view class="text-34rpx fw-bold text-center py20rpx">去发货</view>
      <!-- 下拉框 -->
      <view class="table-item">
        <view class="">
          <wd-picker
            :columns="expressList"
            label="物流公司"
            label-width="33%"
            v-model="expressForm.expressId"
            value-key="expressId"
            label-key="expressName"
            @confirm="handleConfirm"
          />
        </view>
      </view>
      <wd-cell class="w-full" custom-class="w-full" title="物流单号" title-style="width: 33%;">
        <template #default>
          <wd-input
            class="flex-1"
            custom-class="w-full"
            v-model="expressForm.expressNo"
            type="text"
            placeholder="请输入物流单号"
          />
        </template>
      </wd-cell>

      <view class="flex justify-center items-center p20rpx">
        <button class="flex-1 m0 bg-#28a5ff rounded-48rpx text-28rpx text-white" @click="sendPro">
          确认发货
        </button>
      </view>
    </view>

    <!-- 商家修改物流 -->
    <view
      class="py30rpx px10rpx m20rpx rounded-30rpx bg-white box-border"
      v-if="
        detail.payStatus == 20 &&
        detail.deliveryType == 10 &&
        detail.orderStatus == 10 &&
        detail.deliveryStatus != 10
      "
    >
      <view class="text-34rpx fw-bold text-center py20rpx">修改物流</view>
      <!-- 下拉框 -->
      <view class="table-item">
        <view class="">
          <wd-picker
            label-width="33%"
            :columns="expressList"
            label="物流公司"
            v-model="expressForm.expressId"
            value-key="expressId"
            label-key="expressName"
            @confirm="handleConfirm"
          />
        </view>
      </view>
      <wd-cell title="物流单号">
        <template #default>
          <wd-input v-model="expressForm.expressNo" type="text" placeholder="请输入物流单号" />
        </template>
      </wd-cell>
      <view class="flex justify-center items-center p20rpx">
        <button class="m0 bg-#28a5ff text-28rpx text-white" @click="editExpress">确认修改</button>
      </view>
    </view>
    <template v-if="detail.orderSource != 70">
      <template v-if="detail.orderSource != 20 && detail.orderSource != 30">
        <view class="foot-btns">
          <!-- 取消订单 -->
          <!-- 拼团订单 -->
          <!-- 未支付取消订单 -->
          <!-- <template v-if="detail.orderSource == 80">
            <template v-if="detail.orderStatus == 10 && detail.advance.payStatus == 10">
              <button
                class="rounded-30rpx text-#ff5704 border-solid border-#ff5704 text-28rpx m0"
                @click="cancelAdvance(detail.advance.orderAdvanceId)"
              >
                申请取消
              </button>
            </template>
          </template> -->
          <!-- 普通订单 -->
          <!-- <template>
            <button
              class="rounded-30rpx text-#ff5704 border-2rpx border-solid border-#ff5704 text-28rpx m0"
              v-if="detail.payStatus == 10 && detail.orderStatus != 20"
              @click="cancelOrder(detail)"
            >
              取消订单
            </button>
          </template>
          <block v-if="detail.orderStatus != 21">
            <block v-if="detail.orderStatus == 20">已取消</block>
            <block v-else-if="detail.payStatus == 20 && detail.deliveryStatus == 10">
              <button
                @click="cancelOrder(detail)"
                class="rounded-30rpx border-2rpx text-#ff5704 border-solid border-#ff5704 text-28rpx m0"
              >
                申请取消
              </button>
            </block>
          </block>
          <text v-else class="count f28 gray9">取消申请中</text> -->
          <text v-if="detail.orderStatus === 21" class="count f28 gray9">取消申请中</text>
          <!-- <block v-if="detail.payStatus == 10 && detail.orderStatus != 20">
            <button
              @click="onPayOrder(detail.orderId)"
              v-if="detail.payStatus == 10"
              class="rounded-30rpx text-white bg-#ff5704 text-28rpx m0"
            >
              去支付
            </button>
          </block> -->
          <!-- 确认收货 -->
          <!-- 查看物流 -->
          <!-- <button
            class="rounded-30rpx text-#ff5704 border-2rpx border-solid border-#ff5704 text-28rpx m0"
            v-if="
              (detail.deliveryType == 40 || detail.deliveryType == 10) &&
              detail.deliveryStatus != 10
            "
            @click="
              gotoPage(
                `/pages-my/orederExpressDetail/orederExpressDetail?orderId=${orderId}&deliveryType=40`,
              )
            "
          >
            查看物流
          </button> -->
        </view>
      </template>
    </template>
    <!-- 付款明细 -->
    <wd-popup
      v-model="showPaymentDetails"
      position="bottom"
      custom-style="min-height: 200px;max-height:600px;"
      custom-class="p30rpx rounded-30rpx flex flex-col justify-between "
      :safe-area-inset-bottom="true"
      @close="handleClose"
    >
      <view class="">
        <view class="py20rpx" v-if="detail.orderSource == 80">
          <text class="">定金：</text>
          <text>￥{{ detail.advance.payPrice }}</text>
        </view>

        <view class="py20rpx flex justify-between">
          <text class="">运费</text>
          <text>¥ {{ detail.expressPrice }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">善豆</text>
          <text>¥ {{ detail.payShanBean }}</text>
        </view>
        <view v-if="detail.balance != '0.00'" class="py20rpx flex justify-between">
          <text class="">余额实付</text>
          <text>¥ {{ detail.balance }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">银行卡实付</text>
          <text>¥ {{ detail.onlineMoney }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">红包实付</text>
          <text>¥ {{ detail.payRedPocket }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">实付</text>
          <text>¥ {{ detail.payPrice }}</text>
        </view>
        <view class="py20rpx" v-if="detail.payPrice && detail.orderSource == 80">
          <text class="">尾款：</text>
          <text>￥{{ detail.payPrice }}</text>
        </view>
        <view class="py20rpx" v-if="detail.advance && detail.advance.reduceMoney > 0">
          <text class="">尾款立减</text>
          <text>-¥ {{ detail.advance.reduceMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.orderSource == 20">
          <text class="">扣除{{ pointsName }}数：</text>
          <text>-{{ detail.pointsNum }}{{ pointsName }}</text>
        </view>
        <!-- <view class="py20rpx">
				<text class="">订单总额</text>
				<text>¥ {{ detail.totalPrice }}</text>
			</view> -->
        <view class="py20rpx" v-if="detail.updatePrice != '0.00'">
          <text class="gray9">订单差价</text>
          <text>¥ {{ detail.updatePrice }}</text>
        </view>
        <view class="py20rpx" v-if="detail.productReduceMoney > 0">
          <text class="">商品立减</text>
          <text>-¥ {{ detail.productReduceMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.fullreduceMoney > 0">
          <text class="">满减</text>
          <text>-¥ {{ detail.fullreduceMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.pointsMoney > 0">
          <text class="">{{ pointsName }}抵扣</text>
          <text>-¥ {{ detail.pointsMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.couponMoney > 0">
          <text class="">商户优惠券</text>
          <text class="theme-price">- ¥ {{ detail.couponMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.couponMoneySys > 0">
          <text class="">平台优惠券</text>
          <text class="theme-price">- ¥ {{ detail.couponMoneySys }}</text>
        </view>
      </view>
      <view class="flex justify-end">
        <wd-button @click="handleClose" custom-class="!mx-25rpx">确认</wd-button>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import {
  UserOrderDetail,
  CancelUserOrder,
  ReceiptUserOrder,
  deliverGoodsStoreOrder,
  editExpressGoodsStoreOrder,
} from '@/service'

//
let detail = ref<Api.User.UserOrderDetailDetailList>(null)
let orderId = ref(0)
let expressForm = ref({
  expressId: '',
  expressNo: '',
  orderId: '',
})
let expressList = ref([])
let service_open = ref(false)
let pointsName = ref('')
let isSendWx = ref(null)
let showPaymentDetails = ref(false)
onLoad((e) => {
  console.log('e.dataType', e)
  orderId.value = e.orderId
})
onMounted(() => {
  fetchData()
})

const fetchData = async () => {
  uni.showLoading({
    title: '刷新中',
  })
  try {
    let { data } = await UserOrderDetail({
      orderId: orderId.value,
    })
    detail.value = data.detail
    expressForm.value = {
      orderId: data.detail.orderId,
      expressId: data.detail?.express?.expressId,
      expressNo: data.detail?.expressNo,
    }
    expressList.value = data.expressList

    pointsName.value = data.pointsName
    isSendWx.value = data.isSendWx
    service_open.value = data.serviceOpen
    console.log('data1', data)
    console.log('data', detail, detail.value.stateText)
    uni.hideLoading()
  } catch (error) {
    uni.hideLoading()
  }
}
//确认物流公司
const handleConfirm = (value) => {
  console.log('value', value)
}
//去发货
const sendPro = () => {
  // 表单校验逻辑
  if (!expressForm.value.expressId) {
    uni.showToast({ title: '请选择物流公司', duration: 2000, icon: 'none' })
    return
  }
  if (!expressForm.value.expressNo) {
    uni.showToast({ title: '请输入物流单号', duration: 2000, icon: 'none' })
    return
  }
  uni.showModal({
    title: '提示',
    content: '您确定要发货吗?',
    success: (res) => {
      if (!res.confirm) {
        return // 用户取消操作，退出函数
      }
      uni.showLoading({
        title: '正在处理',
      })
      // 发送 API 请求以撤回取消订单
      deliverGoodsStoreOrder({
        appId: import.meta.env.VITE_APPID,
        expressId: expressForm.value.expressId,
        expressNo: expressForm.value.expressNo,
        orderId: expressForm.value.orderId,
      })
        .then((res) => {
          uni.hideLoading()
          uni.showToast({ title: res.msg || '操作成功', duration: 2000, icon: 'success' })
          fetchData() // 刷新订单
        })
        .catch((error) => {
          uni.hideLoading()
          uni.showToast({ title: error.data.msg || '操作失败', duration: 2000, icon: 'none' })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },
  })
}
//编辑物流
const editExpress = () => {
  // 表单校验逻辑
  if (!expressForm.value.expressId) {
    uni.showToast({ title: '请选择物流公司', duration: 2000, icon: 'none' })
    return
  }
  if (!expressForm.value.expressNo) {
    uni.showToast({ title: '请输入物流单号', duration: 2000, icon: 'none' })
    return
  }
  uni.showModal({
    title: '提示',
    content: '您确定要修改物流吗?',
    success: (res) => {
      if (!res.confirm) {
        return // 用户取消操作，退出函数
      }
      uni.showLoading({
        title: '正在处理',
      })
      // 发送 API 请求以撤回取消订单
      editExpressGoodsStoreOrder({
        appId: import.meta.env.VITE_APPID,
        expressId: expressForm.value.expressId,
        expressNo: expressForm.value.expressNo,
        orderId: expressForm.value.orderId,
      })
        .then((res) => {
          fetchData() // 刷新订单
          uni.showToast({ title: res.msg || '操作成功', duration: 1000, icon: 'success' })
        })
        .catch((error) => {
          uni.showToast({ title: error.data.msg || '操作失败', duration: 1000, icon: 'none' })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },
  })
}
//跳转商品详情
const gotoProductDetail = (productId: number, storeId: any) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${productId}&storeId=${storeId}` })
}
//关闭付款明细模态框
const handleClose = () => {
  showPaymentDetails.value = false
  console.log('handleClose', showPaymentDetails.value)
}
const handleShow = () => {
  showPaymentDetails.value = true
}

//跳转店铺详情
const goSupplierIndex = (id: number, extractStoreId: any) => {
  //extractStoreId门店storeid
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${id}&storeId=${extractStoreId}`,
  })
}
const wxOrder = (y) => {}
// 确认收货
const orderReceipt = (orderId: number) => {
  uni.showModal({
    title: '提示',
    content: '你确定要收货吗？',
    success: (res) => {
      if (!res.confirm) {
        return // 用户取消操作，退出函数
      }
      // 发送 API 请求以撤回取消订单
      ReceiptUserOrder({
        appId: import.meta.env.VITE_APPID,
        orderId: orderId,
      })
        .then((res) => {
          uni.hideLoading()
          uni.showToast({ title: res.msg || '操作成功', icon: 'success' })
          fetchData() // 刷新订单列表
        })
        .catch((error) => {
          uni.hideLoading()
          uni.showToast({ title: '操作失败', icon: 'none' })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },
  })
}
//申请取消订单
const cancelOrder = async (e) => {
  let content = '您确定要取消吗?'
  if (e.isMore === 1) {
    content = '取消订单后，促销优惠将一并取消，是否继续？'
  }

  const result = await uni.showModal({
    title: '提示',
    content: content,
  })

  if (result.confirm) {
    uni.showLoading({
      title: '正在处理',
    })

    try {
      const res = await CancelUserOrder({
        orderId: e.orderId,
        appId: import.meta.env.VITE_APPID,
      })
      uni.hideLoading()
      uni.showToast({
        title: res.msg || '操作成功',
        duration: 2000,
        icon: 'success',
      })
      fetchData()
    } catch (error) {
      uni.hideLoading()
      console.error('取消订单失败:', error)
      uni.showToast({
        title: '操作失败',
        duration: 2000,
        icon: 'none',
      })
    }
  }
}
//订单付款
const onPayOrder = (orderId: number) => {
  uni.navigateTo({
    url: `/pages-sub/goods/pay-order/index?orderId=${orderId}`,
  })
}
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}

const yulan = (url: string) => {
  uni.navigateTo({ url })
}
const onApplyRefund = (orderProductId: number) => {
  gotoPage(`/pages-my/refundApply/refundApply?orderProductId=${orderProductId}`)
}
const onSaveTable = (tableId: any, orderProductId: any) => {}
const copyTexts = (orderNo: any) => {
  uni.setClipboardData({
    data: orderNo, // 必填，需要复制的文本
    success: function () {
      // 复制成功的回调
      console.log('复制成功')
    },
    fail: function () {
      // 复制失败的回调
      console.log('复制失败')
    },
    complete: function () {
      // 无论成功或失败都会执行
      console.log('操作完成')
    },
  })
}
const tochat = () => {}
</script>

<style lang="scss" scoped>
:deep(.wd-cell__wrapper) {
  display: flex !important;
  align-items: center !important;
}
:deep(.wd-cell__left) {
  width: 33% !important;
  flex: none !important;
  margin: 0 !important;
}
:deep(.wd-picker__label) {
  margin: 0 !important;
}
//
.foot-btns {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  height: 90rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background: white;
  gap: 10rpx;
}
.foot-btns button {
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
}
</style>

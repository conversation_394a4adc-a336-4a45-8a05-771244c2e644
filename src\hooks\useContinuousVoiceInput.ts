export function useContinuousVoiceInput() {
  const plugin = requirePlugin('WechatSI')
  const manager = plugin.getRecordRecognitionManager()

  const voiceState = reactive({
    isRecording: false,
    recordLoading: false,
  })

  // 连续空识别计数器
  let silentCount = 0
  const SILENT_LIMIT = 5 // 连续几次空识别后认为用户没说话

  // 初始化语音识别监听器
  const initVoiceListeners = (
    onRecognize: (res: {
      tempFilePath: string
      duration: number
      fileSize: number
      result: string
    }) => void,
  ) => {
    manager.onStart = () => {
      voiceState.recordLoading = false
      silentCount = 0
    }

    manager.onRecognize = (res) => {
      console.log('🚀 ~ useContinuousVoiceInput ~ res:', res)
      const text = res.result?.trim()
      if (!text) {
        silentCount++
        if (silentCount >= SILENT_LIMIT) {
          console.warn('检测到用户长时间无语音输入，自动停止识别')
          stopVoice()
        }
      } else {
        silentCount = 0 // 有识别结果就清空计数
      }
    }

    manager.onStop = (res) => {
      voiceState.recordLoading = false

      const text = res?.result?.trim()
      if (text) {
        onRecognize(res)
      }

      // 继续下一轮识别
      if (voiceState.isRecording) {
        setTimeout(() => {
          if (voiceState.isRecording) {
            manager.start({ lang: 'zh_CN' })
          }
        }, 300)
      }
    }

    manager.onError = (err) => {
      console.error('语音识别出错:', err)
      voiceState.isRecording = false
      voiceState.recordLoading = false
    }
  }

  const startContinuousVoice = () => {
    if (voiceState.isRecording) return

    voiceState.isRecording = true
    voiceState.recordLoading = true
    silentCount = 0
    manager.start({ lang: 'zh_CN' })
  }

  const stopVoice = () => {
    if (!voiceState.isRecording) return

    voiceState.isRecording = false
    voiceState.recordLoading = false
    manager.stop()
  }

  return {
    voiceState,
    initVoiceListeners,
    startContinuousVoice,
    stopVoice,
  }
}

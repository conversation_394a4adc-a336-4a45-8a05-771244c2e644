<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '提交评价',
  },
}
</route>

<template>
  <view class="evaluate p30rpx bg-white box-border" v-if="!loading">
    <form @submit="formSubmit" @reset="formReset">
      <view class="evaluate-item" v-for="(item, index) in tableData" :key="index">
        <view class="product flex items-center gap20rpx">
          <view class="cover">
            <image class="w-160rpx h-160rpx" :src="item.productImage" mode="aspectFit"></image>
          </view>
          <view class="flex flex-col justify-center gap10rpx text-28rpx">
            <view class="">{{ item.productName }}</view>
            <view class="text-red">
              ¥
              <text class="text-40rpx">{{ item.productPrice }}</text>
            </view>
          </view>
        </view>
        <view class="flex flex-col gap20rpx py30rpx text-24rpx text-#000000">
          <view class="flex items-center gap20rpx">
            描述相符：
            <wd-rate :num="5" custom-class="flex-1" v-model="describe_score" @change="choosees" />
          </view>
          <view class="flex items-center gap20rpx">
            服务态度：
            <wd-rate :num="5" custom-class="flex-1" v-model="server_score" @change="choosees" />
          </view>
          <view class="flex items-center gap20rpx">
            配送服务：
            <wd-rate :num="5" custom-class="flex-1" v-model="express_score" @change="choosees" />
          </view>
        </view>
        <view class="grade flex items-center justify-between">
          <view
            :class="item.score == 10 ? 'd-c-c active' : 'd-c-c'"
            @click="gradeFunc(item, 10, index)"
          >
            <view class="flex items-center gap10rpx">
              <text class="i-hugeicons-happy"></text>
              <text class=" ">好评</text>
            </view>
          </view>
          <view
            :class="item.score == 20 ? 'd-c-c active' : 'd-c-c'"
            @click="gradeFunc(item, 20, index)"
          >
            <view class="flex items-center gap10rpx">
              <text class="i-hugeicons-meh"></text>
              <text class=" ">中评</text>
            </view>
          </view>
          <view
            :class="item.score == 30 ? 'd-c-c active' : 'd-c-c'"
            @click="gradeFunc(item, 30, index)"
          >
            <view class="flex items-center gap10rpx">
              <text class="i-hugeicons-sad-01"></text>
              <text class=" ">差评</text>
            </view>
          </view>
        </view>
        <view class="textarea-box text-28rpx">
          <textarea
            class="p20rpx box-s-b border flex-1"
            v-model="item.content"
            placeholder="请输入评价内容"
          />
        </view>

        <wd-upload
          :file-list="fileList"
          image-mode="aspectFill"
          :action="action"
          name="iFile"
          :limit="6"
          :form-data="formData"
          @change="handleChange"
          @before-upload="handleBeforeUpload"
          @success="handleSuccess"
          @fail="handleFail"
        ></wd-upload>
      </view>
      <view class="foot-btns py30rpx box-border">
        <button form-type="submit" class="rounded-30rpx text-white bg-red fw-bold">提交评价</button>
      </view>
    </form>

    <!--上传图片-->
    <!-- <Upload v-if="isUpload" @getImgs="getImgsFunc"></Upload> -->
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
// import Upload from '@/components/upload/upload.vue'
import { GetUserCommentToOrder, SubmitUserCommentToOrder } from '@/service'
import { getEnvBaseUploadUrl } from '@/utils'

interface FormDataItem {
  orderProductId: number | string
  productId: number | string
  score: number | string
  imageList: { filePath: string }[]
  expressScore: number | string
  serverScore: number | string
  describeScore: number | string
  orderId: number | string
  userId: number
  content: string
}
const fileList = ref<any[]>([
  // {
  //   // url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg',
  // },
])

const action = ref(getEnvBaseUploadUrl()) // 等同于 useUpload 中的 VITE_UPLOAD_BASEURL
const formData = ref({
  appId: 10001,
  timestamp: Date.now(),
})
// 响应式数据
const loading = ref(true)
const orderId = ref('')
let defauleScore = ref(10)
let tableData = ref<Api.User.UserCommentToOrder[]>([])
const isUpload = ref(false)
// let formData = reactive({
//       orderProductId:  null,
//     productId:  null,
//     score:  null,
//     imageList:  null,
//     expressScore:   5,
//     serverScore:  5,
//     describeScore:  5,
//     orderId:  null,
//     userId:  null,
//     content: null
// })
let describe_score = ref(5)
let server_score = ref(5)
let express_score = ref(5)
const currentIndex = ref('')
const service = ref<boolean[][]>([])
const logistics = ref<boolean[][]>([])
const describe = ref<boolean[][]>([])

// 生命周期钩子
onLoad((e: any) => {
  orderId.value = e.orderId
})

onMounted(() => {
  // uni.showLoading({
  //   title: '加载中'
  // })
  // 获取页面数据
  getData()
})
const handleChange = ({ fileList: files }) => {}

// 工具方法
const showSuccess = (msg: string, callback?: () => void) => {
  uni.showToast({
    title: msg,
    icon: 'success',
    success: () => {
      callback && setTimeout(callback, 1500)
    },
  })
}

const gotoPage = (
  url: string,
  type: 'navigateTo' | 'redirectTo' | 'reLaunch' | 'switchTab' = 'navigateTo',
) => {
  uni[type]({ url })
}

// 获取数据
const getData = async () => {
  uni.showLoading({
    title: '正在处理',
  })

  try {
    const res = await GetUserCommentToOrder({
      orderId: orderId.value,
      appId: import.meta.env.VITE_APPID,
    })
    res.data.forEach((item) => {
      item.score = defauleScore.value
    })
    tableData.value = res.data

    console.log('res', res.data)

    uni.hideLoading()
    loading.value = false
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '查询失败请重试',
      duration: 2000,
      icon: 'none',
    })
  }
}
const handleBeforeUpload = (file) => {
  console.log('准备上传:', file)
}

const handleSuccess = (res: any) => {
  console.log('上传成功:', res) // 假设 res 包含了上传后的文件路径信息
  if (res.file.response) {
    const response = JSON.parse(res.file.response)
    const newFile = {
      uid: response.data.fileId, // 使用组件生成的 uid
      name: response.data.fileId, // 保留原文件名
      url: response.data.filePath, // 服务端返回的文件路径
      thumb: response.data.filePath, // 服务端返回的文件路径
      response: res.file.response,
      status: 'success', // 标记为上传成功
    }
    fileList.value.push(newFile)
  }
}
const handleFail = (err) => {
  console.error('上传失败:', err)
  uni.showToast({ title: '上传失败', icon: 'none' })
}
// 选择评价
const gradeFunc = (item: Api.User.UserCommentToOrder, score: number, index: number) => {
  console.log('score', score, item.score)

  tableData.value[index].score = score
}

// 评分方法
const chooseLog = (n: number, m: number) => {
  // tableData.value[m].express_score = 0
  // logistics.value[m] = logistics.value[m].map((_, index) => {
  //   const selected = index <= n
  //   if (selected) tableData.value[m].express_score!++
  //   return selected
  // })
}

const chooseServ = (n: number, m: number) => {
  // tableData.value[m].server_score = 0
  // service.value[m] = service.value[m].map((_, index) => {
  //   const selected = index <= n
  //   if (selected) tableData.value[m].server_score!++
  //   return selected
  // })
}

const choosees = (n: number, m: number) => {
  // tableData.value[m].describe_score = 0
  // describe.value[m] = describe.value[m].map((_, index) => {
  //   const selected = index <= n
  //   if (selected) tableData.value[m].describe_score!++
  //   return selected
  // })
}

// 表单提交
const formSubmit = async () => {
  let fileIdList = fileList.value.map((ele) => ({
    fileId: ele.uid,
    filePath: ele.url,
  }))
  const formDataObj = [
    {
      orderProductId: tableData.value[0].orderProductId,
      productId: tableData.value[0].productId,
      score: tableData.value[0].score,
      imageList: fileIdList,
      expressScore: express_score.value || 5,
      serverScore: server_score.value || 5,
      describeScore: describe_score.value || 5,
      userId: tableData.value[0].userId,
      content: tableData.value[0].content || '',
      orderId: tableData.value[0].orderId,
    },
  ]

  try {
    let res = await SubmitUserCommentToOrder({
      appId: import.meta.env.VITE_APPID,
      orderId: tableData.value[0].orderId,
      params: formDataObj,
    })
    uni.showToast({
      title: res.msg || '评价成功',
    })
    gotoPage('/pages-my/userEvaluate/userEvaluate')
  } catch (e) {
    uni.showToast({
      title: '评价失败,请重试！',
      icon: 'none',
    })
  }
}

const formReset = () => {
  // 重置表单逻辑
}

// 图片上传相关
const openUpload = (index: number) => {
  //   currentIndex.value = index
  isUpload.value = true
}

const getImgsFunc = (e: any) => {
  if (e && typeof e !== 'undefined') {
    const index = Number(currentIndex.value)
    tableData.value[index].imageList = [...tableData.value[index].imageList, ...e]
  }
  isUpload.value = false
}

const deleteImg = (i: number, n: number) => {
  tableData.value[i].imageList.splice(n, 1)
}
</script>

<style lang="scss" scoped>
.active {
  background: red;
  color: #fff;
  border-radius: 30rpx;
}
.d-c-c {
  padding: 10rpx;
  font-size: 28rpx;
}
</style>

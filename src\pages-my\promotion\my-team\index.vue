<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
    class="w-full bg-[rgba(255,255,255,1)] grid grid-cols-1 grid-auto-rows-min px-36rpx box-border"
  >
    <SimpleNavBar title="我的团队" />
    <div class="grid grid-cols-1 gap-y-84rpx">
      <div class="flex flex-col px-36rpx box-border w-full items-center gap-y-60rpx pt-60rpx">
        <wd-datetime-picker type="year-month" v-model="date" />
        <div class="flex flex-col items-center gap-y-40rpx w-full">
          <span class="text-36rpx text-#333333 font-500">团队成员业绩占比</span>
          <PieChart v-if="data?.records?.length" :data="pieWithOthers" />
          <div v-else>
            <wd-img
              height="300rpx"
              width="300rpx"
              src="https://file.shanqianqi.com/image/2025/06/13/db3fc701583e4ca1a7bf28e60788ff53.png"
              mode="aspectFill"
            ></wd-img>
          </div>
        </div>
      </div>
      <div class="flex flex-col items-center gap-y-40rpx w-full">
        <div class="flex flex-col w-full">
          <div
            class="text-32rpx h74rpx flex items-center font-500 justify-center w-full text-#333333 border border-solid border-#8445c7 border-b-none box-border"
          >
            团队成员业绩数据
          </div>
          <div
            class="text-28rpx h74rpx flex items-center justify-center w-full text-#333333 border border-solid border-#8445c7 border-b-none box-border"
          >
            <wd-datetime-picker type="year-month" v-model="performanceDate" />
          </div>
          <div
            class="grid grid-cols-1 box-border bg-#ffffff border-2rpx border-solid border-[#8445c7]"
          >
            <div
              class="grid grid-cols-3 text-28rpx text-#333333 font-500 border-b-2rpx border-b-solid border-b-[#8445c7] h-66rpx"
            >
              <div
                class="w-full flex items-center justify-center border-r border-r-solid border-r-#8445c7"
              >
                名称
              </div>
              <div
                class="w-full flex items-center justify-center border-r border-r-solid border-r-#8445c7"
              >
                职位
              </div>
              <div
                class="w-full flex items-center justify-center border-r border-r-solid border-r-#8445c7"
              >
                完成业绩
              </div>
              <!-- <div class="w-full flex items-center justify-center">是否晋升</div> -->
            </div>
            <div
              v-for="item in tableDataList"
              :key="item"
              class="grid grid-cols-3 text-24rpx font-500 text-#333333 h-66rpx"
            >
              <div
                class="w-full flex items-center justify-center border-r border-r-solid border-r-#8445c7"
              >
                {{ item?.nickName ?? ' ' }}
              </div>
              <div
                class="w-full flex items-center justify-center border-r border-r-solid border-r-#8445c7"
              >
                {{ item?.position ?? ' ' }}
              </div>
              <div
                class="w-full flex items-center justify-center border-r border-r-solid border-r-#8445c7"
              >
                {{ item?.completePerformance ?? ' ' }}
              </div>
              <!-- <div class="w-full flex items-center justify-center">是</div> -->
            </div>
          </div>

          <div class="w-full">
            <wd-pagination
              custom-style="border: 1px solid #8445c7;border-top:none"
              v-model="page"
              v-if="total"
              :total="total"
            ></wd-pagination>
          </div>
        </div>
      </div>
    </div>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import PieChart from '@/components/PieChart/index.vue'
import { getTeamPerformance } from '@/service'
import dayjs from 'dayjs'

const { safeAreaInsets } = uni.getSystemInfoSync()

const date = ref<number>(Date.now())
watch(
  () => date.value,
  () => {
    run()
  },
)
const { data, run } = useRequest(
  () =>
    getTeamPerformance({
      pageIndex: 1,
      pageSize: 99999,
      level: 1,
      lastMonth: false,
      startDate: dayjs(date.value).startOf('month').format('YYYY-MM-DDTHH:mm:ss'),
      endDate: dayjs(date.value).endOf('month').format('YYYY-MM-DDTHH:mm:ss'),
    }),
  { immediate: true },
)

const pieData = computed<Api.User.TeamPerformanceItem[]>(() => data.value?.records)

const pieWithOthers = computed(() => {
  const top = pieData.value?.slice(0, 4) ?? []
  const others = pieData.value?.slice(4) ?? []

  const topMapped = top.map((item) => ({
    name: `${item?.nickName}（${item?.performanceRatio}%）`,
    value: Math.round(Number(item?.completePerformance)),
  }))

  if (others.length > 0) {
    const otherTotal = others.reduce((sum, item) => sum + Number(item?.completePerformance), 0)
    const otherTotalRatio = others.reduce((sum, item) => sum + Number(item?.performanceRatio), 0)

    topMapped.push({
      name: `其他成员（${otherTotalRatio}%）`,
      value: Math.round(otherTotal),
    })
  }

  return topMapped
})

const performanceDate = ref<number>(Date.now())

const page = ref<number>(1)
watch([() => performanceDate.value, () => page.value], () => {
  tableRun()
})
const pageSize = ref<number>(10)
const { data: tableData, run: tableRun } = useRequest(
  () =>
    getTeamPerformance({
      pageIndex: page.value,
      pageSize: pageSize.value,
      level: 1,
      lastMonth: false,
      startDate: dayjs(performanceDate.value).startOf('month').format('YYYY-MM-DDTHH:mm:ss'),
      endDate: dayjs(performanceDate.value).endOf('month').format('YYYY-MM-DDTHH:mm:ss'),
    }),
  { immediate: true },
)
const total = computed<number>(() => tableData.value?.total)
const tableDataList = computed(() => tableData?.value?.records)
</script>

<style lang="scss" scoped>
:deep(.wd-picker__cell) {
  background: transparent !important;
  color: #333333 !important;
  font-size: 28rpx !important;
}
</style>

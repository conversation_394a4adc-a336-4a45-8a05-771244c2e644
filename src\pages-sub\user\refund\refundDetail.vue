<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: ' 售后详情',
  },
}
</route>

<template>
  <view class="pb100rpx">
    <!--售后状态-->
    <view class="bg-#ff5704 text-34rpx flex items-center text-white gap20rpx p30rpx">
      <view class="i-carbon-warning-alt"></view>
      <view class="state-cont flex-1">
        <view class="state-txt d-b-c">
          <text class="desc f34">{{ detail.stateText }}</text>
        </view>
      </view>
      <view class="dot-bg"></view>
    </view>

    <!--商品信息-->
    <view class="p30rpx mt20rpx bg-white">
      <view
        class="flex items-center gap30rpx pb20rpx"
        @click="gotoProduct(detail.orderProduct.productId, detail.orderM.storeId)"
      >
        <view class="w170rpx h170rpx">
          <image
            :src="detail.orderProduct && detail.orderProduct.productImage"
            mode="aspectFit"
          ></image>
        </view>
        <view class="flex-1 mr20rpx">
          <view class="text-32rpx text-#666">
            {{ detail.orderProduct && detail.orderProduct.productName }}
          </view>
          <view class="pt10rpx">
            <text class="text-24rpx">
              <!--斤:10斤; 颜色:红; 出产地:北方; 销量:大于1000; 大小:直径1分米; 口感:甜;-->
            </text>
          </view>
        </view>
      </view>
      <view class="pt20rpx flex items-center justify-end">
        <view class="text-28rpx">
          商品金额：
          <text class="text-red">¥{{ totalPrice }}</text>
        </view>
      </view>
      <view class="pt10rpx flex items-center justify-end">
        <view class="text-28rpx">
          订单实付金额：
          <text class="text-red">¥{{ totalPayPrice }}</text>
        </view>
      </view>
      <view
        class="pt10rpx lh-150rpx flex items-center justify-end px30rpx"
        v-if="detail.orderM && detail.orderM.orderSource == 80"
      >
        <view class="text-28rpx" v-if="detail.orderM.advance">
          定金({{ detail.orderM.advance.moneyReturn ? '可退' : '不可退' }})：
          <text class="text-red">¥{{ detail.orderM.advance.payPrice }}</text>
        </view>
      </view>
    </view>

    <!-- 已退款金额 -->
    <view
      class="text-28rpx bg-white flex items-center justify-end px30rpx"
      v-if="
        (detail.status == 20 && detail.type == 10) || (detail.status == 20 && detail.type == 30)
      "
    >
      <text class="gray9">已退款金额：</text>
      <text class="text-red">￥{{ detail?.refundMoney }}</text>
    </view>

    <!--申请售后信息-->
    <view class="px30rpx mb20rpx box-border bg-white text-28rpx">
      <view class="py20rpx border-b text-34rpx">申请退货信息</view>
      <view class="py20rpx text-30rpx">
        <text class="text-#999">售后类型：</text>
        <text>{{ detail.typeText }}</text>
      </view>
      <view class="py20rpx text-30rpx">
        <text class="text-#999">申请原因：</text>
        <text>
          {{ detail.applyDesc || '' }}
        </text>
      </view>
      <view class="py20rpx text-30rpx flex items-center gap20rpx">
        <text class="text-#999">申请凭证：</text>
        <view class="flex flex-wrap">
          <block v-if="detail.images && detail.images.length > 0">
            <view
              style="border: 2rpx solid #dddddd"
              class=""
              v-for="(imgs, img_num) in detail.images"
              :key="img_num"
            >
              <wd-img
                :src="imgs.filePath"
                :preview-src="imgs.filePath"
                :width="102"
                :height="102"
                :enable-preview="true"
              />
            </view>
          </block>
          <block v-else>无</block>
        </view>
      </view>
    </view>

    <!-- 售后信息-->
    <view v-if="detail.status == 10" class="px30rpx bg-white">
      <view class="flex items-center">
        <view class="min-w220rpx py20rpx text-34rpx">商家拒绝理由:</view>
        <view class="px20rpx text-red text-28rpx break-all">
          {{ detail.refuseDesc }}
        </view>
      </view>

      <view class="flex items-center">
        <view class="min-w220rpx py20rpx border-b text-34rpx">平台拒绝理由:</view>
        <view class="py20rpx">
          <text class="text-red text-30rpx break-all">{{ detail.plateDesc }}</text>
        </view>
      </view>
    </view>

    <!--退货地址 v-if="detail.isAgree == 10 && detail.address"-->
    <view v-if="detail.isAgree == 10 && detail.address" class="mb20rpx box-border px30rpx bg-white">
      <view style="border-bottom: 1rpx solid #eeeeee" class="py20rpx text-34rpx">退货地址</view>
      <view class="pt30rpx text-28rpx">
        <text class="gray9">收货人：</text>
        <text class="break-all">{{ detail.address.name }}</text>
      </view>
      <view class="pt30rpx text-28rpx">
        <text class="gray9">联系电话：</text>
        <text class="break-all">{{ detail.address.phone }}</text>
      </view>
      <view class="pt30rpx text-28rpx">
        <text class="gray9">详情地址：</text>
        <text class="break-all">{{ detail.address.detail }}</text>
      </view>
      <view class="pt30rpx text-28rpx" v-if="detail.expressNo">
        <text class="gray9">物流公司：</text>
        <text class="break-all">{{ detail.expressName }}</text>
      </view>
      <view class="pt30rpx text-28rpx" v-if="detail.expressNo">
        <text class="gray9">物流单号：</text>
        <text class="break-all">{{ detail.expressNo }}</text>
      </view>
      <view class="pt30rpx text-28rpx" v-if="detail.isPlateSend">
        <text class="gray9">换货物流公司：</text>
        <text class="break-all">{{ detail.sendExpressName }}</text>
      </view>
      <view
        class="pt30rpx text-28rpx"
        v-if="detail.isPlateSend"
        @click="gotoPage('/pages/order/express/refund-express?orderId=' + detail.orderRefundId)"
      >
        <text class="gray9">换货物流单号：</text>
        <text class="break-all">{{ detail.sendExpressNo }}</text>
      </view>
      <view style="border-top: 1rpx solid #eeeeee" class="mt20rpx pb20rpx text-24rpx text-#999">
        <view class="pt20rpx">· 未与卖家协商一致情况下，请勿寄到付或平邮</view>
        <view class="pt10rpx">· 请填写真实有效物流信息</view>
      </view>
    </view>
    <!-- 填写物流信息 -->
    <form
      @submit="formSubmit"
      v-if="detail.type != 30 && detail.isAgree == 10 && detail.isUserSend == 0"
      report-submit
    >
      <view v-if="detail.isPlateSend == 0" class="group bg-white">
        <view class="p30rpx border-b text-34rpx">填写物流信息</view>
        <view class="p-20rpxS-0 d-s-c">
          <!-- <view class="gray9">物流公司：</view> -->
          <view class="flex-1">
            <wd-picker
              :columns="columns"
              label="物流公司"
              placeholder="请选择物流公司"
              v-model="returnGoodsForm.expressId"
              @confirm="handleConfirm"
            />
          </view>
        </view>
        <view class="px30rpx text-24rpx">
          <view class="flex-1">
            <wd-input
              type="text"
              label="物流单号："
              placeholder="请填写物流单号"
              v-model="returnGoodsForm.expressNo"
              center
            ></wd-input>
          </view>
        </view>
        <view class="mt20rpx px30rpx py10rpx">
          <button
            style="background: #f6220c; color: #fff"
            class="!rounded-44rpx text-24rpx"
            @click="formSubmit"
          >
            确认发货
          </button>
        </view>
      </view>
    </form>
  </view>
</template>

<script lang="ts" setup>
import { fetchUserRefunDetail, refundDelivery } from '@/service'
let detail = ref<Api.refund.RerefundDetail>(null)
let orderRefundId = ref('')
let source = ref('user')
let productId = ref('')
let orderId = ref('')
let expressList = ref<Api.refund.RefundExpress[]>([])
let totalPrice = ref('')
let totalPayPrice = ref('')
let columns = ref([])
let returnGoodsForm = {
  expressNo: '',
  expressId: '',
  orderRefundId: '',
}

onLoad((options) => {
  orderRefundId.value = options.order_refund_id
  if (source.value) {
    source.value = options.source
  }
  if (options.productId) {
    productId.value = options.productId
  }
  if (options.orderId) {
    orderId.value = options.orderId
  }
})
onMounted(() => {
  fetchData()
})

const gotoProduct = (productId, storeId) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${productId}&storeId=${storeId}` })
}
const paging = ref()
const fetchData = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    const { data } = await fetchUserRefunDetail({
      orderRefundId: orderRefundId.value,
      appId: import.meta.env.VITE_APPID,
      orderId: orderId.value,
      productId: productId.value,
    })
    detail.value = data
    console.log(detail.value.isAgree)

    columns.value = data.expressList.map((ele) => {
      return {
        label: ele.expressName,
        value: ele.expressId,
      }
    })
    totalPrice.value = data.orderProduct.totalPrice
    totalPayPrice.value = data.orderProduct.totalPayPrice
    uni.hideLoading()
  } catch (err) {
    uni.hideLoading()
  }
}
//选择物流公司
const handleConfirm = (e) => {
  returnGoodsForm.expressId = e.value
}
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
//发货
const formSubmit = async () => {
  returnGoodsForm.orderRefundId = orderRefundId.value
  if (!returnGoodsForm.expressId) {
    uni.showToast({ title: '请输入快递公司！', icon: 'none' })
    return
  }
  if (!returnGoodsForm.expressNo) {
    uni.showToast({ title: '请输入物流单号！', icon: 'none' })
    return
  }

  uni.showLoading({ title: '正在提交', mask: true })
  // 实际提交逻辑
  try {
    const res = await refundDelivery({
      ...returnGoodsForm,
      appId: import.meta.env.VITE_APPID,
    })
    uni.hideLoading()
    fetchData()
    uni.showToast({ title: res.msg || '申请成功' })
  } catch (err) {
    uni.hideLoading()
    uni.showToast({ title: '申请失败，请稍后重试' })
  }
}
</script>

<style lang="scss" scoped>
//
:deep(.wd-picker__label) {
  padding-left: 30rpx !important;
}
:deep(.wd-radio) {
  margin: 0 !important;
  display: flex;
  gap: 30rpx;
}

:deep(.wd-cell__value) {
  text-align: left !important;
}
</style>

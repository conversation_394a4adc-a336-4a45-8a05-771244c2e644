<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '申请入驻',
  },
}
</route>

<template>
  <view></view>
</template>

<script setup lang="ts">
import { userApplyDetail } from '@/service'
import { useUserSettingStore } from '@/store'
const userSettingStore = useUserSettingStore()

const loading = ref(true)

// 生命周期 - 页面加载
onShow(() => {
  getData()
})

// 获取数据
const getData = async () => {
  loading.value = true
  uni.showLoading({ title: '加载中...' })

  await userSettingStore.fetchUserSetting()
  const status = userSettingStore.userSetting?.supplierStatus

  const { data } = await userApplyDetail()

  if (status === 3) {
    uni.showModal({
      content: '商户异常,请联系客服处理',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      },
    })
    return
  }

  if ((!data?.supplierApplyId && status !== 2) || (!data?.ecNo && !data?.resultUrl)) {
    uni.redirectTo({ url: '/pages-my/my-shop/shop-apply/index' })
    return
  }

  if (!data?.ecNo && data?.resultUrl) {
    uni.redirectTo({ url: `/pages-sub/webview/webview?url=${data?.resultUrl}` })
    return
  }

  if ((data?.ecNo || data?.merChantNo || data?.contractId) && !data?.lakalaLedgerBind) {
    uni.redirectTo({ url: '/pages-my/my-shop/shop-apply-status/index' })
    return
  }

  if (status === 2 && data?.lakalaLedgerBind) {
    uni.redirectTo({ url: '/pages-my/my-shop/index' })
    return
  }
}
</script>
<style scoped>
:deep(.wd-picker__cell) {
  padding-left: unset !important;
  padding-right: unset !important;
  background: unset !important;
}
:deep(.wd-picker__label) {
  margin-right: unset !important;
}
:deep(.wd-picker__value) {
  color: #999999 !important;
}

:deep(.wd-col-picker__cell) {
  padding-left: unset !important;
  padding-right: unset !important;
  background: unset !important;
}
:deep(.wd-col-picker__label) {
  margin-right: unset !important;
}
:deep(.wd-col-picker__value) {
  color: #999999 !important;
}
:deep(.wd-picker__cell::after) {
  display: none !important;
}
:deep(.wd-col-picker__cell) {
  padding: unset !important;
}
:deep(.wd-col-picker__cell::after) {
  display: none !important;
}
:deep(.wd-picker__cell) {
  padding: unset !important;
}
</style>

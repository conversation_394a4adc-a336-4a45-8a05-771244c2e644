import { defineStore } from 'pinia'
import { fetchLoginSetting } from '@/service'

export const useLoginSettingStore = defineStore(
  'login-setting',
  () => {
    const loginSetting = ref<Api.User.LoginSettingData>()

    const getLoginSetting = async () => {
      const { data } = await fetchLoginSetting({ appId: import.meta.env.VITE_APP_TITLE })

      setLoginSetting(data)
    }

    const setLoginSetting = (info: Api.User.LoginSettingData) => {
      loginSetting.value = info
    }

    const clearLoginSetting = () => {
      loginSetting.value = undefined
    }

    return {
      loginSetting,
      getLoginSetting,
      setLoginSetting,
      clearLoginSetting,
    }
  },
  { persist: true },
)

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#fff h-100% pt-30rpx">
    <z-paging
      ref="paging"
      empty-view-text="没有评论哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      loading-more-no-more-text="已加载全部评论"
      :paging-style="{ background: '#ffffff', 'padding-bottom': `${bottom || 15}px` }"
      fixed
    >
      <template #top>
        <wd-navbar
          left-arrow
          :safeAreaInsetTop="true"
          :placeholder="true"
          :bordered="false"
          fixed
          @click-left="handleBack"
        >
          <template #title>
            <view class="">详情</view>
          </template>
        </wd-navbar>
      </template>
      <div class="grid grid-cols-1 gap-y-30rpx px-25rpx box-border pt-30rpx">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <img
              :src="detailInfo?.avatarUrl"
              alt=""
              class="w-110rpx h-110rpx rounded-50% mr-20rpx"
            />
            <div class="flex flex-col gap-y-5rpx">
              <span class="text-26rpx text-#333333">{{ detailInfo?.nickName }}</span>
              <span class="text-#999999 text-22rpx">
                {{ detailInfo?.updateTime }}
              </span>
              <span class="text-#999999 text-22rpx">
                IP属地：{{ detailInfo?.city ? `${detailInfo?.city}` : '未知' }}
              </span>
            </div>
          </div>
          <div
            class="w-120rpx h-55rpx flex justify-center items-center rounded-60rpx"
            :class="detailInfo?.isFollow ? 'bg-#F2F2F2' : 'bg-#ff7d2633'"
            @click.stop="handleFollow(detailInfo)"
            v-if="defaultPage.myId != detailInfo.userId"
          >
            <span
              class="text-22rpx"
              :class="detailInfo?.isFollow ? 'text-#999999' : 'text-#FF7D26'"
            >
              {{ detailInfo?.isFollow ? '已关注' : '+关注' }}
            </span>
          </div>
        </div>
        <div class="text-32rpx text-#333333">
          {{ detailInfo?.title }}
        </div>
        <div class="text-28rpx text-#333333">
          {{ detailInfo?.content }}
        </div>
        <div class="mt-20rpx gap-15rpx" v-if="detailInfo?.images?.length > 0">
          <wd-img
            v-for="(img, imgIndex) in detailInfo?.images"
            :key="imgIndex"
            :src="img"
            mode="aspectFill"
            width="100%"
            height="720rpx"
            radius="16rpx"
            alt=""
            @click="previewImages(imgIndex)"
          />
        </div>
        <div class="s_bor flex justify-around pb-30rpx">
          <div class="flex flex-col justify-between items-center">
            <image
              @click.stop="shoucang(detailInfo)"
              class="h-60rpx w-60rpx"
              :src="
                detailInfo.isCollect
                  ? 'https://file.shanqianqi.com/image/2025/06/24/2730dbe75bcf489b86bdc8a17577489b.png'
                  : 'https://file.shanqianqi.com/image/2025/06/24/b17beaacc78944b69ec4f85d17876218.png'
              "
            ></image>
            <span class="text-24rpx text-#666666">{{ detailInfo?.collectCount }}</span>
          </div>
          <div class="flex flex-col justify-between items-center">
            <image
              @click.stop="dianzan(detailInfo)"
              class="h-60rpx w-60rpx"
              :src="
                detailInfo.isLike
                  ? 'https://file.shanqianqi.com/image/2025/06/24/37ec1f17f8864efdb3d7108233fa0217.png'
                  : 'https://file.shanqianqi.com/image/2025/06/24/91d17d7940f84f96b477ee4f31c11494.png'
              "
            ></image>
            <span class="text-24rpx text-#666666">{{ detailInfo?.likesCount }}</span>
          </div>
        </div>
      </div>

      <div class="pl-25rpx pr-25rpx pt28rpx">
        <div class="flex justify-between items-center mb-30rpx">
          <div class="text-28rpx text-#333333">全部回复</div>
        </div>
        <div v-for="item in dataList" :key="item?.postId" class="">
          <div class="flex justify-between w-100%">
            <div class="flex items-center">
              <wd-img
                width="70rpx"
                height="70rpx"
                :src="item?.avatarUrl"
                radius="50%"
                mode="aspectFill"
              />
              <span class="text-26rpx text-#333333 ml-20rpx">{{ item?.nickName }}</span>
            </div>
            <div class="flex items-center pr-10rpx" v-if="item.isDelete != 1">
              <image
                @click.stop="commentLike(item)"
                class="h-40rpx w-40rpx"
                :src="
                  item.isLike
                    ? 'https://file.shanqianqi.com/image/2025/06/24/37ec1f17f8864efdb3d7108233fa0217.png'
                    : 'https://file.shanqianqi.com/image/2025/06/24/91d17d7940f84f96b477ee4f31c11494.png'
                "
              ></image>
              <span class="text-22rpx text-#999999 ml-10rpx">{{ item?.likesCount }}</span>
            </div>
          </div>
          <div class="ml-90rpx">
            <div class="flex flex-col">
              <div class="">
                <span
                  v-if="item?.content && item.isDelete != 1"
                  class="!text-28rpx !m-unset !p-unset !line-height-unset !text-#333333 !justify-start customBtn break-normal"
                >
                  {{ item?.content }}
                </span>
                <div class="w-200rpx h-200rpx" v-if="item.image">
                  <wd-img
                    :src="item?.image"
                    width="200rpx"
                    height="200rpx"
                    radius="16rpx"
                    mode="aspectFit"
                    :enable-preview="true"
                  ></wd-img>
                </div>
                <span
                  class="text-24rpx text-blue ml-30rpx"
                  v-if="item.isDelete != 1"
                  @click="handleCommentAction(item)"
                >
                  {{ item.userId === userStore.userInfo.userId ? '操作' : '回复' }}
                </span>
              </div>
              <div v-if="item.isDelete == 1" class="text-#333333">评论已删除</div>
              <span class="text-#999999 text-22rpx mt-20rpx mb-10rpx flex items-center gap-x-5px">
                {{ item?.createTime }}
                <span>{{ item?.city ? `(${item?.city})` : '(未知)' }}</span>
              </span>
            </div>
            <div
              v-if="item?.children?.length"
              class="bg-#F7F7FA p-20rpx rounded border border-solid border-#eeeeee"
            >
              <CommentChildrenItem :comment="item" @showActionPopup="handleChildCommentAction" />
            </div>
          </div>
        </div>
      </div>
      <template #bottom>
        <div class="flex w-100%">
          <div class="flex flex-col w-100%">
            <div class="flex pl-25rpx" v-if="commentImage">
              <view class="relative">
                <wd-img
                  :src="commentImage"
                  width="100rpx"
                  height="100rpx"
                  radius="8rpx"
                  mode="aspectFill"
                />
                <wd-icon
                  name="close"
                  size="16px"
                  class="absolute top-0 right-0 bg-#00000080 text-#fff rounded-full p-2rpx"
                  @click="commentImage = ''"
                />
              </view>
              <span
                class="text-#FF7D26 text-26rpx h-60rpx w-120rpx mt50rpx ml-50rpx bg-#ff7d2633 flex justify-center items-center rounded-40rpx"
                @click="handleReply({ value: commentInputVal })"
              >
                发送
              </span>
            </div>

            <div class="flex justify-between pl-25rpx pr-25rpx pt-25rpx">
              <div
                class="bg-#F6F6F6 rounded-40rpx w-500rpx pl-30rpx flex items-center justify-between"
              >
                <wd-input
                  v-model="commentInputVal"
                  :focus="forumStore?.isFoucs"
                  placeholder="请输入评论内容"
                  @blur="handleInputBlur"
                  @confirm="handleReply"
                  class="w-100%"
                  no-border
                >
                  <template #suffix>
                    <div class="flex items-center">
                      <img
                        src="https://file.shanqianqi.com/image/2025/06/25/dcd85b4ae2ef4494bfab005053cded3d.png"
                        class="w-45rpx h-45rpx mr-15rpx"
                        alt=""
                        @click.stop="handleChooseImageWithDefault"
                      />
                    </div>
                  </template>
                </wd-input>
              </div>
              <div class="flex">
                <div class="flex flex-col justify-center items-center mr-30rpx">
                  <image
                    @click.stop="shoucang(detailInfo)"
                    class="h-40rpx w-40rpx"
                    :src="
                      detailInfo.isCollect
                        ? 'https://file.shanqianqi.com/image/2025/06/24/2730dbe75bcf489b86bdc8a17577489b.png'
                        : 'https://file.shanqianqi.com/image/2025/06/24/b17beaacc78944b69ec4f85d17876218.png'
                    "
                  ></image>
                  <span class="text-22rpx text-#666666">{{ detailInfo?.collectCount }}</span>
                </div>
                <div class="flex flex-col justify-center items-center">
                  <image
                    @click.stop="dianzan(detailInfo)"
                    class="h-40rpx w-40rpx"
                    :src="
                      detailInfo.isLike
                        ? 'https://file.shanqianqi.com/image/2025/06/24/37ec1f17f8864efdb3d7108233fa0217.png'
                        : 'https://file.shanqianqi.com/image/2025/06/24/91d17d7940f84f96b477ee4f31c11494.png'
                    "
                  ></image>
                  <span class="text-22rpx text-#666666">{{ detailInfo?.likesCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </z-paging>
    <wd-popup
      v-model="defaultPage.showPop"
      position="bottom"
      custom-style="height: 200px;"
      @close="handleClose"
      closable
    >
      <div class="flex flex-col items-center justify-center pl-40rpx pr-40rpx mt-50rpx">
        <wd-button
          type="info"
          icon="view-module"
          size="large"
          p
          hairline
          block
          custom-class="!w-100% mt-50rpx"
          @click="handlePopupReply"
        >
          回复
        </wd-button>
        <wd-button
          icon="delete-thin"
          type="error"
          size="large"
          hairline
          block
          custom-class="!w-100% mt-30rpx"
          @click="handleDeleteComment"
        >
          删除
        </wd-button>
      </div>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import CommentChildrenItem from '@/components/CommentChildrenItem/index.vue'

import {
  discourseDetail,
  discourseCommentList,
  discourseAddComment,
  discourseFollower,
  discourseCancelFollower,
  discourseCancelLikes,
  discourseAddLikes,
  addCommentLike,
  cancelCommentLike,
  deleteComment,
} from '@/service'
import { useUserStore } from '@/store'
import { useForumStore } from '@/store'
import { useLocationStore } from '@/store'
import { getEnvBaseUploadUrl, createUploadHandler } from '@/utils'
const defaultPage = reactive({
  myId: 0,
  upImg: true,
  showPop: false,
})
const commentImage = ref('') // 评论图片URL
const locationStore = useLocationStore()
const forumStore = useForumStore()
const userStore = useUserStore()

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`
// 图片上传相关
const fileList = ref<Array<{ url: string }>>([])

const option = ref<{ postId: string }>({
  postId: '',
})
const postId = computed(() => option?.value?.postId)

onLoad(async (e: { postId: string }) => {
  console.log('🚀 ~ onLoad ~ e:', e)
  defaultPage.myId = userStore?.userInfo?.userId
  option.value = e
  fetchDetailInfo()
})

const previewImages = (currentIndex: number) => {
  uni.previewImage({
    current: currentIndex, // 当前显示图片的索引
    urls: detailInfo.value?.images || [], // 所有图片URL数组
    indicator: 'number', // 显示数字指示器
    loop: true, // 支持循环预览
  })
}
//论坛详情
const { data: detailInfo, run: fetchDetailInfo } = useRequest(() =>
  discourseDetail({ postId: postId.value }),
)

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const paging = ref()
const dataList = ref<Api.Forum.ReplyItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await discourseCommentList({ pageIndex, pageSize, postId: postId.value })
      paging?.value?.complete(data?.records)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

const commentInputVal = ref('')

const handleInputBlur = () => {
  if (!commentImage.value && !commentInputVal.value) {
    forumStore.setIsFoucs(false)
    forumStore.clearActiveParentCommentId()
  }
}
// 关闭弹出层
const handleClose = () => {
  defaultPage.showPop = false
}
const handleChildCommentAction = ({ parentCommentId, commentId }) => {
  currentComment.parentCommentId = parentCommentId
  currentComment.commentId = commentId
  defaultPage.showPop = true
}
// 选择图片
const handleChooseImage = async () => {
  try {
    if (commentImage.value) {
      const { confirm } = await uni.showModal({
        title: '提示',
        content: '只能上传一张图片，是否替换当前图片？',
      })
      if (!confirm) return
    }

    await chooseAndUploadImage()
  } catch (error) {}
}

const chooseAndUploadImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sourceType: ['album', 'camera'],
    })

    if (!res.tempFilePaths?.length) return

    uni.showLoading({ title: '上传中...', mask: true })
    const uploadResult = await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: VITE_UPLOAD_BASEURL,
        filePath: res.tempFilePaths[0],
        name: 'iFile',
        formData: { appId: 10001 },
        header: { token: `${userStore.token}` },
        success: resolve,
        fail: reject,
      })
    })

    // 处理上传结果
    const responseData = JSON.parse(uploadResult.data)
    console.log('上传结果:', responseData)

    if (responseData.code === 1) {
      commentImage.value = responseData.data.filePath
    } else {
      throw new Error(responseData.msg || '上传失败')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    throw error
  } finally {
    uni.hideLoading()
  }
}

// 添加新方法
const handleChooseImageWithDefault = async () => {
  // 如果输入框为空，设置默认值
  if (!commentInputVal.value.trim()) {
    commentInputVal.value = ' '
  }
  await handleChooseImage()
}

const handleReply = async ({ value }: { value: string }) => {
  // 是默认值视为空内容
  const finalContent = value === ' ' ? '' : value

  if (!finalContent && !commentImage.value) {
    uni.showToast({ title: '请输入内容或添加图片', icon: 'none' })
    return
  }
  try {
    uni.showLoading({ title: '发布中...', mask: true })
    await discourseAddComment({
      postId: detailInfo?.value?.postId,
      content: finalContent,
      parentCommentId: forumStore?.activeParentCommentId,
      city: locationStore?.locationInfo?.cityName,
      image: commentImage.value,
    })
    forumStore.clearActiveParentCommentId()
    forumStore.setIsFoucs(false)
    commentInputVal.value = ''
    commentImage.value = ''
    paging.value?.reload()
  } catch (error) {
    console.error('评论失败:', error)
    uni.showToast({ title: '评论失败', icon: 'none' })
  } finally {
    uni.hideLoading()
  }
}

onBeforeUnmount(() => {
  forumStore.setIsFoucs(false)
  forumStore.clearActiveParentCommentId()
})

// 处理关注/取消关注操作
const handleFollow = async (detailInfo) => {
  try {
    const isFollow = !detailInfo.isFollow
    const api = isFollow ? discourseFollower : discourseCancelFollower
    await api({ userId: detailInfo.userId })
    // 更新状态
    detailInfo.isFollow = isFollow
    if (detailInfo.value) detailInfo.value.isFollow = isFollow
    uni.showToast({
      title: isFollow ? '关注成功' : '已取消关注',
      icon: 'none',
    })
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
  }
}
// 收藏/取消收藏
const shoucang = async (detailInfo) => {
  try {
    const isCollect = !detailInfo.isCollect
    const api = isCollect ? discourseAddLikes : discourseCancelLikes
    await api({
      postId: detailInfo.postId,
      source: 1, // 1表示收藏
    })
    // 更新状态和数量
    detailInfo.isCollect = isCollect
    detailInfo.collectCount = isCollect
      ? (detailInfo.collectCount || 0) + 1
      : Math.max(0, (detailInfo.collectCount || 0) - 1)
    if (detailInfo.value) {
      detailInfo.value.isCollect = isCollect
      detailInfo.value.collectCount = isCollect
        ? (detailInfo.value.collectCount || 0) + 1
        : Math.max(0, (detailInfo.value.collectCount || 0) - 1)
    }
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
  }
}

// 点赞/取消点赞
const dianzan = async (detailInfo) => {
  try {
    const isLike = !detailInfo.isLike
    const api = isLike ? discourseAddLikes : discourseCancelLikes
    await api({
      postId: detailInfo.postId,
      source: 2, // 2表示点赞
    })
    // 更新状态和数量
    detailInfo.isLike = isLike
    detailInfo.likesCount = isLike
      ? (detailInfo.likesCount || 0) + 1
      : Math.max(0, (detailInfo.likesCount || 0) - 1)
    if (detailInfo.value) {
      detailInfo.value.isLike = isLike
      detailInfo.value.likesCount = isLike
        ? (detailInfo.value.likesCount || 0) + 1
        : Math.max(0, (detailInfo.value.likesCount || 0) - 1)
    }
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
  }
}
//评论点赞
const commentLike = async (item) => {
  try {
    const isLike = !item.isLike
    const api = isLike ? addCommentLike : cancelCommentLike
    await api({
      commentId: item.commentId,
    })
    // 更新状态和数量
    item.isLike = isLike
    item.likesCount = isLike ? (item.likesCount || 0) + 1 : Math.max(0, (item.likesCount || 0) - 1)
    if (item.value) {
      item.value.isLike = isLike
      item.value.likesCount = isLike
        ? (item.value.likesCount || 0) + 1
        : Math.max(0, (item.value.likesCount || 0) - 1)
    }
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
  }
}

// 存储当前操作的评论ID
const currentComment = reactive({
  parentCommentId: '',
  commentId: '',
})
const handleCommentAction = (item) => {
  if (item.userId === userStore.userInfo.userId) {
    // 是自己的评论，显示操作弹窗
    currentComment.parentCommentId =
      item.parentCommentId === '0' ? item.commentId : item.parentCommentId
    currentComment.commentId = item.commentId
    defaultPage.showPop = true
  } else {
    // 不是自己的评论，直接回复
    forumStore.setIsFoucs(true)
    forumStore.setActiveParentCommentId(
      item.parentCommentId === '0' ? item.commentId : item.parentCommentId,
    )
  }
}

// 弹出层回复
const handlePopupReply = () => {
  forumStore.setIsFoucs(true)
  forumStore.setActiveParentCommentId(currentComment.parentCommentId)
  defaultPage.showPop = false // 关闭弹出层
}

// 删除评论
const handleDeleteComment = async () => {
  try {
    uni.showLoading({ title: '删除中...' })
    await deleteComment({ commentId: currentComment.commentId })
    uni.showToast({ title: '删除成功' })
    defaultPage.showPop = false
    paging.value?.reload() // 刷新评论列表
  } catch (error) {
    uni.showToast({ title: '删除失败', icon: 'none' })
  }
}

// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1,
  })
}
</script>

<style lang="scss" scoped>
.s_bor {
  border-bottom: 1px #f2f2f2 solid;
}
::v-deep .wd-input {
  background-color: transparent !important;
  width: 500rpx !important;
}
:deep(.customBtn .wd-button__content) {
  justify-content: start !important;
}
::v-deep .wd-img {
  margin-bottom: 15rpx !important;
}
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full grid grid-rows-[auto_auto_1fr] gap-y-10px px11px box-border"
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
  >
    <SimpleNavBar title="商户数据" />
    <div class="grid grid-cols-1 grid-auto-rows-min gap-y-10px">
      <div class="flex items-center gap-x-5px">
        <div class="w58rpx h58rpx bg-#ff5704 rd-1/2 flex items-center justify-center">
          <div class="i-hugeicons-chart-line-data-01 text-30rpx text-#ffffff"></div>
        </div>
        <span class="text-30rpx font-bold text-#333333">数据概况</span>
      </div>
      <div
        class="w-full bg-#ffffff rd-20rpx p30rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-10rpx"
      >
        <div
          class="text-26rpx text-#333333 border-b border-b-solid border-b-#eeeeee h80rpx box-border flex items-center justify-between"
        >
          今日成交量
        </div>
        <div class="grid grid-cols-3 gap-y-20px">
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.order?.orderTotalPriceT }}</span>
            <span class="text-26rpx text-#999999">成交额</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.order?.orderPerPriceT }}</span>
            <span class="text-26rpx text-#999999">客单价</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.order?.orderTotalT }}</span>
            <span class="text-26rpx text-#999999">付款订单</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.order?.orderUserTotalT }}</span>
            <span class="text-26rpx text-#999999">付款人数</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.order?.orderRefundMoneyT }}</span>
            <span class="text-26rpx text-#999999">成功退款金额</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.order?.orderRefundTotalT }}</span>
            <span class="text-26rpx text-#999999">退款订单</span>
          </div>
        </div>
      </div>
      <div
        class="w-full bg-#ffffff rd-20rpx p30rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-10rpx"
      >
        <div
          class="text-26rpx text-#333333 border-b border-b-solid border-b-#eeeeee h80rpx box-border flex items-center justify-between"
        >
          今日访问量
        </div>
        <div class="grid grid-cols-4">
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.visit?.favStoreT }}</span>
            <span class="text-26rpx text-#999999">商户收藏</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.visit?.favProductT }}</span>
            <span class="text-26rpx text-#999999">商品收藏</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.visit?.visitUserT }}</span>
            <span class="text-26rpx text-#999999">访客数</span>
          </div>
          <div class="w-full flex items-center justify-center flex-col gap-y-5px">
            <span class="text-32rpx text-#333333">{{ storeData?.visit?.visitTotalT }}</span>
            <span class="text-26rpx text-#999999">访问量</span>
          </div>
        </div>
      </div>
    </div>
    <div class="grid grid-rows-[auto_1fr] gap-y-10px">
      <div class="flex items-center gap-x-5px">
        <div class="w58rpx h58rpx bg-#ff5704 rd-1/2 flex items-center justify-center">
          <div class="i-hugeicons-license-draft text-30rpx text-#ffffff"></div>
        </div>
        <span class="text-30rpx font-bold text-#333333">订单结算</span>
      </div>
      <z-paging
        ref="paging"
        v-model="dataList"
        :default-page-size="10"
        :fixed="false"
        @query="queryList"
      >
        <div class="bg-#ffffff rd-25rpx">
          <view
            v-for="(item, index) in dataList"
            :key="index"
            class="w-full p23rpx box-border text-#585858 flex flex-col gap-y-20rpx"
          >
            <view class="d-b-c mb20rpx flex items-center justify-between">
              <view class="gray9" style="width: 400rpx">订单编号：{{ item.orderNo }}</view>
              <text class="i-carbon-chevron-right text-14px text-white"></text>
            </view>
            <view class="flex items-center justify-between text-24rpx">
              <view class="text-#999999">
                支付金额：
                <text class="text-#333333">{{ item.payMoney }}</text>
              </view>
              <view class="text-#999999">
                结算金额：
                <text class="text-#333333">{{ item.supplierMoney }}</text>
              </view>
            </view>
          </view>
        </div>
      </z-paging>
    </div>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { userStoreData } from '@/service'
const { safeAreaInsets } = uni.getSystemInfoSync()

const storeData = ref<Api.User.UserStoreData>()
const paging = ref()
const dataList = ref<Api.User.UserStoreDataRecordsItem[]>([])

const queryList = async (pageNo: number) => {
  try {
    const { data } = await userStoreData({
      pageIndex: pageNo,
    })
    storeData.value = data
    paging.value.complete(data?.ordersettle?.records)
  } catch (err) {
    paging.value.complete(false)
  }
}
</script>

<style lang="scss" scoped>
//
</style>

<template>
  <view>
    <view v-html="content"></view>
  </view>
</template>

<script>
import { fetchStoreList, getPolicy } from '@/service'

export default {
  data() {
    return {
      type: '',
      content: '',
    }
  },
  onLoad(e) {
    this.type = e.type
    let title = ''
    if (this.type == 'service') {
      title = '用户协议'
    } else if (this.type === 'privacy') {
      title = '隐私协议'
    } else if (this.type === 'merchart') {
      uni.setStorageSync('hasReadMerchartAgreement', true)
      title = '商户入驻协议'
    } else {
      uni.setStorageSync('hasReadPromotionAgreement', true)
      title = '推广员协议'
    }
    uni.setNavigationBarTitle({
      title: title,
    })
    this.getData()
  },
  methods: {
    async getData() {
      let self = this
      try {
        const { data } = await getPolicy()
        if (self.type == 'service') {
          self.content = data.service
        } else if (this.type === 'privacy') {
          self.content = data.privacy
        } else if (this.type === 'merchart') {
          self.content = data.merchant
        } else {
          self.content = data.revenueSharing
        }
      } catch (error) {}
    },
  },
}
</script>

<style></style>

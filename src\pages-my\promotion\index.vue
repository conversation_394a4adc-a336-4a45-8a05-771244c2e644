<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full grid grid-cols-1 grid-rows-[auto_1fr_auto] bg-#ffffff box-border"
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
  >
    <SimpleNavBar :bordered="false" title="我的推广" custom-class="!bg-#8445c7 " />
    <template v-if="isAgent">
      <div class="grid grid-cols-1 h-full gap-y-80rpx of-auto px-40rpx box-border">
        <div class="grid grid-cols-1 grid-auto-rows-min gap-y-40rpx">
          <!-- 头像 -->
          <div @click="goDetail" class="flex items-center justify-center gap-x-20rpx mt-20rpx">
            <wd-img
              width="266rpx"
              height="266rpx"
              :src="levelUrlMap[agent?.agent?.gradeId ?? 2]"
              mode="aspectFill"
            ></wd-img>
            <div class="flex flex-col gap-y-4rpx">
              <wd-img
                width="100rpx"
                height="100rpx"
                radius="50%"
                :src="userInfo?.avatarUrl"
                mode="aspectFill"
              ></wd-img>
              <span class="text-#8445C7 text-32rpx font-500">
                善新年（{{ agent?.agent?.grade }}）
              </span>
              <span class="text-#999999 text-28rpx">推荐人：{{ refereeUserNickName }}</span>
            </div>
          </div>
          <!-- 业绩 -->
          <div @click="goDetail" class="flex flex-col gap-y-40rpx">
            <div class="flex flex-col gap-y-4rpx text-24rpx">
              <span class="text-#999999">
                {{ diffMoney > 0 ? `当月距离下一等级${diffMoney}元业绩` : '已完成' }}
              </span>
              <wd-progress :percentage="moneyPercent" color="#9F50F4" hide-text />
              <div class="flex items-center justify-between">
                <span class="text-#333333">已完成业绩{{ countMoney }}元</span>
                <span class="text-#333333">{{ nextGradeAgentMoney }}元业绩/月</span>
              </div>
            </div>
            <div class="flex flex-col gap-y-4rpx text-24rpx">
              <span class="text-#999999">
                {{ diffStore > 0 ? `当月距离下一等级${diffStore}个直推商家` : '已完成' }}
              </span>
              <wd-progress :percentage="moneyStore" color="#FFC493" hide-text />
              <div class="flex items-center justify-between">
                <span class="text-#333333">已直推{{ countSupplier }}个直推商家</span>
                <span class="text-#333333">每月新增{{ nextGradeAgentStore }}个直推商家</span>
              </div>
            </div>
          </div>
          <!-- 收益 -->
          <div class="grid grid-cols-3 mt-40rpx">
            <div class="w-full flex flex-col items-center justify-center gap-y-4rpx text-#333333">
              <span class="text-40rpx font-500">{{ agent?.agent?.money }}</span>
              <span class="text-24rpx">可提现收益</span>
            </div>
            <div
              @click="goWithdrawal(10)"
              class="w-full flex flex-col items-center justify-center gap-y-4rpx text-#333333"
            >
              <span class="text-40rpx font-500">{{ agent?.agent?.freezeMoney }}</span>
              <span class="text-24rpx">待提现收益</span>
            </div>
            <div
              @click="goWithdrawal(40)"
              class="w-full flex flex-col items-center justify-center gap-y-4rpx text-#333333"
            >
              <span class="text-40rpx font-500">{{ agent?.agent?.totalMoney }}</span>
              <span class="text-24rpx">已提现收益</span>
            </div>
          </div>
          <!-- 按钮 -->
          <div class="grid grid-cols-4 gap-x-20rpx">
            <div
              @click="goPage(item?.url)"
              v-for="item in iconList"
              :key="item.icon"
              class="w-full h200rpx flex flex-col py-32rpx justify-between items-center border-rd-20rpx border-2rpx border-solid border-[#f0d4ba] box-border bg-gradient-linear bg-gradient-[180deg,#f7efff_0%,#ffffff_100%]"
            >
              <wd-img width="80rpx" height="80rpx" :src="item.icon" mode="aspectFill"></wd-img>
              <span class="text-24rpx text-#333333">{{ item.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="px-40rpx w-full box-border pt-20rpx">
        <wd-button @click="goApply" custom-class="!h-96rpx !rd-48rpx !bg-#8445C7" block>
          立即提现
        </wd-button>
      </div>
    </template>
    <template v-if="!isAgent">
      <div class="grid grid-rows-[1fr_auto] grid-cols-1">
        <view
          class="flex flex-col justify-center items-center gap30rpx py50rpx text-#999999 agent-w"
        >
          <text style="font-size: 30rpx; margin-bottom: 50rpx">您还不是推广员，请先提交申请</text>
        </view>
      </div>
      <div class="px-26rpx box-border grid grid-cols-1">
        <div @click="handleMerchartClick">
          <wd-checkbox v-model="merchartCheck" :disabled="!merchartRead" shape="square">
            <div class="flex items-center">
              阅读并同意
              <div class="h-fit flex items-center" @click.stop>
                <wd-button
                  @click="handleOpenPolicy"
                  type="text"
                  custom-class="!p-unset !m-unset !line-height-unset !text-#8445C7"
                >
                  《推广员协议》
                </wd-button>
              </div>
            </div>
          </wd-checkbox>
        </div>
        <wd-button
          type="primary"
          custom-class="!h-96rpx !rd-48rpx !bg-#8445C7"
          size="large"
          @click="applyagent"
          block
        >
          立即申请
        </wd-button>

        <wd-popup
          v-model="privateInfo"
          :closeOnClickModal="false"
          custom-style="border-radius:20rpx;width:80%;max-height:50%"
          @close="privateInfo = !privateInfo"
        >
          <div class="w-full p-40rpx box-border flex flex-col items-center gap-y-20px">
            <wd-img
              width="80rpx"
              height="80rpx"
              src="https://file.shanqianqi.com/image/2025/06/26/f021567f51c64834805330d12a4aafd0.png"
              mode="aspectFill"
            ></wd-img>

            <div v-html="policy?.revenueSharing"></div>

            <div class="w-full">
              <wd-button block @click="privateInfo = !privateInfo">已阅读</wd-button>
            </div>
          </div>
        </wd-popup>
      </div>
    </template>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { userAgent, getPolicy } from '@/service/api/user'

const { safeAreaInsets } = uni.getSystemInfoSync()

const levelUrlMap = {
  2: 'https://file.shanqianqi.com/image/2025/06/12/0d8a6b3d2b4f4c59994094b894947faa.png',
  3: 'https://file.shanqianqi.com/image/2025/06/12/ea101d2670ac4798b96439f96c3d34fb.png',
  4: 'https://file.shanqianqi.com/image/2025/06/12/b3fda5e63d2748b3a15de4b6b5cb34de.png',
  5: 'https://file.shanqianqi.com/image/2025/06/12/3d96ef9cf3294c0d81003683ecc24098.png',
}

const { data: agent, run } = useRequest(() => userAgent())

const isAgent = computed(() => agent?.value?.isAgent)

// 用户信息
const userInfo = computed(() => agent.value?.user)

// 推荐人
const refereeUserNickName = computed(() => agent?.value?.refereeUserNickName)

// 业绩
const countMoney = computed(() => agent.value?.performance?.countMoney ?? 0)
const nextGradeAgentMoney = computed(() => agent.value?.agent?.nextGradeAgentMoney ?? 0)
const diffMoney = computed(() => Number(nextGradeAgentMoney.value) - Number(countMoney.value) || 0)
console.log('🚀 ~ diffMoney:', diffMoney)
const moneyPercent = computed(() => {
  const current = Number(countMoney.value)
  const target = Number(nextGradeAgentMoney.value)
  if (!target || target <= 0) return 0
  return Math.min((current / target) * 100, 100) ?? 0 // 限制最大为 100%
})
console.log('🚀 ~ moneyPercent ~ moneyPercent:', moneyPercent)

// 店铺业绩
const countSupplier = computed(() => agent.value?.performance?.countSupplier ?? 0)
const nextGradeAgentStore = computed(() => agent.value?.agent?.nextGradeAgentStore ?? 0)
const diffStore = computed(
  () => Number(nextGradeAgentStore.value) - Number(countSupplier.value) || 0,
)
const moneyStore = computed(() => {
  const current = Number(countSupplier.value)
  const target = Number(nextGradeAgentStore.value)
  if (!target || target <= 0) return 0
  return Math.min((current / target) * 100, 100) ?? 0 // 限制最大为 100%
})

const iconList = [
  {
    label: '收益明细',
    icon: 'https://file.shanqianqi.com/image/2025/06/12/1f41737987ac44c492cdf4e6893ff6b5.png',
    url: '/pages-my/promotion/distribution/index',
  },
  {
    label: '推广二维码',
    icon: 'https://file.shanqianqi.com/image/2025/06/12/84642128c2b644f8857ffedb3e3fd2a2.png',
    url: '/pages-sub/user/agent/qrcode',
  },
  {
    label: '我的推广',
    icon: 'https://file.shanqianqi.com/image/2025/06/12/a2c744dcc10141ecb1b4dffe0da70139.png',
    url: '/pages-my/promotion/my-promotion/index',
  },
  {
    label: '我的团队',
    icon: 'https://file.shanqianqi.com/image/2025/06/12/28d14e05ef1241d38cc2e0f7822979d3.png',
    url: '/pages-my/promotion/my-team/index',
  },
]

const goDetail = () => {
  uni.navigateTo({ url: '/pages-my/promotion/promotion-detail/index' })
}

const goWithdrawal = (type: number) => {
  uni.navigateTo({ url: `/pages-my/promotion/withdrawal/index?type=${type}` })
}

const goApply = () => {
  uni.navigateTo({ url: '/pages-my/promotion/apply/index' })
}

const goPage = (url: string) => {
  uni.navigateTo({ url })
}

const privateInfo = ref(false)

const hasReadOnce = ref(uni.getStorageSync('hasReadPromotionAgreement') || false)

const merchartRead = computed(() => {
  return hasReadOnce.value
})

const { data: policy, run: fetchPolicy } = useRequest(() => getPolicy())
const handleOpenPolicy = async () => {
  await fetchPolicy()

  privateInfo.value = true
}

watch(
  () => privateInfo.value,
  (v) => {
    if (v) {
      hasReadOnce.value = true
      uni.setStorageSync('hasReadPromotionAgreement', true)
    }
  },
)

onShow(async () => {
  await run()
  if (!isAgent.value && !hasReadOnce.value) {
    await handleOpenPolicy()
  }
})

const merchartCheck = ref(false)

const handleMerchartClick = () => {
  if (!merchartRead.value) {
    uni.showToast({ title: '请阅读推广员协议', icon: 'none' })
  }
}

/*申请分销商*/
const applyagent = () => {
  if (!merchartCheck.value) {
    uni.showToast({ title: '请阅读推广员协议', icon: 'none' })
    return
  }
  uni.navigateTo({ url: '/pages-sub/user/agent/apply' })
}
</script>

<style lang="scss" scoped>
:deep(.wd-navbar__title) {
  color: #ffffff !important;
}
:deep(.wd-navbar__arrow) {
  color: #ffffff !important;
}
// :deep(.wd-progress) {
//   height: 12rpx !important;
// }
// :deep(.wd-progress__inner) {
//   height: 12rpx !important;
// }
</style>

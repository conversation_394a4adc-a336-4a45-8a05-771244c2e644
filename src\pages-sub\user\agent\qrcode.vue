<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '推广二维码',
  },
}
</route>

<template>
  <view class="qrcode flex flex-col items-center bg-#ffffff">
    <view class="qrCode_box flex flex-col items-center justify-center">
      <image style="" :src="qrcodeUrl" mode="widthFix" show-menu-by-longpress></image>
      <view class="pt30rpx pb10rpx text-red text-30rpx">长按图片转发给好友</view>
      <view class="text-red text-30rpx">成功推广得5善豆,上不封顶</view>
    </view>
    <view class="btns-wrap w-full flex gap-50rpx items-center px30rpx box-border">
      <!-- #ifdef MP || APP-PLUS -->
      <button class="w-full bg-red rounded-30rpx text-white" @click="downloadQrcode">
        保存图片
      </button>
      <!-- <button @click="shareImage" class="w-full bg-red rounded-30rpx text-white">转发图片</button> -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { fetchUserAgentQrCode, shareQrocde } from '@/service'
import { onMounted, ref } from 'vue'
import { getPlatform } from '@/utils'

// 响应式数据
const qrcodeUrl = ref('')

// 生命周期 - 组件挂载
onMounted(() => {
  getData()
})

// 获取数据
const getData = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    const { data } = await fetchUserAgentQrCode({
      source: getPlatform(),
      appId: import.meta.env.VITE_APPID,
    })
    uni.hideLoading()
    qrcodeUrl.value = data
  } catch (e) {}
  uni.hideLoading()
}
const shareImage = async () => {
  uni.showLoading({ title: '分享中...' })
  try {
    const tempFilePath = await downloadImageToTempFile(qrcodeUrl.value) // 不转base64了

    wx.showShareImageMenu({
      path: tempFilePath,
      success: () => {
        uni.hideLoading()
        uni.showToast({ title: '分享成功', duration: 1000, icon: 'none' })
        console.log('分享成功')
      },
      fail: (err) => {
        console.error('分享失败', err)
        uni.showToast({ title: '分享失败', duration: 1000, icon: 'none' })
        uni.hideLoading()
      },
    })
  } catch (error) {
    uni.hideLoading()
    console.error('分享图片失败', error)
    uni.showToast({ title: error.message || '分享失败', duration: 1000, icon: 'none' })
  }
}

const downloadImageToTempFile = (imageUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url: imageUrl,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: (err) => {
        reject(new Error('下载图片失败: ' + err.errMsg))
      },
    })
  })
}
// https图片下载 → 转 base64 → 转 ArrayBuffer → 写临时文件 → 分享(base64路径格式图片的处理方式)
// const shareImage = async () => {
//   uni.showLoading({ title: '分享中...' })
//   try {
//     // 1. 下载图片并转换为base64
//     const base64Data = await downloadImageToBase64(qrcodeUrl.value)
//     console.log('base64Data', base64Data)

//     // 2. 将base64写入临时文件
//     const tempFilePath = await writeBase64ToTempFile(base64Data)

//     // 3. 调用微信分享图片菜单
//     wx.showShareImageMenu({
//       path: tempFilePath,
//       success: () => {
//         uni.hideLoading()
//         uni.showToast({ title: '分享成功', duration: 1000, icon: 'none' })
//         console.log('分享成功')
//       },
//       fail: (err) => {
//         console.error('分享失败', err)
//         uni.showToast({ title: '分享失败', duration: 1000, icon: 'none' })
//         uni.hideLoading()
//       },
//     })
//   } catch (error) {
//     uni.hideLoading()
//     console.error('分享图片失败', error)
//     uni.showToast({ title: error.message || '分享失败', duration: 1000, icon: 'none' })
//   }
// }

// 下载图片并转换为base64
const downloadImageToBase64 = (imageUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url: imageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          // 读取下载的文件并转换为base64
          const fs = uni.getFileSystemManager()
          fs.readFile({
            filePath: res.tempFilePath,
            encoding: 'base64',
            success: (readRes) => {
              resolve(readRes.data as string)
            },
            fail: (err) => {
              reject(new Error('读取文件失败: ' + err.errMsg))
            },
          })
        } else {
          reject(new Error('下载失败，状态码: ' + res.statusCode))
        }
      },
      fail: (err) => {
        reject(new Error('下载图片失败: ' + err.errMsg))
      },
    })
  })
}

// 将base64数据写入临时文件
const writeBase64ToTempFile = (base64Data: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const fs = uni.getFileSystemManager()
    const tempFilePath = `${wx.env.USER_DATA_PATH}/share_qrcode_${Date.now()}.png`

    // 将base64转换为ArrayBuffer
    const arrayBuffer = uni.base64ToArrayBuffer(base64Data)

    fs.writeFile({
      filePath: tempFilePath,
      data: arrayBuffer,
      encoding: 'binary',
      success: () => {
        resolve(tempFilePath)
      },
      fail: (err) => {
        reject(new Error('写入临时文件失败: ' + err.errMsg))
      },
    })
  })
}
// 检查并请求相册权限
const checkAlbumPermission = () => {
  return new Promise((resolve, reject) => {
    // 先检查是否已有相册权限
    uni.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          resolve(true) // 已有权限
        } else {
          // 没有权限，请求授权
          uni.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => resolve(true), // 用户允许授权
            fail: () => resolve(false), // 用户拒绝授权
          })
        }
      },
      fail: (err) => reject(err),
    })
  })
}

// 带权限检查的保存图片到相册函数(base64路径格式图片的处理方式)
const saveBase64ToPhotoAlbum = async (base64Data) => {
  // 检查并获取相册权限
  const hasPermission = await checkAlbumPermission()
  if (!hasPermission) {
    throw new Error('用户拒绝了相册权限')
  }

  // 1. 去除Base64头部信息
  const base64 = base64Data.replace(/^data:image\/\w+;base64,/, '')

  // 2. 转换为ArrayBuffer
  const arrayBuffer = uni.base64ToArrayBuffer(base64)

  // 3. 生成临时文件路径
  const filePath = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.png`

  // 4. 写入文件
  const fs = uni.getFileSystemManager()
  return new Promise((resolve, reject) => {
    fs.writeFile({
      filePath,
      data: arrayBuffer,
      encoding: 'binary',
      success: () => {
        // 5. 保存到相册
        uni.saveImageToPhotosAlbum({
          filePath,
          success: () => resolve('保存成功'),
          fail: (err) => reject('保存失败：' + err.errMsg),
        })
      },
      fail: (err) => reject('文件写入失败：' + err.errMsg),
    })
  })
}
// 下载二维码图片并保存到相册
const downloadQrcode = async () => {
  // 检查并获取相册权限
  const hasPermission = await checkAlbumPermission()
  if (!hasPermission) {
    throw new Error('用户拒绝了相册权限')
  }

  uni.showLoading({ title: '加载中' })
  uni.downloadFile({
    url: qrcodeUrl.value,
    success(res) {
      if (res.statusCode === 200) {
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success:  function () {
            uni.showToast({
              title: '图片保存成功',
              duration: 2000,
              icon: 'none',
            })
          },
          fail: function (e) {
            console.log(e)
            // // 处理权限被拒绝的情况
            // if (e.errMsg.includes('auth deny')) {
            //   uni.showModal({
            //     title: '权限申请',
            //     content: '需要相册权限才能保存图片到本地',
            //     confirmText: '去授权',
            //     success: (res) => {
            //       if (res.confirm) {
            //         uni.openSetting({
            //           success: (settingRes) => {
            //             console.log('设置页面返回', settingRes)
            //           },
            //         })
            //       }
            //     },
            //   })
            // } else {
            uni.showToast({
              title: '图片保存失败',
              duration: 2000,
              icon: 'none',
            })
          },
          // },
        })
      }
    },
    fail: (err) => {
      console.error('下载图片失败', err)
      uni.showToast({
        title: '图片下载失败',
        duration: 2000,
        icon: 'none',
      })
    },
    complete: () => {
      uni.hideLoading()
    },
  })
}
//base64图片保存到相册
const savePosterImg = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    // await saveBase64ToPhotoAlbum(qrcodeUrl.value)
    // 5. 保存到相册
    uni.saveImageToPhotosAlbum({
      filePath: qrcodeUrl.value,
      success: () => {
        uni.showToast({ title: '保存成功', duration: 2000, icon: 'success' })
      },
      fail: (err) => {
        uni.showToast({ title: '保存失败', duration: 2000, icon: 'none' })
      },
    })
    uni.showToast({ title: '保存成功', duration: 2000, icon: 'success' })
    console.log('保存图片成功')
  } catch (error) {
    console.error('保存图片失败:', error)
    uni.showToast({ title: error.message || error, duration: 1000, icon: 'none' })

    // 处理权限被拒绝的情况
    if (error.toString().includes('拒绝了相册权限')) {
      uni.showModal({
        title: '权限申请',
        content: '需要相册权限才能保存图片到本地',
        confirmText: '去授权',
        success: (res) => {
          if (res.confirm) {
            uni.openSetting() // 引导用户打开权限设置
          }
        },
      })
    }
  } finally {
    uni.hideLoading()
  }
}
</script>
<style lang="scss" scoped>
.qrcode {
  width: 100%;
  height: 100vh;
}
.btns-wrap {
  flex: 2;
  height: 20vh;
}
.qrCode_box {
  flex: 5;
  image {
    width: 500rpx;
    height: 100%;
  }
}

.tips {
  padding: 30rpx 0;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
}
</style>

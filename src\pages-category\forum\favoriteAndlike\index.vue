<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view>
    <z-paging
      ref="paging"
      empty-view-text="没有点赞哦~"
      :default-page-size="10"
      :paging-style="{ background: '#ffffff', 'padding-bottom': `${bottom || 15}px` }"
      fixed
    >
      <template #top>
        <wd-navbar
          left-arrow
          :bordered="false"
          safeAreaInsetTop
          fixed
          placeholder
          @click-left="handleBack"
        >
          <template #title>
            <view class="">
              <span class="ml-20rpx">获赞/被收藏</span>
            </view>
          </template>
        </wd-navbar>
      </template>
      <div class="pl-25rpx pr-25rpx">
        <div class="overflow-hidden">
          <div
            class="flex bg-#ffffff rounded-15rpx p-15rpx pt-20rpx pb-20rpx justify-between items-center"
          >
            <div class="flex items-center">
              <img src="" alt="" class="w-90rpx h-90rpx rounded-50% bg-#00000080 mr-15rpx" />
              <div class="flex flex-col">
                <span class="text-28rpx text-#333333">名字名字</span>
                <div>
                  <span class="text-24rpx text-#333333">收藏了你的视频</span>
                  <span class="text-24rpx text-#999999 ml-10rpx">2025.5.22 18:00:00</span>
                </div>
              </div>
            </div>
            <div
              class="bg-#ff7d2633 text-#FF7D26 text-22rpx p-10rpx pl-30rpx pr-30rpx rounded-35rpx"
            >
              查看
            </div>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>
<script lang="ts" setup>
// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1, // 返回的页面数
  })
}
</script>
<style lang="scss" scoped>
.flex.items-center {
  &:active {
    opacity: 0.7;
  }
}
</style>

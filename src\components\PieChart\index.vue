<template>
  <view class="charts-box">
    <qiun-data-charts type="pie" :opts="opts" :chartData="chartData" />
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{ data: { name: string; value: number }[] }>()
const opts = {
  legend: {
    fontSize: 14,
    fontColor: '#333333',
    float: 'center',
    itemGap: 40,
  },
  color: ['#B973FF', '#73FFBE', '#FF6A6A', '#FFD573', '#73C5FF'],
  padding: [5, 5, 5, 5],
  enableScroll: false,
  extra: {
    pie: {
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: 0,
      labelWidth: 15,
      border: true,
      borderWidth: 3,
      borderColor: '#FFFFFF',
    },
  },
}

const chartData = computed(() => {
  const res = {
    series: [
      {
        data: props.data,
      },
    ],
  }
  return res
})

// onMounted(() => {
//   nextTick(() => {

//     chartData.value = props.data
//   })
//   // getServerData()
// })

// const getServerData = () => {
//   //模拟从服务器获取数据时的延时
//   setTimeout(() => {
//     //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
//     let res = {
//       series: [
//         {
//           data: [
//             { name: '团队成员1（31%）', value: 31 },
//             { name: '团队成员2（10%）', value: 30 },
//             { name: '团队成员3（12%）', value: 20 },
//             { name: '团队成员4（28%）', value: 18 },
//             { name: '其他成员（29%）', value: 8 },
//           ],
//         },
//       ],
//     }
//     chartData.value = JSON.parse(JSON.stringify(res))
//   }, 500)
// }
</script>

<style scoped>
/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
.charts-box {
  width: 100%;
  height: 300px;
}
</style>

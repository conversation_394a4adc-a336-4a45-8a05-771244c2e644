import type { CustomRequestOptions } from '@/interceptors/request'
import { useUserStore } from '@/store/user'
import { doLogin } from '@/utils'

export const http = <T>(options: CustomRequestOptions) => {
  const userStore = useUserStore()
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    if (!options?.query) options.query = {}
    if (!options?.data) options.data = {}
    if (options.method === 'GET') {
      options.query.appId = import.meta.env.VITE_APPID
      options.query.token = userStore?.token ?? ''
      options.query.env = userStore?.envVersion ?? ''
      // if (import.meta.env.VITE_IS_DEV !== 'true') {
      //   options.query.env = userStore?.envVersion ?? ''
      // }
    } else {
      options.data['appId'] = import.meta.env.VITE_APPID
      options.data['token'] = userStore?.token ?? ''
      options.data['env'] = userStore?.envVersion ?? ''
      // if (import.meta.env.VITE_IS_DEV !== 'true') {
      //   options.data['env'] = userStore?.envVersion ?? ''
      // }
    }
    uni.request({
      ...options,
      header: {
        appId: import.meta.env.VITE_APPID,
        ...options.header,
      },
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 2.1 提取核心数据 res.data
          if ((res.data as IResData<T>)?.code === 1) {
            resolve(res.data as IResData<T>)
          } else if ((res.data as IResData<T>)?.code === -1) {
            userStore.clearUserInfo()

            // // 获取当前页面完整路径（带参数）
            // const pages = getCurrentPages()
            // const currentPage = pages[pages.length - 1]
            // const path = currentPage.route
            // // 兼容不同平台获取页面参数
            // // 微信小程序用 currentPage.options，H5/APP 可能用 currentPage.$page?.options
            // const query =
            //   // @ts-ignore
            //   currentPage.options ||
            //   // @ts-ignore
            //   currentPage.$page?.options ||
            //   {}

            // const queryStr = Object.keys(query)
            //   .map((key) => `${key}=${encodeURIComponent(query[key])}`)
            //   .join('&')

            // const fullPath = `/${path}${queryStr ? `?${queryStr}` : ''}`

            // uni.getStorageSync('invitationId')
            //   ? uni.getStorageSync('invitationId')
            //   : 0
            // uni.getStorageSync('refereeId')
            //   ? uni.getStorageSync('refereeId')
            //   : 0

            // uni.navigateTo({
            //   url: `/pages-sub/login/weblogin?redirect=${encodeURIComponent(fullPath)}`,
            // })

            doLogin()

            return
          } else {
            !options.hideErrorToast &&
              uni.showToast({
                icon: 'none',
                title: (res.data as IResData<T>)?.msg || '请求错误',
              })
            reject(res)
          }
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          // userStore.clearUserInfo()
          // uni.navigateTo({ url: '/pages/login/login' })
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: (res.data as IResData<T>).msg || '请求错误',
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpGet = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
  hideErrorToast?: boolean,
) => {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
    hideErrorToast,
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
  hideErrorToast?: boolean,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
    hideErrorToast,
  })
}
/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpPostForm = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
  hideErrorToast?: boolean,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header: {
      'content-type': 'application/x-www-form-urlencoded',
      ...header,
    },
    hideErrorToast,
  })
}
/**
 * PUT 请求
 */
export const httpPut = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    data,
    query,
    method: 'PUT',
    header,
  })
}

/**
 * DELETE 请求（无请求体，仅 query）
 */
export const httpDelete = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    method: 'DELETE',
    header,
  })
}

http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '售后申请',
  },
}
</route>

<template>
  <view>
    <view class="refund-apply pb100rpx bg-#F8F8F8">
      <form @reset="formReset">
        <view
          class="one-product p30rpx mb20rpx bg-white flex justify-between items-center gap40rpx"
        >
          <view class="w160rpx h160rpx">
            <image :src="product.productImage" mode="aspectFit"></image>
          </view>
          <view class="flex-1">
            <view class="pro-info">{{ product.productName }}</view>
            <view class="pt10rpx p-0-30 text-24rpx gray9">
              <text class="">单价：¥{{ product.linePrice }}</text>
              <text class="ml20rpx">数量：{{ product.totalNum }}</text>
            </view>
          </view>
        </view>

        <!-- 服务类型 -->
        <view
          class="group bg-white p30rpx mb20rpx box-border"
          v-if="product && product.orderM && product.orderM.deliveryType != 30"
        >
          <view class="group-hd" style="1px solid #eeeeee">
            <view class="left pb20rpx">
              <text class="min-name">服务类型</text>
            </view>
          </view>
          <view class="gap20rpx flex items-center text-28rpx">
            <button
              v-if="product.orderM && product.orderM.deliveryType != 30"
              :class="type == 10 ? 'normalBtn  btn-red-border' : 'normalBtn'"
              @click="tabType(10)"
            >
              退货/退款
            </button>
            <button
              v-if="product.orderM && product.orderM.deliveryType != 30"
              :class="type == 20 ? 'normalBtn btn-red-border' : 'normalBtn'"
              @click="tabType(20)"
            >
              换货
            </button>
            <button
              v-if="product.orderM && product.orderM.deliveryType != 30"
              :class="type == 30 ? 'normalBtn btn-red-border' : 'normalBtn'"
              @click="tabType(30)"
            >
              仅退款
            </button>
          </view>
        </view>

        <!--申请原因-->
        <view class="group px30rpx py10rpx mb20rpx bg-white">
          <view class="py20rpx">
            <view class="left">
              <text class="min-name">申请原因</text>
            </view>
          </view>
          <view class="d-s-c" style="border: 1px solid #eeeeee">
            <textarea
              class="w-full p10rpx box-border flex-1 text-24rpx"
              v-model="applyForm.content"
              placeholder="请详细填写申请原因，注意保持商品的完好，建议您先与卖家沟通"
            />
          </view>
        </view>

        <!--退款金额-->
        <view class="" v-if="type == 10 || type == 30">
          <view class="group-hd bg-white p30rpx mb20rpx">
            <view class="left">
              <text class="min-name">退款金额：</text>
              <text class="text-red text-30rpx">¥{{ product.totalPayPrice }}</text>
            </view>
          </view>
        </view>

        <!--上传图片-->
        <view class="group">
          <view class="group-hd px30rpx py20rpx bg-white">
            <view class="left">
              <text class="min-name">上传凭证</text>
              <text class="gray9 text-24rpx text-#999">(最多6张)</text>
            </view>
          </view>
          <view class="upload-list px30rpx mb20rpx bg-white">
            <wd-upload
              :file-list="fileList"
              image-mode="aspectFill"
              :max-size="1"
              :action="action"
              name="iFile"
              :limit="6"
              :form-data="formData"
              @change="handleChange"
              @before-upload="handleBeforeUpload"
              @success="handleSuccess"
              @fail="handleFail"
            ></wd-upload>
          </view>
        </view>
        <!-- <image v-if="fileList.length>0" class="w-160rpx h-160rpx" :src="fileList[0].url"></image> -->
        <view class="flex justify-end items-center p20rpx bg-white">
          <button class="bg-red text-white text-24rpx m0" @click="formSubmit">确认提交</button>
        </view>
      </form>
    </view>
  </view>
</template>

<script lang="ts" setup>
//
import { getEnvBaseUploadUrl } from '@/utils'
import { applyRefund, fetchUserRefundToApply } from '@/service'
const product = ref<any>({})

const type = ref(10)
const applyForm = ref({
  content: '',
  type: 10,
  orderProductId: '',
  images: [],
})
let orderProductId = ref('')

const action = ref(getEnvBaseUploadUrl()) // 等同于 useUpload 中的 VITE_UPLOAD_BASEURL
const fileList = ref([]) // 文件列表
const formData = ref({
  appId: import.meta.env.VITE_APPID,
  timestamp: Date.now(),
})
const mpState = ref(1)
const temlIds = ref('TEMPLATE_ID')

const tabType = (selectedType) => {
  type.value = selectedType
}
onLoad((e) => {
  orderProductId.value = e.orderProductId
})
onMounted(() => {
  getData()
})
const getData = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    let { data } = await fetchUserRefundToApply({
      appId: import.meta.env.VITE_APPID,
      orderProductId: Number(orderProductId.value),
      platform: 'wx',
    })
    product.value = data.Apply
    uni.hideLoading()
  } catch (err) {
    uni.hideLoading()
  }
}
//更新 fileList
const handleChange = ({ fileList: files }) => {
  console.log('file', files)

  // fileList.value = files
}
const handleBeforeUpload = (file) => {
  console.log('准备上传:', file)
  uni.showLoading({ title: '图片上传中' })
}
const handleSuccess = (res: any) => {
  console.log('上传成功:', res) // 假设 res 包含了上传后的文件路径信息
  if (res.file.response) {
    const response = JSON.parse(res.file.response)
    console.log('response', response)
    const newFile = {
      uid: response.data.fileId, // 使用组件生成的 uid
      name: response.data.fileId, // 保留原文件名
      url: response.data.filePath, // 服务端返回的文件路径
      thumb: response.data.filePath, // 服务端返回的文件路径
      response: res.file.response,
      status: 'success', // 标记为上传成功
    }
    fileList.value.push(newFile)
  }

  // const url = JSON.parse(res.data)?.data?.filePath
  uni.hideLoading()
  uni.showToast({ title: '上传成功', duration: 1000 })
}

const handleFail = (err) => {
  console.error('上传失败:', err)
  uni.showToast({ title: '上传失败', icon: 'none', duration: 1000 })
  uni.hideLoading()
}
const formSubmit = async () => {
  console.log('fileList.value', fileList.value)
  let fileIdList = fileList.value.map((ele) => ({
    fileId: ele.uid,
    filePath: ele.url,
  }))
  try {
    let res = await applyRefund({
      appId: import.meta.env.VITE_APPID,
      type: type.value,
      images: fileIdList,
      orderProductId: Number(orderProductId.value),
      content: applyForm.value.content,
    })
    uni.showToast({ title: res.msg || '提交成功' })
    uni.navigateTo({ url: `/pages-sub/user/refund/index?tab=1` })
  } catch (err) {
    uni.showToast({ title: '提交失败,请重试' })
  }
}

const formReset = () => {
  applyForm.value.content = ''
  fileList.value = []
  type.value = 10
}
</script>

<style lang="scss" scoped>
//
.btn-red-border {
  border: 1px solid #f6220c;
  color: #f6220c;
}
.normalBtn {
  margin: 0;
  font-size: 28rpx;
}
</style>

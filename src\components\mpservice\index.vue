<template>
  <div class="grid grid-cols-1 p-30rpx box-border gap-y-10px">
    <view class="mpservice-wrap" v-if="!isloding">
      <view
        class="noDatamodel"
        v-if="
          dataModel == null ||
          (dataModel.qq == '' && dataModel.wechat == '' && dataModel.phone == '')
        "
      >
        该商家尚未设置客服
      </view>
      <template v-if="dataModel != null">
        <view v-if="dataModel.qq != ''" class="data-model" @click="copyQQ(dataModel.qq)">
          <image
            src="https://file.shanqianqi.com/image/2025/06/24/3fc6ee7db49048c9aca1edd945531c4f.png"
            mode="widthFix"
            class="imgIcon"
          ></image>
          <view class="p-0-30 flex-1">
            <view>官方QQ</view>
            <view class="desc">{{ dataModel.qq }}</view>
          </view>
          <text class="txt">点击复制</text>
        </view>
        <view v-if="dataModel.wechat != ''" class="data-model" @click="copyQQ(dataModel.qq)">
          <image
            src="https://file.shanqianqi.com/image/2025/06/24/8cc4e5ed7a4c4decad74c16ff67dc77b.png"
            mode="widthFix"
            class="imgIcon"
          ></image>
          <view class="p-0-30 flex-1">
            <view>官方微信</view>
            <view class="desc">{{ dataModel.wechat }}</view>
          </view>
          <text class="txt">点击复制</text>
        </view>
        <view v-if="dataModel.phone != ''" class="data-model" @click="callPhone(dataModel.phone)">
          <image
            src="https://file.shanqianqi.com/image/2025/06/24/91d40f2fd078469a9c63a6e8d4a522eb.png"
            mode="widthFix"
            class="imgIcon"
          ></image>
          <view class="p-0-30 flex-1">
            <view>官方热线</view>
            <view class="desc">{{ dataModel.phone }}</view>
          </view>
          <text class="txt">一键拨打</text>
        </view>
      </template>
    </view>
  </div>
</template>
<script setup>
import { copyQQ, callPhone } from '@/utils'
import { fetchMpService } from '@/service'
const props = defineProps({
  shopSupplierId: String,
})

const isloding = ref(false)
const dataModel = ref({
  wechat: '',
  qq: '',
  phone: '',
})
const getData = async () => {
  try {
    isloding.value = true
    const res = await fetchMpService({ shopSupplierId: props.shopSupplierId })
    dataModel.value = res.data
  } finally {
    isloding.value = false
  }
}

onMounted(() => {
  getData()
})
</script>
<style lang="scss" scoped>
.imgIcon {
  width: 80rpx;
  height: auto;
}
.txt {
  width: 140rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  color: #fff;
  background: #d9b47f;
  text-align: center;
  border-radius: 30rpx;
}
.desc {
  font-size: 28rpx;
  color: #666;
  letter-spacing: 1rpx;
}
.mpservice-wrap {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}
.data-model {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  width: 100%;
  font-size: 34rpx;
  border-bottom: 1rpx solid #eeeeee;
}
.mpservice-wrap .mp-image {
  width: 560rpx;
  margin-top: 40rpx;
}
.mpservice-wrap .mp-image image {
  width: 100%;
}
.icon-qq {
  color: #1296db;
  font-size: 64rpx;
}
.icon-weixin {
  color: #1afa29;
  font-size: 64rpx;
}
.icon-guanbi {
  font-size: 26rpx;
}
.icon-002dianhua {
  color: #1296db;
  font-size: 52rpx;
}
.kf-close {
  justify-content: flex-end;
}
.noDatamodel {
  font-size: 30rpx;
  width: 100%;
  text-align: center;
  height: 200rpx;
  line-height: 128rpx;
  color: #929292;
}
</style>

<template>
	<view class="demo-block">
		<text class="demo-block__title-text ultra">浮动气泡</text>
		<text class="demo-block__desc-text">浮动气泡</text>
		<view class="demo-block__body">
			<view class="demo-block">
				<!-- <text class="demo-block__title-text large">更小标题</text> -->
				<view class="demo-block__body">
					<view class="tabs">
						<text class="tab" :class="{active: active == 0}" @click="change(0)">基础用法</text>
						<text class="tab" :class="{active: active == 1}" @click="change(1)">自由磁吸</text>
						<text class="tab" :class="{active: active == 2}" @click="change(2)">控制位置</text>
					</view>
					<template v-if="active == 0">
						<text class="text">在 x 轴默认位置，允许 y 轴方向拖拽</text>
						<l-fab @click="onClick">
							<text style="color: white;">拖</text>
						</l-fab>
					</template>
					<template v-if="active == 1">
						<text class="text">允许 x 和 y 轴方向拖拽，吸附到 x 轴方向最近一边</text>
						<l-fab axis="xy" magnetic="x"><text style="color: white;">拖</text></l-fab>
					</template>
					<template v-if="active == 2">
						<text class="text">使用 offset 控制位置, x:{{offset[0]}}, y:{{offset[1]}}</text>
						<l-fab v-model:offset="offset" axis="xy"><text style="color: white;">拖</text></l-fab>
					</template>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				active: 0,
				offset: [200, 200]
			}
		},
		methods: {
			onClick() {

			},
			change(index : number) {
				this.active = index
			}
		}
	}
</script>
<style lang="scss">
	.tabs {
		background-color: white;
		flex-direction: row;
		justify-content: center;
	}

	.tab {
		padding: 40rpx 46rpx;
		color: rgba(0, 0, 0, 0.6);
	}

	.active {
		color: rgba(0, 0, 0, 1);
	}

	.text {
		margin: 40rpx 30rpx;
		color: rgba(0, 0, 0, 0.6);
	}

	.demo-block {
		margin: 32px 20px 0;
		overflow: visible;

		&__title {
			margin: 0;
			margin-top: 8px;

			&-text {
				color: rgba(0, 0, 0, 0.6);
				font-weight: 400;
				font-size: 14px;
				line-height: 16px;

				&.large {
					color: rgba(0, 0, 0, 0.9);
					font-size: 18px;
					font-weight: 700;
					line-height: 26px;
				}

				&.ultra {
					color: rgba(0, 0, 0, 0.9);
					font-size: 24px;
					font-weight: 700;
					line-height: 32px;
				}
			}
		}

		&__desc-text {
			color: rgba(0, 0, 0, 0.6);
			margin: 8px 16px 0 0;
			font-size: 14px;
			line-height: 22px;
		}

		&__body {
			margin: 16px 0;
			overflow: visible;

			.demo-block {
				// margin-top: 0px;
				margin: 0;
			}
		}
	}
</style>
<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full flex flex-col box-border">
    <SimpleNavBar title="搜索结果" />
    <z-paging
      :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
      ref="paging"
      v-model="dataList"
      :default-page-size="20"
      :auto="autoQuery"
      :fixed="false"
      :refresher-enabled="autoQuery"
      :loading-more-enabled="autoQuery"
      class="flex-1 of-hidden"
      @query="queryList"
    >
      <template #top>
        <div class="grid grid-cols-1 p-10px box-border bg-#ffffff">
          <div class="flex items-center gap-x-28rpx h60rpx">
            <div
              class="flex items-center h-full bg-#f2f2f2 of-hidden flex-1 box-border px-20rpx gap-x-10rpx rd-30rpx"
            >
              <wd-icon name="search" size="28rpx" color="#999999"></wd-icon>
              <wd-input
                inputmode="search"
                v-model="searchVal"
                @confirm="handleSearch"
                @clear="handleSearch"
                no-border
                placeholder="搜索商品"
                clearable
                confirm-type="search"
                placeholderClass="text-#999999"
                custom-class="search !flex-1 !text-#999999 !h-full !flex !items-center !text-24rpx !bg-#f2f2f2"
              ></wd-input>
            </div>
          </div>
          <!-- <div v-if="autoQuery" class="bg-#ffffff flex items-center">
            <wd-tabs @change="handleTabChange" v-model="tab" auto-line-width>
              <block v-for="item in tabs" :key="item.key">
                <wd-tab :title="`${item.label}`" :name="`${item.label}`"></wd-tab>
              </block>
            </wd-tabs>
            <wd-sort-button title="价格" allow-reset />
          </div> -->
        </div>
      </template>
      <div
        class="grid grid-cols-1 mt10px gap-y-20px p10px box-border"
        :class="`${dataList.length > 0 ? 'bg-#ffffff' : ''}`"
      >
        <template v-if="searchType === 'product' || optionSearch">
          <div
            @click="() => goGoodsDetail(item)"
            v-for="item in dataList"
            :key="item.productId"
            class="flex items-center gap-x-10px border-b border-b-solid border-b-#eeeeee pb-10px box-border"
          >
            <div class="w-210rpx h-210rpx rd-25rpx of-hidden relative">
              <wd-img
                :src="item?.productImage"
                width="210rpx"
                height="210rpx"
                mode="aspectFill"
              ></wd-img>
              <div
                v-if="item?.productStock <= 0"
                class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center"
              >
                <span>售罄</span>
              </div>
            </div>

            <div class="flex flex-col justify-between h-full flex-1">
              <div class="flex flex-col gap-y-16rpx">
                <span class="text-28rpx text-#333333 line-clamp-1">{{ item?.productName }}</span>
                <!-- <span class="text-24rpx text-#999999">累计成交：{{ item?.productSales }}笔</span> -->
              </div>
              <div class="text-#999999 line-through text-20rpx">¥{{ item?.linePrice }}</div>

              <div class="flex items-end gap-x-16rpx">
                <div class="flex gap-30rpx items-baseline text-24rpx text-#ff4c01">
                  <span>
                    ¥
                    <span class="text-32rpx font-bold">{{ item?.productPrice }}</span>

                    <span
                      v-if="item?.extraRedPocketPrice && item?.extraRedPocketPrice !== '0.00'"
                      class="text-#ff4c01 text-20rpx fw-400"
                    >
                      + {{ item?.extraRedPocketPrice }}红包
                    </span>
                  </span>
                  <span class="text-20rpx text-#666666">
                    已售{{ formatLargeNumber(item?.productSales) }}+
                  </span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="searchType === 'store' || optionStoreSearch">
          <div
            v-for="item in dataList"
            :key="item?.storeId"
            class="flex gap-x-8px"
            @click="goSupplierDetail(item?.shopSupplierId, item?.storeId)"
          >
            <wd-img
              width="160rpx"
              custom-class="aspect-square"
              mode="aspectFill"
              radius="10px"
              :src="item?.logoFilePath"
            ></wd-img>
            <div class="flex-1 grid grid-cols-1 gap-y-13px">
              <div class="grid grid-cols-1 gap-y-2px">
                <div class="flex items-center justify-between gap-x-5px">
                  <span
                    v-if="item?.storeType === 30"
                    class="text-14px text-#333333 font-500 flex-1 line-clamp-1"
                  >
                    {{ item?.supplierName }}({{ item?.storeName }})
                  </span>
                  <span v-else class="text-14px text-#333333 font-500 flex-1 line-clamp-1">
                    {{ item?.storeName }}
                  </span>
                  <span class="text-#FF2828 text-20rpx">
                    {{ item?.status ? '营业中' : '停业中' }}
                  </span>
                </div>
                <div class="flex items-center gap-x-5px">
                  <wd-rate
                    :modelValue="
                      item?.serverScore ? getScore(item?.serverScore, 1) : getScore(5, 1)
                    "
                    readonly
                    active-color="#FF2828"
                  />
                  <span class="text-20rpx text-#FF2828 font-500">{{ item?.serverScore }}</span>
                </div>
                <span class="text-24rpx text-#999999 line-clamp-1">
                  {{ item?.area?.replace(/\//g, '') }}{{ item?.address }}
                </span>
              </div>
              <div class="flex items-center justify-between">
                <div v-if="false" class="flex items-center">
                  <div
                    class="bg-#FF7D26 rd-2px text-24rpx p2px box-border flex items-center justify-center text-#803909"
                  >
                    积
                  </div>
                  <div
                    class="text-24rpx bg-#FFC39B text-#FF8533 rd-tr-2px rd-br-2px p2px box-border"
                  >
                    最高返{{ item?.supplierCommissionRate }}%
                  </div>
                </div>
                <div></div>
                <div class="flex items-end">
                  <div class="i-carbon-location text-14px text-#333333"></div>
                  <span class="text-#999999 text-20rpx">
                    {{ item?.distance ? `${(item?.distance).toFixed(2)}km` : '' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { fetchRecGoods, fetchIndexStore } from '@/service'
import { useSearchStore } from '@/store/search'
import { removePunctuation, getScore, formatLargeNumber } from '@/utils'
import { useLocationStore } from '@/store/location'

const locationStore = useLocationStore()

const { safeAreaInsets } = uni.getSystemInfoSync()

const searchStore = useSearchStore()

const searchVal = ref('')

const searchType = ref('')

const tabs = ref([
  { label: '综合', key: 'all' },
  { label: '销量', key: 'sales' },
  { label: '价格', key: 'price' },
])
const tab = ref('综合')

const tabIndex = computed(() => tabs.value.findIndex((item) => item.label === tab.value))

const paging = ref()
const autoQuery = ref(true)
const dataList = ref<Api.Home.GoodItem[] & Api.Home.HomeStoreListItem[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    if (searchFrom.value === 'mall' || searchType.value === 'product') {
      const { data: goodData } = await fetchRecGoods(
        {
          appId: import.meta.env.VITE_APPID,
          pageIndex: pageNo,
          pageSize,
          sortPrice: 1,
          search: searchVal.value,
          sortType: tabs.value[tabIndex.value]?.key,
        },
        { appId: import.meta.env.VITE_APPID },
      )
      paging.value.complete(goodData?.records)
    } else {
      const { data: store } = await fetchIndexStore({
        pageIndex: pageNo,
        pageSize,
        longitude: locationStore?.locationInfo?.location?.lon,
        latitude: locationStore?.locationInfo?.location?.lat,
        keyWord: searchVal.value,
      })

      paging.value.complete(store?.records)
    }
  } catch (err) {
    paging.value.complete(false)
  }
}

const optionSearch = ref('')
const optionStoreSearch = ref('')

const searchFrom = ref('')

onLoad((option) => {
  if (option?.search) {
    if (option?.from === 'mall') {
      searchFrom.value = 'mall'
      optionSearch.value = option?.search
    } else {
      searchFrom.value = 'store'
      optionStoreSearch.value = option?.search
    }
    searchVal.value = option?.search
    paging.value?.reload()
  } else if (option?.searchType) {
    searchType.value = option?.searchType
    autoQuery.value = false
    searchVal.value = removePunctuation(searchStore.searchKeyword) || ''
    nextTick(() => {
      paging.value?.complete(searchStore.goodsList ?? [])
    })
  }
})

const goGoodsDetail = (item: Api.Home.GoodItem) => {
  if (item?.productStock <= 0) return
  uni.navigateTo({
    url: `/pages-sub/goods/detail/index?id=${item?.productId}&from=${searchFrom.value}`,
  })
}

const goSupplierDetail = (shopSupplierId, storeId) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${shopSupplierId}&storeId=${storeId}`,
  })
}

const handleSearch = () => {
  paging.value?.reload()
}

const handleTabChange = () => {
  paging.value?.reload()
}
</script>

<style lang="scss" scoped>
//
</style>

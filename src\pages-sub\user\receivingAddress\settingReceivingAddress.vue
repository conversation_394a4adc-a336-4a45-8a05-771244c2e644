<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '地址编辑',
  },
}
</route>
<template>
  <view class="address-form">
    <!-- 智能解析按钮 -->
    <view class="flex justify-center items-center py28rpx">
      <view
        @click="showSmartParsePopup"
        class="flex rounded-48rpx px34rpx py22rpx gap20rpx"
        style="border: 2rpx solid #ff7d26"
      >
        <image
          src="https://file.shanqianqi.com/image/2025/06/28/7e8a2bbfa9424705bed1bff19c227df6.png"
          class="w-40rpx h-40rpx"
        />
        <span class="text-#000000 text-28rpx">智能粘贴</span>
      </view>
    </view>

    <form @submit="formSubmit">
      <view class="bg-white text-32rpx px30rpx" style="font-family: UICTFontTextStyleBody">
        <view class="flex items-center" style="border-bottom: 1rpx solid #eeeeee">
          <text class="w-140rpx">收货人</text>
          <input
            class="flex-1 py30rpx"
            name="name"
            type="text"
            v-model="address.name"
            placeholder-class="grary9"
            placeholder="请输入收货人姓名"
          />
        </view>
        <view class="flex items-center" style="border-bottom: 1rpx solid #eeeeee">
          <text class="w-140rpx">联系方式</text>
          <input
            class="flex-1 py30rpx"
            name="phone"
            type="text"
            v-model="address.phone"
            placeholder-class="grary9"
            placeholder="请输入收货人手机号"
          />
        </view>
        <view class="flex items-center" style="border-bottom: 1rpx solid #eeeeee">
          <text class="w-140rpx">所在地区</text>
          <view class="input-box flex-1" @click="showMulLinkageThreePicker">
            <input
              class="flex-1 py30rpx"
              type="text"
              placeholder-class="grary9"
              placeholder=""
              v-model="selectCity"
              disabled
              style="pointer-events: none"
            />
          </view>
        </view>
        <view class="flex items-center" style="border-bottom: 1rpx solid #eeeeee">
          <text class="w-140rpx">详细地址</text>
          <textarea
            class="flex-1 py30rpx h-auto max-h-288rpx"
            name="detail"
            placeholder-class="grary9"
            :auto-height="true"
            v-model="address.detail"
            placeholder="请输入街道小区楼牌号等"
          ></textarea>
        </view>
      </view>
      <view class="p30">
        <button form-type="submit" class="bg-#ff5704 add-btn">保存</button>
      </view>
    </form>
    <mpvue-city-picker
      v-if="isLoad"
      ref="mpvueCityPickerRef"
      :province="province"
      :city="city"
      :area="area"
      :pickerValueDefault="cityPickerValueDefault"
      @onConfirm="onConfirm"
    />

    <!-- 智能解析弹窗组件 -->
    <AddressParsePopup v-model="showParsePopup" @confirm="handleParseConfirm" />
  </view>
</template>

<script setup lang="ts">
import mpvueCityPicker from '@/components/mpvue-citypicker/mpvueCityPicker.vue'
import AddressParsePopup from './components/AddressParsePopup/index.vue'
import { userAddressDetail, userAddressEdit, fetchUserAddressList } from '@/service'
import { useUserStore } from '@/store'
import { onMounted, reactive, ref } from 'vue'
import { useConfirmOrderStore } from '@/store/confirm-order'

const confirmOrderStore = useConfirmOrderStore()
const userStore = useUserStore()

// 组件引用声明
defineComponent({
  components: { mpvueCityPicker },
})

// 地址表单类型
interface AddressForm {
  name: string
  phone: string
  detail: string
}

// 地区选择结果类型
interface RegionResult {
  label: string
  cityCode: number[]
}

// 响应式数据
const addressId = ref<number>(0)
let address = reactive<AddressForm>({
  name: '',
  phone: '',
  detail: '',
})
const selectCity = ref('选择省,市,区')
const provinceId = ref(0)
const cityId = ref(0)
const regionId = ref(0)
const province = ref<any[]>([]) // 实际应替换为具体类型
const city = ref<any[]>([])
const area = ref<any[]>([])
const isLoad = ref(false)
const cityPickerValueDefault = ref([0, 0, 0])

// 智能解析相关数据
const showParsePopup = ref(false)

// 引用子组件
const mpvueCityPickerRef = ref<InstanceType<typeof mpvueCityPicker>>()

// 生命周期钩子
onLoad((e: any) => {
  addressId.value = e.addressId || 0
})

onMounted(() => {
  getData()
})

// 获取地址数据
const getData = async () => {
  const { data } = await userAddressDetail({
    token: userStore.token,
    appId: import.meta.env.VITE_APPID,
    addressId: addressId.value,
  })

  address = data.detail
  provinceId.value = data.detail.provinceId
  cityId.value = data.detail.cityId
  regionId.value = data.detail.regionId
  selectCity.value = `${data.detail.region.province}${data.detail.region.city}${data.detail.region.region}`
  province.value = data.regionData[0]
  city.value = data.regionData[1]
  area.value = data.regionData[2]
  isLoad.value = true
}

// 提交表单
const formSubmit = async (e: any) => {
  const formdata = e.detail.value
  formdata.provinceId = provinceId.value
  formdata.cityId = cityId.value
  formdata.regionId = regionId.value
  formdata.addressId = addressId.value
  formdata.appId = import.meta.env.VITE_APPID

  // 表单验证
  if (!formdata.name) {
    uni.showToast({ title: '请输入收货人姓名', icon: 'none' })
    return
  }
  if (!formdata.phone || !/^1[3-9]\d{9}$/.test(formdata.phone)) {
    uni.showToast({ title: '请输入正确的手机号码', icon: 'none' })
    return
  }
  if (!provinceId.value || !cityId.value || !regionId.value) {
    uni.showToast({ title: '请选择完整省市区', icon: 'none' })
    return
  }

  // 模拟提交请求
  uni.showLoading({ title: '保存中...' })

  await userAddressEdit(formdata)
  const { data } = await fetchUserAddressList({
    token: userStore.token,
    appId: import.meta.env.VITE_APPID,
  })
  confirmOrderStore.resetSelectedAddressId()
  setTimeout(() => {
    confirmOrderStore.setSelectedAddressId(data.defaultId)
  }, 500)

  setTimeout(function () {
    uni.hideLoading()
  }, 500)
  uni.showToast({ title: '保存成功', icon: 'none' })

  // 跳转逻辑
  uni.navigateBack({ delta: addressId.value ? 1 : 2 })
}

// 显示三级联动选择器
const showMulLinkageThreePicker = () => {
  console.log('11111')

  mpvueCityPickerRef.value?.show()
}

// 选择地区回调
const onConfirm = (e: RegionResult) => {
  const [provinceName, cityName, regionName] = e.label.split(',')
  selectCity.value = `${provinceName}${cityName}${regionName}`
  provinceId.value = e.cityCode[0]
  cityId.value = e.cityCode[1]
  regionId.value = e.cityCode[2]
}

// 显示智能解析弹窗
const showSmartParsePopup = () => {
  showParsePopup.value = true
}

// 处理解析确认结果
const handleParseConfirm = (result: any) => {
  // 填入表单基本信息
  if (result.name) {
    address.name = result.name
  }
  if (result.phone) {
    address.phone = result.phone
  }
  if (result.detail) {
    address.detail = result.detail
  }

  // 处理地区信息
  if (result.region) {
    const regionInfo = parseRegionFromString(result.region)
    if (regionInfo) {
      // 设置显示文本
      selectCity.value = `${regionInfo.provinceName}${regionInfo.cityName}${regionInfo.regionName}`

      // 设置ID值
      provinceId.value = regionInfo.provinceId
      cityId.value = regionInfo.cityId
      regionId.value = regionInfo.regionId

      // 先隐藏组件
      isLoad.value = false

      // 等待组件完全销毁后再设置新值并重新显示
      setTimeout(() => {
        // 设置新的默认值
        cityPickerValueDefault.value = [
          regionInfo.provinceIndex,
          regionInfo.cityIndex,
          regionInfo.regionIndex,
        ]
        console.log('已设置新的默认值:', cityPickerValueDefault.value)

        // 重新显示组件
        setTimeout(() => {
          isLoad.value = true
          console.log('地址选择器重新加载完成，传入的默认值:', cityPickerValueDefault.value)
        }, 50)
      }, 50)
    }
  }
}

// 从地区字符串解析出对应的省市区信息
const parseRegionFromString = (regionStr: string) => {
  try {
    // 提取省市区名称
    const { provinceName, cityName, regionName } = extractRegionNames(regionStr)

    if (!provinceName) {
      console.warn('未能识别省份名称:', regionStr)
      return null
    }

    // 在省份数据中查找匹配项
    console.log('查找省份:', provinceName, '在数据中...')
    let provinceIndex = province.value.findIndex((p: any) => p.label === provinceName)
    console.log('精确匹配结果:', provinceIndex)

    if (provinceIndex === -1) {
      // 尝试模糊匹配
      console.log('尝试模糊匹配...')
      provinceIndex = province.value.findIndex(
        (p: any) =>
          p.label.includes(provinceName.replace(/省|市|自治区|特别行政区/, '')) ||
          provinceName.includes(p.label.replace(/省|市|自治区|特别行政区/, '')),
      )
      console.log('模糊匹配结果:', provinceIndex)
    }

    if (provinceIndex === -1) {
      console.warn(
        '未找到省份:',
        provinceName,
        '可用省份:',
        province.value.slice(0, 10).map((p: any) => p.label),
      )
      return null
    }

    console.log('最终找到省份索引:', provinceIndex, '对应数据:', province.value[provinceIndex])

    const provinceData = province.value[provinceIndex]
    const provinceId = provinceData.value

    // 在城市数据中查找匹配项
    let cityIndex = 0
    let cityId = 0
    let actualCityName = cityName

    if (cityName && city.value[provinceIndex]) {
      const cityList = city.value[provinceIndex]
      cityIndex = cityList.findIndex((c: any) => c.label === cityName)

      if (cityIndex === -1) {
        // 如果没找到完全匹配的，尝试模糊匹配
        cityIndex = cityList.findIndex(
          (c: any) =>
            c.label.includes(cityName.replace('市', '')) ||
            cityName.includes(c.label.replace('市', '')),
        )
        if (cityIndex === -1) {
          console.warn(
            '未找到城市:',
            cityName,
            '在省份:',
            provinceName,
            '可用城市:',
            cityList.map((c: any) => c.label),
          )
          cityIndex = 0 // 默认选择第一个城市
        }
      }

      if (cityList[cityIndex]) {
        cityId = cityList[cityIndex].value
        actualCityName = cityList[cityIndex].label
      }
    }

    // 在区县数据中查找匹配项
    let regionIndex = 0
    let regionId = 0
    let actualRegionName = regionName

    if (regionName && area.value[provinceIndex] && area.value[provinceIndex][cityIndex]) {
      const regionList = area.value[provinceIndex][cityIndex]
      regionIndex = regionList.findIndex((a: any) => a.label === regionName)

      if (regionIndex === -1) {
        // 如果没找到完全匹配的，尝试模糊匹配
        regionIndex = regionList.findIndex(
          (a: any) =>
            a.label.includes(regionName.replace(/区|县|市/, '')) ||
            regionName.includes(a.label.replace(/区|县|市/, '')),
        )
        if (regionIndex === -1) {
          console.warn(
            '未找到区县:',
            regionName,
            '在城市:',
            actualCityName,
            '可用区县:',
            regionList.map((a: any) => a.label),
          )
          regionIndex = 0 // 默认选择第一个区县
        }
      }

      if (regionList[regionIndex]) {
        regionId = regionList[regionIndex].value
        actualRegionName = regionList[regionIndex].label
      }
    }

    return {
      provinceName: provinceData.label,
      cityName: actualCityName,
      regionName: actualRegionName,
      provinceId,
      cityId,
      regionId,
      provinceIndex,
      cityIndex,
      regionIndex,
    }
  } catch (error) {
    console.error('解析地区信息失败:', error)
    return null
  }
}

// 从地区字符串中提取省市区名称
const extractRegionNames = (regionStr: string) => {
  let provinceName = ''
  let cityName = ''
  let regionName = ''

  // 移除街道信息
  const cleanRegion = regionStr.replace(/街道.*$/, '').trim()

  console.log('开始解析地区字符串:', cleanRegion)

  // 更精确的省份匹配 - 使用边界匹配确保准确性
  // 先匹配直辖市
  const municipalityRegex = /^(北京市|上海市|天津市|重庆市)/
  const municipalityMatch = cleanRegion.match(municipalityRegex)
  if (municipalityMatch) {
    provinceName = municipalityMatch[1]
  } else {
    // 匹配省份和自治区 - 使用更严格的边界
    const provinceRegex = /^([\u4e00-\u9fa5]{2,}?(?:省|自治区))/
    const provinceMatch = cleanRegion.match(provinceRegex)
    if (provinceMatch) {
      provinceName = provinceMatch[1]
    } else {
      // 匹配特别行政区
      const sarRegex = /^([\u4e00-\u9fa5]{2,}?特别行政区)/
      const sarMatch = cleanRegion.match(sarRegex)
      if (sarMatch) {
        provinceName = sarMatch[1]
      }
    }
  }

  // 匹配城市（在省份之后，排除直辖市的情况）
  if (provinceName && !['北京市', '上海市', '天津市', '重庆市'].includes(provinceName)) {
    const remainAfterProvince = cleanRegion.replace(provinceName, '')
    const cityRegex = /^([\u4e00-\u9fa5]{2,}?(?:市|州|盟|地区))/
    const cityMatch = remainAfterProvince.match(cityRegex)
    if (cityMatch) {
      cityName = cityMatch[1]
    }
  }

  // 匹配区县（在城市之后）
  let searchText = cleanRegion
  if (provinceName) searchText = searchText.replace(provinceName, '')
  if (cityName) searchText = searchText.replace(cityName, '')

  const regionRegex = /^([\u4e00-\u9fa5]{2,}?(?:区|县|旗))/
  const regionMatch = searchText.match(regionRegex)
  if (regionMatch) {
    regionName = regionMatch[1]
  } else {
    // 如果没有匹配到区县，可能是县级市
    const countyRegex = /^([\u4e00-\u9fa5]{2,}?市)/
    const countyMatch = searchText.match(countyRegex)
    if (countyMatch) {
      regionName = countyMatch[1]
    }
  }

  console.log('提取的地区名称:', {
    provinceName,
    cityName,
    regionName,
    original: regionStr,
    cleanRegion,
    searchText,
    steps: {
      step1_cleanRegion: cleanRegion,
      step2_afterProvince: provinceName ? cleanRegion.replace(provinceName, '') : cleanRegion,
      step3_afterCity: cityName
        ? cleanRegion.replace(provinceName, '').replace(cityName, '')
        : cleanRegion.replace(provinceName, ''),
      step4_finalSearch: searchText,
    },
  })

  return { provinceName, cityName, regionName }
}
</script>

<style lang="scss" scoped>
/* 样式保持与原代码一致，无需修改 */
page {
  background-color: #ffffff;
}

.address-form {
  border-top: 16rpx solid #f2f2f2;
}

.address-form .key-name {
  width: 140rpx;
  font-size: 32rpx;
}
.add-btn {
  color: white;
  position: fixed;
  left: 24rpx;
  bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  margin: 20rpx auto;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 692rpx;
  height: 88rpx;
  border-radius: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: 800;
}
.address-form .btn-red {
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  box-shadow: 0 8rpx 16rpx 0 rgba(226, 35, 26, 0.6);
}

.addBtn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}
</style>

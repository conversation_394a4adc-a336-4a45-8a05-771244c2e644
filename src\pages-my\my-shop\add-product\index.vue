<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
    class="size-full grid grid-rows-[auto_1fr_auto] px11px gap-y-10px box-border of-auto"
  >
    <SimpleNavBar title="添加商品" />
    <div class="of-auto flex flex-col gap-y-20px">
      <wd-form ref="form" :model="formModel" :rules="rules">
        <wd-cell-group title="基础信息" border>
          <!-- 商品名称 -->
          <wd-input
            label="商品名称"
            label-width="100px"
            :maxlength="60"
            show-word-limit
            prop="productName"
            required
            clearable
            v-model="formModel.productName"
            placeholder="例如:【五双装】韩版复古女袜"
          />
          <!-- 商品图片 -->
          <wd-cell required title="商品图片" title-width="100px" prop="image">
            <wd-upload
              :file-list="showProductImg"
              :upload-method="customUpload"
              name="iFile"
              @change="handleFileChange"
              accept="image"
              :action="VITE_UPLOAD_BASEURL"
            ></wd-upload>
          </wd-cell>
          <!-- 商品视频 -->
          <wd-cell title="商品视频" title-width="100px" prop="videoId">
            <wd-upload
              :file-list="showVideo"
              :upload-method="customVideoUpload"
              name="iFile"
              :limit="1"
              @change="handleVideoChange"
              accept="video"
              :action="VITE_UPLOAD_BASEURL"
            ></wd-upload>
          </wd-cell>
          <!-- 视频封面 -->
          <wd-cell title="视频封面" title-width="100px" prop="posterId">
            <wd-upload
              :file-list="showPostImg"
              :upload-method="customUpload"
              name="iFile"
              :limit="1"
              @change="handlePosterChange"
              accept="image"
              :action="VITE_UPLOAD_BASEURL"
            ></wd-upload>
          </wd-cell>
          <!-- 商品分类 -->
          <wd-picker
            required
            v-if="columns.length"
            :columns="columns"
            label="商品分类"
            prop="categoryIds"
            label-width="100px"
            v-model="categoryIds"
            :column-change="onChangeCategory"
            :display-format="displayFormat"
          />
          <!-- 销售状态 -->
          <wd-cell title="销售状态" custom-class="ncenter" title-width="100px" prop="productStatus">
            <wd-radio-group
              shape="dot"
              custom-class="!flex !items-center "
              v-model="formModel.productStatus"
              inline
            >
              <wd-radio :value="10">立即上架</wd-radio>
              <wd-radio :value="20">放入仓库</wd-radio>
            </wd-radio-group>
          </wd-cell>
          <!-- 商品卖点 -->
          <wd-textarea
            label="商品卖点"
            label-width="100px"
            type="textarea"
            v-model="formModel.sellingPoint"
            :maxlength="300"
            show-word-limit
            placeholder="请输入商品卖点"
            clearable
            prop="sellingPoint"
          />
        </wd-cell-group>
        <wd-cell-group custom-class="!mt20rpx" title="其他设置" border>
          <!-- 商品属性 -->
          <wd-cell title="商品属性" custom-class="ncenter" title-width="100px" prop="isVirtual">
            <wd-radio-group
              shape="dot"
              custom-class="!flex !items-center "
              v-model="formModel.isVirtual"
              inline
            >
              <wd-radio :value="0">实物商品</wd-radio>
              <wd-radio :value="1">虚拟商品</wd-radio>
            </wd-radio-group>
          </wd-cell>
          <!-- 运费模板 -->
          <wd-picker
            v-if="settings?.delivery && formModel.isVirtual == 0 && formModel.isDeliveryFree == 1"
            label="运费模板"
            required
            placeholder="请选择运费模板"
            label-width="100px"
            prop="deliveryId"
            v-model="formModel.deliveryId"
            :columns="settings?.delivery"
            label-key="name"
            value-key="deliveryId"
          />
          <!-- 商品排序 -->
          <wd-cell required title="商品排序" title-width="100px" prop="productSort">
            <wd-input-number v-model="formModel.productSort" :min="0" />
          </wd-cell>
          <!-- 限购数量 -->
          <wd-cell required title="限购数量" title-width="100px" prop="limitNum">
            <wd-input-number v-model="formModel.limitNum" :min="0" />
          </wd-cell>
          <wd-cell title-width="100%">
            <template #title>
              <div class="text-24rpx text-#999999">每个会员购买的最大数量，0为不限购</div>
            </template>
          </wd-cell>
          <template v-if="formModel.isVirtual === 1">
            <!-- 发货类型 -->
            <wd-cell title="发货类型" custom-class="ncenter" title-width="100px" prop="virtualAuto">
              <wd-radio-group
                shape="dot"
                custom-class="!flex !items-center "
                v-model="formModel.virtualAuto"
                inline
              >
                <wd-radio :value="1">自动</wd-radio>
                <wd-radio :value="0">手动</wd-radio>
              </wd-radio-group>
            </wd-cell>
            <!-- 虚拟内容 -->
            <wd-textarea
              label="虚拟内容"
              label-width="100px"
              type="textarea"
              v-model="formModel.virtualContent"
              :maxlength="300"
              show-word-limit
              placeholder="请输入虚拟内容"
              clearable
              prop="virtualContent"
            />
          </template>
        </wd-cell-group>
        <wd-cell-group custom-class="!mt20rpx" title="规格/库存" border>
          <!-- 库存计算方式 -->
          <wd-cell
            title="库存计算方式"
            custom-class="ncenter"
            title-width="100px"
            prop="deductStockType"
          >
            <wd-radio-group
              shape="dot"
              custom-class="!flex !items-center "
              v-model="formModel.deductStockType"
              inline
            >
              <wd-radio :value="10">下单减库存</wd-radio>
              <wd-radio :value="20">付款减库存</wd-radio>
            </wd-radio-group>
          </wd-cell>
          <!-- 产品规格 -->
          <wd-cell title="产品规格" custom-class="ncenter" title-width="100px" prop="specType">
            <wd-radio-group
              shape="dot"
              custom-class="!flex !items-center "
              v-model="formModel.specType"
              inline
            >
              <wd-radio :value="10">单规格</wd-radio>
              <!-- <wd-radio :value="20">多规格</wd-radio> -->
            </wd-radio-group>
          </wd-cell>
          <!-- 单规格 -->
          <template v-if="formModel.specType === 10">
            <!-- 产品编码 -->
            <wd-input
              label="产品编码"
              label-width="100px"
              prop="productNo"
              required
              clearable
              v-model="formModel.sku.productNo"
              placeholder="请输入产品编码"
            />
            <!-- 产品价格 -->
            <wd-input
              label="产品价格"
              label-width="100px"
              prop="productPrice"
              required
              clearable
              v-model="formModel.sku.productPrice"
              placeholder="请输入产品价格"
            />
            <!-- 产品划线价 -->
            <wd-input
              label="产品划线价"
              label-width="100px"
              prop="linePrice"
              required
              clearable
              v-model="formModel.sku.linePrice"
              placeholder="请输入产品划线价"
            />
            <!-- 库存数量 -->
            <wd-input
              label="库存数量"
              label-width="100px"
              prop="stockNum"
              required
              clearable
              v-model="formModel.sku.stockNum"
              placeholder="请输入库存数量"
            />
            <!-- 商品重量(Kg) -->
            <wd-input
              label="商品重量(Kg)"
              label-width="100px"
              prop="productWeight"
              required
              clearable
              v-model="formModel.sku.productWeight"
              placeholder="请输入商品重量"
            />
          </template>
          <!-- 多规格 -->
          <template v-if="false">
            <!-- 规格属性 -->
            <view class="mb-4">
              <view
                v-for="(attr, attrIndex) in formModel.specMany.specAttr"
                :key="attr.groupId"
                class="mb-2"
              >
                <wd-input
                  v-model="attr.groupName"
                  label="规格名称"
                  label-width="100px"
                  placeholder="例如：颜色、尺寸"
                  class="mb-2"
                />
                <view class="flex flex-wrap gap-2 items-center">
                  <view
                    v-for="(item, itemIndex) in attr.specItems"
                    :key="item.itemId"
                    class="px-2 py-1 border rounded"
                  >
                    {{ item.specValue }}
                    <wd-icon name="close" @click="removeSpecItem(attrIndex, itemIndex)" />
                  </view>
                  <wd-input
                    v-model="attr.tempValue"
                    placeholder="添加规格值"
                    class="border px-2 py-1 rounded"
                    @blur="() => addSpecItem(attrIndex)"
                  />
                </view>
              </view>
              <wd-button size="small" @click="addSpecAttr">添加规格属性</wd-button>
            </view>

            <!-- SKU 表格头 -->
            <view class="flex bg-gray-100 border border-gray-300">
              <view
                v-for="attr in formModel.specMany.specAttr"
                :key="attr.groupId"
                class="flex-1 border-r border-gray-300 p-2 text-center font-bold"
              >
                {{ attr.groupName }}
              </view>
              <view class="w-32 border-r border-gray-300 p-2 text-center font-bold">产品编码</view>
              <view class="w-20 border-r border-gray-300 p-2 text-center font-bold">价格</view>
              <view class="w-20 border-r border-gray-300 p-2 text-center font-bold">划线价</view>
              <view class="w-20 border-r border-gray-300 p-2 text-center font-bold">库存</view>
              <view class="w-24 p-2 text-center font-bold">重量(Kg)</view>
            </view>

            <!-- SKU 数据行 -->
            <view
              v-for="(sku, index) in formModel.specMany.specList"
              :key="sku.specSkuId"
              class="flex border-b border-gray-200"
            >
              <view
                v-for="row in sku.rows"
                :key="row.itemId"
                class="flex-1 border-r border-gray-300 p-1 text-center"
              >
                {{ row.specValue }}
              </view>

              <view class="w-32 border-r border-gray-300 p-1">
                <wd-input v-model="sku.productNo" placeholder="编码" />
              </view>
              <view class="w-20 border-r border-gray-300 p-1">
                <wd-input v-model="sku.productPrice" placeholder="价格" />
              </view>
              <view class="w-20 border-r border-gray-300 p-1">
                <wd-input v-model="sku.linePrice" placeholder="划线价" />
              </view>
              <view class="w-20 border-r border-gray-300 p-1">
                <wd-input v-model="sku.stockNum" placeholder="库存" />
              </view>
              <view class="w-24 p-1">
                <wd-input v-model="sku.productWeight" placeholder="重量" />
              </view>
            </view>
          </template>
        </wd-cell-group>
        <wd-cell-group custom-class="!mt20rpx" title="商品详情" border>
          <!-- 商品详情 -->
          <wd-cell title="商品详情" title-width="100px" prop="contentImage">
            <wd-upload
              :file-list="showContentImg"
              :upload-method="customUpload"
              name="iFile"
              @change="handleContentImgChange"
              accept="image"
              :action="VITE_UPLOAD_BASEURL"
            ></wd-upload>
          </wd-cell>
        </wd-cell-group>
        <wd-cell-group custom-class="!mt20rpx" title="善豆设置" border>
          <!-- 是否开启善豆赠送 -->
          <wd-cell title="是否开启善豆赠送" title-width="120px" prop="isPointsGift">
            <wd-switch
              v-model="formModel.isPointsGift"
              size="20px"
              :active-value="1"
              :inactive-value="0"
            />
          </wd-cell>
          <!-- 是否允许使用善豆抵扣 -->
          <wd-cell title="是否允许使用善豆抵扣" title-width="140px" prop="isPointsDiscount">
            <wd-switch
              v-model="formModel.isPointsDiscount"
              size="20px"
              :active-value="1"
              :inactive-value="0"
            />
          </wd-cell>
          <template v-if="formModel.isPointsDiscount">
            <!-- 商品重量(Kg) -->
            <wd-input
              label="最大抵扣善豆数量"
              label-width="140px"
              prop="maxPointsDiscount"
              clearable
              type="digit"
              v-model="formModel.maxPointsDiscount"
              placeholder="最大抵扣善豆数量"
            />
            <wd-cell title-width="100%">
              <template #title>
                <div class="text-24rpx text-#999999">最大抵扣善豆数量，-1为使用通用设置</div>
              </template>
            </wd-cell>
          </template>
        </wd-cell-group>
        <wd-cell-group custom-class="!mt20rpx" title="分销设置" border>
          <!-- 是否开启分销 -->
          <wd-cell title="是否开启分销" title-width="120px" prop="isAgent">
            <wd-switch
              v-model="formModel.isAgent"
              size="20px"
              :active-value="1"
              :inactive-value="0"
            />
          </wd-cell>
        </wd-cell-group>
      </wd-form>
    </div>
    <view class="footer">
      <wd-button type="primary" size="large" @click="handleSubmit" block>提交</wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { isArray } from 'wot-design-uni/components/common/util'
import type { FormRules } from 'wot-design-uni/components/wd-form/types'
import type { UploadFile } from 'wot-design-uni/components/wd-upload/types'
import { getEnvBaseUploadUrl, createUploadHandler, convertCategoryArrayToPickerMap } from '@/utils'
import { userSupplierCategory, addProductSetting, addProduct } from '@/service'

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`

const { safeAreaInsets } = uni.getSystemInfoSync()

const { data: settings, run } = useRequest(() => addProductSetting(), { immediate: true })

const formModel = reactive<Api.User.AddProductSettingBody>({
  productName: '',
  image: [],
  videoId: 0,
  posterId: 0,
  categoryPid: '0',
  productStatus: 10,
  sellingPoint: '',
  isVirtual: 0,
  deliveryId: '',
  isDeliveryFree: 1,
  productSort: 0,
  limitNum: 0,
  virtualAuto: 1,
  virtualContent: '',
  isPicture: 0,
  deductStockType: 10,
  specType: 10,
  sku: {
    productNo: '',
    productPrice: '',
    linePrice: '',
    stockNum: '',
    productWeight: '',
  },
  specMany: {
    specAttr: [],
    specList: [],
  },
  contentImage: [],
  isPointsGift: 0,
  isPointsDiscount: 0,
  isAgent: 0,
  maxPointsDiscount: '',
})

watch(
  () => formModel.deliveryId,
  (v) => {
    if (v == 0) {
      formModel.isDeliveryFree = 0
    } else {
      formModel.isDeliveryFree = 1
    }
  },
)

const rules: FormRules = {
  productName: [
    {
      required: true,
      message: '请输入商品名称',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请输入商品名称')
        }
      },
    },
  ],
  image: [
    {
      required: true,
      message: '请上传商品图片',
      validator: (value) => {
        if (isArray(value) && value.length) {
          return Promise.resolve()
        } else {
          return Promise.reject('请上传商品图片')
        }
      },
    },
  ],
  categoryIds: [
    {
      required: true,
      message: '请选择商品分类',
      validator: (value) => {
        if (isArray(value) && value.length) {
          return Promise.resolve()
        } else {
          return Promise.reject('请选择商品分类')
        }
      },
    },
  ],
  deliveryId: [
    {
      required: true,
      message: '请选择运费模板',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请选择运费模板')
        }
      },
    },
  ],
  productSort: [
    {
      required: true,
      message: '请输入商品排序',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请输入商品排序')
        }
      },
    },
  ],
  limitNum: [
    {
      required: true,
      message: '请输入限购数量',
      validator: (value) => {
        if (value || value === 0) {
          return Promise.resolve()
        } else {
          return Promise.reject('请输入限购数量')
        }
      },
    },
  ],
}

onMounted(() => getClassify())

// 获取分类数据

const columns = ref([])

const categoryIds = ref([])

let categoryMap = reactive<Record<string, { label: string; value: number }[]>>({})
const getClassify = async () => {
  const { data } = await userSupplierCategory()

  categoryMap = convertCategoryArrayToPickerMap(data)

  columns.value = [categoryMap['0'] || [], categoryMap[categoryMap['0']?.[0]?.value] || []]
}

const onChangeCategory = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]
  if (columnIndex === 0) {
    const nextCol = categoryMap[item.value] || [{ label: '', value: '' }]

    pickerView.setColumnData(1, nextCol)
  }
  resolve()
}

const displayFormat = (items) => {
  const labels = items.map((i) => i?.label).filter((label) => !!label)

  return labels.join(' > ')
}

// 添加规格属性
const addSpecAttr = () => {
  formModel.specMany.specAttr.push({
    groupId: Date.now(),
    groupName: '',
    specItems: [],
    tempValue: '',
  })
}

// 添加规格项
const addSpecItem = (attrIndex: number) => {
  const attr = formModel.specMany.specAttr[attrIndex]
  const value = attr.tempValue.trim()
  if (value) {
    attr.specItems.push({
      itemId: Date.now(),
      specValue: value,
    })
    attr.tempValue = ''
    generateSpecList()
  }
}

// 删除规格项
const removeSpecItem = (attrIndex: number, itemIndex: number) => {
  formModel.specMany.specAttr[attrIndex].specItems.splice(itemIndex, 1)
  generateSpecList()
}

// 笛卡尔积生成所有组合
function cartesian<T>(arr: T[][]): T[][] {
  return arr.reduce(
    (acc, cur) => {
      return acc.flatMap((a) => cur.map((b) => a.concat([b])))
    },
    [[]] as T[][],
  )
}

// 根据规格项生成 sku 表格
const generateSpecList = () => {
  const attrs = formModel.specMany.specAttr
  if (attrs.some((attr) => attr.specItems.length === 0)) {
    formModel.specMany.specList = []
    return
  }

  const combos = cartesian(attrs.map((attr) => attr.specItems))

  formModel.specMany.specList = combos.map((combo) => {
    const rows = combo.map((item) => ({
      itemId: item.itemId,
      specValue: item.specValue,
    }))
    return {
      productSkuId: 0,
      specSkuId: combo.map((i) => i.itemId).join('_'),
      rows,
      productNo: '',
      productPrice: '',
      linePrice: '',
      stockNum: '',
      productWeight: '',
    }
  })
}

const form = ref()

// 商品图片
const showProductImg = ref<UploadFile[]>([])
// 商品视频
const showVideo = ref<UploadFile[]>([])
// 视频封面
const showPostImg = ref<UploadFile[]>([])
// 商品详情
const showContentImg = ref<UploadFile[]>([])

function handleSubmit() {
  form.value
    .validate()
    .then(async ({ valid, errors }) => {
      console.log(valid)
      console.log(errors)
      formModel.image = showProductImg.value.map((item) => item?.response)

      formModel.videoId =
        showVideo.value.map((item) => {
          if (
            item &&
            typeof item.response === 'object' &&
            item.response !== null &&
            'fileId' in item.response
          ) {
            return item.response.fileId
          }
          return ''
        })?.[0] ?? 0

      formModel.posterId =
        showPostImg.value.map((item) => {
          if (
            item &&
            typeof item.response === 'object' &&
            item.response !== null &&
            'fileId' in item.response
          ) {
            return item.response.fileId
          }
          return ''
        })?.[0] ?? 0

      formModel.contentImage = showContentImg.value.map((item) => item?.response)

      formModel.contentImage.length ? (formModel.isPicture = 1) : (formModel.isPicture = 0)

      formModel.categoryPid = categoryIds.value?.[0] ?? '0'

      formModel.categoryId = categoryIds.value?.[1] ?? '0'

      console.log('🚀 ~ formModel:', formModel)

      try {
        await addProduct(formModel)
        uni.showToast({ title: '添加成功' })
        setTimeout(() => {
          uni.navigateBack()
        }, 500)
      } catch (error) {
        console.log('🚀 ~ .then ~ error:', error)
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

// 商品图片
const customUpload = createUploadHandler('image')

function handleFileChange({ fileList }) {
  showProductImg.value = fileList
}
// 商品视频
const customVideoUpload = createUploadHandler('video')
function handleVideoChange({ fileList }) {
  showVideo.value = fileList
}

// 视频封面
function handlePosterChange({ fileList }) {
  showPostImg.value = fileList
}

// 商品详情
function handleContentImgChange({ fileList }) {
  showContentImg.value = fileList
}
</script>

<style lang="scss" scoped>
:deep(.ncenter .wd-cell__wrapper) {
  align-items: center !important;
}
</style>

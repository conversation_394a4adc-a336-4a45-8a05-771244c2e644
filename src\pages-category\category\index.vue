<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden pb-2 box-border h-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="20"
      :fixed="false"
      :empty-view-super-style="{ background: '#ffffff' }"
      @query="queryList"
    >
      <template #top>
        <div
          :style="{ background: themeStore?.navColor }"
          class="pb20rpx px24rpx box-border bg-#ffffff grid grid-cols-1 gap-y-5px transition-all-300"
        >
          <wd-navbar
            safeAreaInsetTop
            fixed
            placeholder
            :custom-style="`background: ${themeStore?.navColor};`"
            left-arrow
            :bordered="false"
            @click-left="handleClickLeft"
          >
            <template #title>
              <div class="flex w-full justify-center items-center text-#ffffff">
                <span>{{ title }}</span>
              </div>
            </template>
          </wd-navbar>
          <div
            class="w-full h66rpx border border-solid border-#ffffff rd-20px px-30rpx py-5px box-border flex gap-x-10px items-center"
          >
            <wd-input
              placeholder="请输入要搜索的内容"
              inputmode="search"
              no-border
              clearable
              v-model="searchVal"
              confirm-type="search"
              @confirm="handleSearch"
              @clear="handleClear"
              placeholderClass="!text-#ffffff"
              custom-input-class="!text-#ffffff"
              custom-class="search !flex-1 !text-#ffffff !h-full !flex !items-center !text-24rpx !bg-[transparent]"
            ></wd-input>
            <div class="flex h-full items-center gap-x-10px">
              <div class="w1px h-full bg-#ffffff"></div>
              <wd-button
                type="text"
                custom-class="!m-unset !line-height-unset !p-unset !text-#ffffff !h-unset"
              >
                搜索
              </wd-button>
            </div>
          </div>
        </div>
      </template>
      <div class="grid grid-cols-1 gap-y-20rpx">
        <!-- 轮播 -->
        <div
          :style="{ background: themeStore?.navColor }"
          class="px5px box-border transition-all-300"
        >
          <wd-swiper
            :list="swiperList"
            value-key="imgUrl"
            autoplay
            height="110"
            @change="handleSwiperChange"
          ></wd-swiper>
        </div>
        <!-- 导航 -->
        <div class="px-20rpx pb-20rpx grid grid-cols-1 gap-y-20rpx grid-auto-rows-min">
          <scroll-view scroll-x class="bg-#ffffff rd-8px w-full px-14px box-border">
            <div class="flex flex-nowrap gap-x-14px">
              <div
                class="grid grid-cols-5 gap-y-10px gap-x-20px py12px min-w-full box-border grid-rows-[auto_auto]"
                v-for="(group, inxex) in categories"
                :key="inxex"
              >
                <!-- <div
                  @click="handleCategoryChange(fatherCategory?.categoryId)"
                  class="w-full aspect-square flex flex-col items-center gap-y-14rpx justify-center"
                >
                  <div class="flex items-center justify-center rd-1/2 transition-all-300">
                    <wd-img
                      :src="fatherCategory?.picPath"
                      width="96rpx"
                      height="96rpx"
                      radius="50%"
                      mode="aspectFit"
                    ></wd-img>
                  </div>

                  <span
                    :class="
                      searchParams.categoryId === fatherCategory?.categoryId
                        ? 'text-#FF7D26'
                        : 'text-#333333'
                    "
                    class="text-24rpx inline-block transition-all-300"
                  >
                    全部美食
                  </span>
                </div> -->
                <div
                  v-for="item in group"
                  :key="item?.categoryId"
                  @click="handleCategoryChange(Number(item?.categoryId))"
                  class="w-full aspect-square flex flex-col items-center gap-y-14rpx justify-center"
                >
                  <div class="flex items-center justify-center rd-1/2 transition-all-300">
                    <wd-img
                      :src="item?.picPath"
                      width="96rpx"
                      height="96rpx"
                      radius="50%"
                      mode="aspectFit"
                    ></wd-img>
                  </div>

                  <span
                    :class="
                      item?.categoryId === searchParams?.categoryId
                        ? 'text-#FF7D26'
                        : 'text-#333333'
                    "
                    class="text-24rpx inline-block transition-all-300"
                  >
                    {{ item?.name }}
                  </span>
                </div>
              </div>
            </div>
          </scroll-view>
        </div>

        <!-- <div class="px-20rpx pb-20rpx grid grid-cols-1 gap-y-20rpx grid-auto-rows-min">
          <div
            class="w-full bg-#ffffff rd-8px grid grid-cols-5 box-border gap-y-10px gap-x-20px px14px py12px box-border"
          >
            <div
              @click="handleCategoryChange(fatherCategory?.categoryId)"
              class="w-full aspect-square flex flex-col items-center gap-y-14rpx justify-center"
            >
              <div class="flex items-center justify-center rd-1/2 transition-all-300">
                <wd-img
                  :src="fatherCategory?.picPath"
                  width="96rpx"
                  height="96rpx"
                  radius="50%"
                  mode="aspectFit"
                ></wd-img>
              </div>

              <span
                :class="
                  searchParams.categoryId === fatherCategory?.categoryId
                    ? 'text-#FF7D26'
                    : 'text-#333333'
                "
                class="text-24rpx inline-block transition-all-300"
              >
                全部美食
              </span>
            </div>
            <div
              v-for="item in categories"
              :key="item?.categoryId"
              @click="handleCategoryChange(Number(item?.categoryId))"
              class="w-full aspect-square flex flex-col items-center gap-y-14rpx justify-center"
            >
              <div class="flex items-center justify-center rd-1/2 transition-all-300">
                <wd-img
                  :src="item?.picPath"
                  width="96rpx"
                  height="96rpx"
                  radius="50%"
                  mode="aspectFit"
                ></wd-img>
              </div>

              <span
                :class="
                  item?.categoryId === searchParams?.categoryId ? 'text-#FF7D26' : 'text-#333333'
                "
                class="text-24rpx inline-block transition-all-300"
              >
                {{ item?.name }}
              </span>
            </div>
          </div>
        </div> -->
        <!-- 商品 -->
        <div class="grid grid-cols-1 gap-y-10px p24rpx box-border bg-#ffffff rd-lt-8px rd-rt-8px">
          <div class="grid grid-cols-1 gap-y-10px">
            <!-- <div class="text-#333333 text-32rpx font-bold">店铺列表</div> -->
            <div class="drop-menu-container flex flex-col">
              <div class="grid grid-cols-2">
                <div
                  @click="toggleTab('distance')"
                  class="w-full h70rpx flex items-center justify-center gap-x-5px"
                >
                  <div
                    :class="searchParams.distance ? 'text-#FF7D26' : 'text-#333333'"
                    class="text-28rpx text-#333333"
                  >
                    {{ distanceName }}
                  </div>
                  <div
                    :class="`${activeTab === 'distance' ? 'i-carbon-caret-up' : 'i-carbon-caret-down'} ${searchParams.distance ? 'text-#FF7D26' : 'text-#333333'}`"
                    class="text-12px"
                  ></div>
                </div>
                <div
                  @click="toggleTab('sort')"
                  class="w-full h70rpx flex items-center justify-center gap-x-5px"
                >
                  <div
                    :class="searchParams.sortType ? 'text-#FF7D26' : 'text-#333333'"
                    class="text-28rpx"
                  >
                    {{ sortName }}
                  </div>
                  <div
                    :class="`${activeTab === 'sort' ? 'i-carbon-caret-up' : 'i-carbon-caret-down'} ${searchParams.sortType ? 'text-#FF7D26' : 'text-#333333'}`"
                    class="text-12px text-#333333"
                  ></div>
                </div>
              </div>
              <div class="content">
                <div
                  v-if="activeTab === 'distance'"
                  class="w-full px20px py15px box-border grid grid-cols-5 gap-14px"
                >
                  <div
                    v-for="item in distanceOption"
                    :key="item.value"
                    @click="() => handleDistanceChange(item)"
                    :class="item.value === searchParams.distance ? 'bg-#FFECDF text-#FF7D26' : ''"
                    class="h-56rpx w-full rd-3px bg-#eeeeee flex text-24rpx text-#333333 items-center justify-center"
                  >
                    {{ item?.label }}
                  </div>
                </div>
                <div
                  v-if="activeTab === 'sort'"
                  class="w-full px20px py15px box-border grid grid-cols-4 gap-14px"
                >
                  <div
                    v-for="item in sortOption"
                    :key="item.value"
                    @click="() => handleSortChange(item)"
                    :class="item.value === searchParams.sortType ? 'bg-#FFECDF text-#FF7D26' : ''"
                    class="h-56rpx w-full rd-3px bg-#eeeeee text-24rpx flex text-#333333 items-center justify-center"
                  >
                    {{ item?.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="grid grid-cols-1 gap-y-20px grid-auto-rows-min">
            <template v-if="loading">
              <view v-for="item in 20" :key="item" class="flex gap-x-8px">
                <wd-skeleton
                  animation="gradient"
                  theme="avatar"
                  :row-col="[{ size: '160rpx', type: 'rect' }]"
                />
                <wd-skeleton
                  animation="gradient"
                  theme="paragraph"
                  :custom-style="{ width: '100%', marginLeft: '12px' }"
                  :row-col="[{ width: '100%' }, { width: '100%' }, { width: '100%' }]"
                />
              </view>
            </template>
            <div
              v-for="item in dataList"
              :key="item?.storeId"
              class="flex gap-x-8px items-center"
              @click="goSupplierDetail(item?.shopSupplierId, item?.storeId, item?.storeName)"
            >
              <wd-img
                width="160rpx"
                height="160rpx"
                mode="aspectFill"
                radius="10px"
                :src="item?.logoFilePath"
              ></wd-img>
              <div class="flex-1 grid grid-cols-1 gap-y-13px">
                <div class="grid grid-cols-1 gap-y-2px">
                  <div class="flex items-center justify-between gap-x-5px">
                    <span
                      v-if="item?.storeType === 30"
                      class="text-14px text-#333333 font-500 flex-1 line-clamp-1"
                    >
                      {{ item?.supplierName }}({{ item?.storeName }})
                    </span>
                    <span v-else class="text-14px text-#333333 font-500 flex-1 line-clamp-1">
                      {{ item?.storeName }}
                    </span>
                    <span class="text-#FF2828 text-20rpx">
                      {{ item?.status ? '营业中' : '停业中' }}
                    </span>
                  </div>
                  <div class="flex items-center gap-x-5px">
                    <wd-rate
                      :modelValue="getScore(item?.serverScore, 1)"
                      readonly
                      active-color="#FF2828"
                    />
                    <span class="text-20rpx text-#FF2828 font-500">
                      {{ item?.serverScore }}
                    </span>
                  </div>
                  <span class="text-24rpx text-#999999 line-clamp-1">
                    {{ item?.area.replace(/\//g, '') }}{{ item?.address }}
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <div v-if="false" class="flex items-center">
                    <div
                      class="bg-#FF7D26 rd-2px text-24rpx p2px box-border flex items-center justify-center text-#803909"
                    >
                      积
                    </div>
                    <div
                      class="text-24rpx bg-#FFC39B text-#FF8533 rd-tr-2px rd-br-2px p2px box-border"
                    >
                      最高返{{ item?.supplierCommissionRate }}%
                    </div>
                  </div>
                  <div></div>
                  <div class="flex items-end">
                    <div class="i-carbon-location text-14px text-#333333"></div>
                    <span class="text-#999999 text-20rpx">
                      {{ item?.distance ? `${(item?.distance / 1000).toFixed(1)}km` : '' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { useThemeStore } from '@/store/theme'
import { getPlatform } from '@/utils'
import { getProductCategoryListByPId, fetchIndexStore, fetchSupplierCategory } from '@/service'
import { getScore, chunkCategoryList } from '@/utils'
import { useLocationStore } from '@/store'

const themeStore = useThemeStore()

defineOptions({
  name: 'Foods',
})

const categoryId = ref('1')
onLoad((e) => {
  if (e?.categoryPid) {
    categoryId.value = e?.categoryPid
    searchParams.value.categoryId = Number(e?.categoryPid)
    run()
  }
})

// 父类分类列表
const { data: partentCategorys } = useRequest(() => fetchSupplierCategory(), { immediate: true })
// 父类分类
const fatherCategory = computed<Api.Home.SupplierCategoryListItem>(() => {
  return partentCategorys.value?.find((item) => item?.categoryId === Number(categoryId.value))
})

// 父类children
const { data: category, run } = useRequest(() =>
  getProductCategoryListByPId({ parentId: categoryId.value }),
)

// 整合父类子类
const categories = computed<Api.Home.SupplierCategoryListItem[][]>(() => {
  if (!fatherCategory.value) return [[]]

  const allList = [{ ...fatherCategory.value }, ...(category.value || [])]

  return chunkCategoryList(allList)
})

// 标题
const title = computed(() => fatherCategory.value?.name)
// 轮播
const swiperList = computed(() => {
  const turnValueStr = JSON.parse(category.value?.[0]?.turnValue ?? '[]')?.[0]?.background
  themeStore.setNavColor(turnValueStr)

  const activeSwiper =
    partentCategorys.value?.find(
      (item) => item?.categoryId === Number(searchParams.value.categoryId),
    )?.turnValue ??
    category.value?.find((item) => item?.categoryId === Number(searchParams.value.categoryId))
      ?.turnValue

  let parsedSwiper = []
  try {
    parsedSwiper = JSON.parse(activeSwiper || '[]')
  } catch (e) {
    console.warn('JSON.parse failed for activeSwiper:', activeSwiper, e)
    parsedSwiper = []
  }

  return parsedSwiper
})

const handleSwiperChange = ({ current }) => {
  themeStore.setNavColor(swiperList.value[current]?.background)
}

const activeTab = ref('') // '', 'category', 'distance', 'sort'

const toggleTab = (tab: string) => {
  activeTab.value = activeTab.value === tab ? '' : tab
}

const searchParams = ref({
  distance: '',
  sortType: '',
  categoryId: fatherCategory.value?.categoryId ?? 1,
})

const handleCategoryChange = (categoryId: number) => {
  searchParams.value.categoryId = categoryId
  paging.value?.reload()
}

const distanceName = ref('附近')
const distanceOption = ref([
  { label: '附近', value: '' },
  { label: '1km', value: 1 },
  { label: '2km', value: 2 },
  { label: '5km', value: 5 },
  { label: '10km', value: 10 },
  { label: '20km', value: 20 },
  { label: '30km', value: 30 },
  { label: '40km', value: 40 },
])

const handleDistanceChange = ({ label, value }) => {
  searchParams.value.distance = value
  distanceName.value = label
  activeTab.value = ''
  paging.value?.reload()
}

const sortName = ref('智能排序')
const sortOption = ref([
  { label: '智能排序', value: '' },
  { label: '评分优先', value: 2 },
  { label: '距离优先', value: 3 },
])
const handleSortChange = ({ label, value }) => {
  searchParams.value.sortType = value
  sortName.value = label
  activeTab.value = ''
  paging.value?.reload()
}

const locationStore = useLocationStore()

const paging = ref()
const dataList = ref<Api.Home.HomeStoreListItem[]>([])
const loading = ref(false)
const queryList = async (pageNo: number, pageSize: number) => {
  loading.value = true
  try {
    const { data: store } = await fetchIndexStore({
      pageIndex: pageNo,
      pageSize,
      longitude: locationStore?.locationInfo?.location?.lon,
      latitude: locationStore?.locationInfo?.location?.lat,
      categoryId: searchParams.value?.categoryId,
      distance: searchParams.value.distance,
      sortType: searchParams.value.sortType,
      keyWord: searchVal.value,
    })

    setTimeout(() => {
      loading.value = false
    }, 10)

    paging.value.complete(store?.records)
  } catch (err) {
    setTimeout(() => {
      loading.value = false
    }, 10)
    paging.value.complete(false)
  }
}

watch(
  () => locationStore?.locationInfo,
  (newVal) => {
    if (newVal) {
      paging.value?.reload()
    }
  },
)

const goSupplierDetail = (shopSupplierId: number, storeId: number, storeName: string) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${shopSupplierId}&storeId=${storeId}&storeName=${storeName}`,
  })
}

const searchVal = ref('')

const handleSearch = () => {
  paging.value?.reload()
}

const handleClear = () => {
  searchVal.value = ''
  paging.value?.reload()
}

const handleClickLeft = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
:deep(.wd-sidebar-item) {
  font-size: 26rpx !important;
}
:deep(.wd-input__clear) {
  background: transparent !important ;
}
:deep(.wd-navbar__arrow) {
  color: #ffffff !important;
}
</style>

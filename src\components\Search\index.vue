<template>
  <div
    @click="handleGoSearch"
    class="flex items-center h-full bg-#ffffff of-hidden box-border pl-20rpx pr-1px gap-x-10rpx rd-30rpx"
  >
    <!-- <wd-icon @click="handleSearch" name="search" size="28rpx" color="#ffffff"></wd-icon> -->
    <wd-input
      :placeholder="placeholder"
      inputmode="search"
      no-border
      @input="handleSearchChange"
      clearable
      focus
      v-model="searchVal"
      confirm-type="search"
      @confirm="handleSubSearch"
      @clear="handleClear"
      :readonly="Boolean(handleGoSearch)"
      placeholderClass="text-#999999"
      custom-class="search !flex-1 !text-#999999 !h-full !flex !items-center !text-24rpx"
    ></wd-input>
    <!-- <div
          class="px30rpx py14rpx box-border rd-30rpx bg-#FF7D26 text-24rpx font-500 text-#ffffff"
        >
          搜索
        </div> -->
    <wd-button
      @click="handleSubSearch"
      custom-class="!w-108rpx !p-unset !min-w-unset !px30rpx !py14rpx !box-border !rd-30rpx !h-58rpx"
    >
      搜索
    </wd-button>
  </div>
</template>

<script lang="ts" setup>
import { useSearchStore } from '@/store/search'

const searchStore = useSearchStore()

const props = defineProps<{
  handleGoSearch?: () => void
  handleSearch?: (search: string) => void
  placeholder: string
  from: string
}>()

const emits = defineEmits<{
  change: [val: string]
}>()

const searchVal = ref('')

const handleSubSearch = () => {
  if (props.from === 'mall') {
    searchStore.addSearch(searchVal.value)
  } else {
    searchStore.addStoreSearch(searchVal.value)
  }

  props.handleSearch(searchVal.value)

  searchVal.value = ''
}

const handleClear = () => {
  searchVal.value = ''
  props.handleSearch(searchVal.value)
  emits('change', '')
}

const handleSearchChange = ({ value }) => {
  emits('change', value)
}

const handleSearchComplete = () => {
  searchVal.value = ''
}

defineExpose({
  handleSearchComplete,
})
</script>

<style lang="scss" scoped>
//
</style>

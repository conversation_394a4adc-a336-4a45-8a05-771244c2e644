<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <scroll-view scroll-y>
    <view class="size-full grid grid-cols-1 grid-auto-rows-min gap-y-10px">
      <!-- <SimpleNavBar title="商品详情" /> -->
      <div
        @click="handleBack"
        class="fixed z-10 w-full flex items-center px20rpx"
        :style="{ height: menuButtonInfo.height + 'px', top: menuButtonInfo.top + 'px' }"
      >
        <div class="w24px h24px flex items-center justify-center rd-1/2 bg-[rgba(0,0,0,0.25)]">
          <div class="i-carbon-chevron-left text-#ffffff text-20px"></div>
        </div>
      </div>
      <div class="relative">
        <wd-swiper
          :list="productDetail?.detail?.image?.map((item) => item.filePath) ?? []"
          autoplay
          indicator
          :height="390"
        ></wd-swiper>
        <div
          v-if="productDetail?.detail?.productStock <= 0"
          class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center pointer-events-none"
        >
          <span>售罄</span>
        </div>
      </div>

      <div class="w-full px-10px grid grid-cols-1 box-border gap-y-10px grid-auto-rows-min">
        <!-- 详情 -->
        <div class="w-full p30rpx box-border bg-#ffffff rd-20rpx flex flex-col gap-y-10px">
          <div class="flex items-end justify-between">
            <div class="flex items-baseline text-#FF7D26">
              <span class="text-20rpx font-500">¥</span>
              <span class="text-40rpx font-bold">
                {{ showPrice?.split('.')?.[0] }}
              </span>
              <span class="text-24rpx font-bold">.{{ showPrice?.split('.')?.[1] }}</span>
              <div
                class="flex items-end"
                v-if="
                  productDetail?.detail?.extraRedPocketPrice &&
                  productDetail?.detail?.extraRedPocketPrice != '0.00'
                "
              >
                <span class="text-#FF7D26 text-24rpx fw-400 ml0.5">
                  +{{ productDetail?.detail?.extraRedPocketPrice }}
                </span>
                <wd-img
                  src="https://file.shanqianqi.com/image/2025/06/16/4c5ad84a06aa4491a4e5475e01f14986.png"
                  width="28rpx"
                  height="28rpx"
                  mode="aspectFill"
                ></wd-img>
              </div>

              <div
                v-if="productDetail?.detail?.isShowBeanValue === 'Y'"
                class="flex items-end text-30rpx fw-700 pl40rpx"
              >
                <span class="mx-1.5">可抵扣</span>
                <span class="text-#FF7D26">{{ productDetail?.detail?.beanDeductionValue }}</span>
                <image
                  class="w30rpx h30rpx"
                  src="https://file.shanqianqi.com/image/2025/06/24/e50f41403ded42a18709e739b74e8b91.png"
                ></image>
              </div>
            </div>
            <div class="flex items-center gap-x-10px">
              <wd-button
                type="text"
                @click="toggleFavorite"
                custom-class="!m-unset !min-w-unset !text-#333333 !h-unset !p-unset !line-height-unset"
              >
                <div
                  :class="`flex flex-col items-center text-26rpx gap-y-5px transition-all-300 ${productDetail?.isFav ? 'text-#FF7D26' : ''}`"
                >
                  <div
                    :class="`text-18px transition-all-300  ${productDetail?.isFav ? 'text-#FF7D26 i-carbon-star-filled' : 'text-gray i-carbon-star'}`"
                  ></div>
                  <!-- {{ productDetail?.isFav ? '已收藏' : '收藏' }} -->
                </div>
              </wd-button>

              <wd-button
                type="text"
                open-type="share"
                custom-class="!m-unset !min-w-unset !text-#333333 !h-unset !p-unset !line-height-unset"
              >
                <div class="flex flex-col items-center text-26rpx gap-y-5px">
                  <div class="i-carbon-share text-18px text-gray"></div>
                  <!-- 分享 -->
                </div>
              </wd-button>
            </div>
          </div>
          <div class="flex items-start">
            <div class="flex-1 text-30rpx text-#333333">
              {{ productDetail?.detail?.productName }}
              <span
                v-if="productDetail?.detail?.supplier?.storeType === 20"
                class="w-48rpx ml-5rpx bg-#ff5704 text-#ffffff rd-7rpx text-21rpx px-10rpx py5rpx inline-flex items-center justify-center"
              >
                自营
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between gap-x-5px text-24rpx">
            <span class="text-#999999">
              已售{{ formatLargeNumber(productDetail?.detail?.productSales) }}+
            </span>
            <div class="flex items-baseline text-30rpx fw-700 gap-4rpx">
              <span class="text-red">获得积分</span>
              <span class="text-#FF7D26">{{ productDetail?.detail?.scoreDeductionValue }}</span>
            </div>
            <div v-if="false" class="flex items-center gap-x-10px">
              <div
                class="bg-[rgba(255,204,0,0.09)] text-#ff5704 px7rpx py6rpx flex items-center justify-center rd-4rpx text-22rpx"
              >
                返善豆
              </div>
              <div class="text-22rpx text-#999999">
                商城购物返善豆，订单完成后最高返{{ productDetail?.discount?.givePoints }}善豆
              </div>
            </div>
          </div>
        </div>
        <!-- 规格 -->
        <div
          v-if="productDetail?.detail?.specType === 20"
          class="w-full px30rpx h-92rpx box-border bg-#ffffff rd-20rpx flex flex-col gap-y-10px"
        >
          <div class="size-full flex items-center justify-between">
            <div
              class="flex items-center gap-x-10px text-28rpx"
              style="max-width: calc(100% - 120rpx); min-width: 0"
            >
              <span class="flex-shrink-0">规格:</span>
              <span class="ellipsis">
                {{ selectedSpecText ? `已选择:${selectedSpecText}` : '请选择规格' }}
              </span>
            </div>
            <div @click="showPrePay = !showPrePay" class="flex items-center flex-shrink-0">
              <wd-button custom-class="!text-24rpx !m-unset !p-unset !h-unset" type="text">
                选择
              </wd-button>
              <div class="i-carbon-chevron-right text-12px text-#FF7D26"></div>
            </div>
          </div>
        </div>
        <!-- 评价 -->
        <div
          v-if="
            productDetail?.detail?.supplier?.storeType !== 30 &&
            productDetail?.detail?.supplier?.storeType !== 40
          "
          class="w-full px30rpx h-92rpx box-border bg-#ffffff rd-20rpx flex flex-col gap-y-10px"
        >
          <div class="size-full flex items-center justify-between">
            <div class="flex items-center gap-x-10px text-28rpx">
              评价
              <span class="text-22rpx text-#999999">({{ productDetail?.commentDataCount }})</span>
            </div>
            <div @click="goCommentList" class="flex items-center">
              <wd-button custom-class="!text-24rpx !m-unset !p-unset !h-unset" type="text">
                查看全部
              </wd-button>
              <div class="i-carbon-chevron-right text-12px text-#FF7D26"></div>
            </div>
          </div>
        </div>
        <!-- 店铺 -->
        <!-- <div
          v-if="productDetail?.detail?.supplier?.storeType !== 20"
          class="w-full p30rpx box-border bg-#ffffff rd-20rpx flex items-center justify-between"
        >
          <div class="flex items-center gap-x-10px flex-1">
            <wd-img
              :src="productDetail?.detail?.supplier?.logoFilePath"
              width="120rpx"
              mode="widthFix"
            ></wd-img>
            <div class="flex flex-col gap-y-10px">
              <div class="text-32rpx font-bold">{{ productDetail?.detail?.supplier?.name }}</div>
              <div class="flex flex-col text-#999999 gap-y-5px text-26rpx">
                <div class="flex items-center">
                  <span>主营品牌：</span>
                  <span>{{ productDetail?.detail?.supplier?.categoryName }}</span>
                </div>
                <div class="flex items-center">
                  <span>销量：</span>
                  <span>{{ `${productDetail?.detail?.supplier?.productSales ?? ''}件` }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-col gap-y-10px">
            <div class="flex items-center text-#FF7D26 text-26rpx">
              商户评分：
              <span class="font-bold">{{ productDetail?.detail?.supplier?.serverScore }}</span>
            </div>
            <wd-button
              @click="() => goSupplierIndex(productDetail?.detail?.supplier?.shopSupplierId)"
              plain
              custom-class="!min-w-unset"
            >
              进店看看
            </wd-button>
          </div>
        </div> -->
        <!-- 简介 -->
        <div class="w-full box-border bg-#ffffff rd-20rpx flex flex-col">
          <div
            class="text-28rpx h90rpx flex items-center px30rpx box-border border-b border-b-solid border-b-#eeeeee"
          >
            商品介绍
          </div>
          <div>
            <view
              v-if="productDetail?.detail?.isPicture === 0"
              v-html="productDetail?.detail?.content"
            ></view>
            <div v-else>
              <div class="w-full flex flex-col">
                <wd-img
                  width="100%"
                  mode="widthFix"
                  :src="img.filePath"
                  v-for="img in productDetail?.detail?.contentImage"
                  :key="img.imageId"
                ></wd-img>
              </div>
            </div>
          </div>
        </div>
        <!-- btns -->
      </div>

      <div
        class="h-100rpx w-full pt-10rpx"
        :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
      ></div>
      <div
        class="h-100rpx w-full fixed bottom-0 left-0 bg-#ffffff grid grid-cols-[auto_1fr] place-content-center gap-x-10px pt-10rpx"
        :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
      >
        <div class="grid grid-cols-3 gap-x-20px h-full pl30rpx box-border place-content-center">
          <wd-button
            @click="goHome"
            type="text"
            custom-class="!m-unset !min-w-unset !text-#333333 !h-unset !p-unset !line-height-unset"
          >
            <div class="flex flex-col items-center gap-y-5px">
              <div class="i-carbon-home text-#999999 text-18px"></div>
              <span class="text-#999999 text-22rpx">首页</span>
            </div>
          </wd-button>

          <wd-button
            @click="openMaservice"
            type="text"
            custom-class="!m-unset !min-w-unset !text-#333333 !h-unset !p-unset !line-height-unset"
          >
            <div class="flex flex-col items-center gap-y-5px">
              <div class="i-hugeicons-customer-service-01 text-#999999 text-18px"></div>
              <span class="text-#999999 text-22rpx">客服</span>
            </div>
          </wd-button>

          <wd-badge
            v-if="fromRoute !== 'store'"
            :modelValue="getTotalProductCount(cartStore?.cartData?.productList ?? [])"
            custom-class="cart-badge"
          >
            <wd-button
              @click="goCart"
              type="text"
              custom-class="!m-unset !min-w-unset !text-#333333 !h-unset !p-unset !line-height-unset"
            >
              <div class="flex flex-col items-center gap-y-5px">
                <div class="i-carbon-shopping-cart text-#999999 text-18px"></div>
                <span class="text-#999999 text-22rpx">购物车</span>
              </div>
            </wd-button>
          </wd-badge>
        </div>
        <div
          class="grid pr-30rpx box-border place-content-center"
          :class="
            productDetail?.detail?.supplier?.storeType !== 30 &&
            productDetail?.detail?.supplier?.storeType !== 40
              ? 'grid-cols-2'
              : 'grid-cols-1'
          "
        >
          <wd-button
            v-if="
              productDetail?.detail?.supplier?.storeType !== 30 &&
              productDetail?.detail?.supplier?.storeType !== 40
            "
            @click="() => handlePopUp('cart')"
            custom-class="!bg-[linear-gradient(45deg,_#ffcb05_0%,_#fd9f01_100%)] !rd-rt-unset !rd-rb-unset !flex-1"
          >
            加入购物车
          </wd-button>

          <wd-button
            @click="() => handlePopUp('buy')"
            :custom-class="`!m-unset !min-w-unset !flex-1 ${
              productDetail?.detail?.supplier?.storeType == 30 ||
              productDetail?.detail?.supplier?.storeType == 40
                ? ''
                : '!rd-lt-unset !rd-lb-unset'
            }`"
          >
            立即购买
          </wd-button>
        </div>
      </div>
    </view>
    <!--客服-->

    <wd-popup
      v-model="isMpservice"
      position="bottom"
      safe-area-inset-bottom
      lockScroll
      custom-style="min-height: 200px;max-height:600px"
      @close="closeMpservice"
    >
      <Mpservice
        v-if="productDetail?.detail?.supplier?.shopSupplierId"
        :shopSupplierId="`${productDetail?.detail?.supplier?.shopSupplierId}`"
      ></Mpservice>
    </wd-popup>
    <wd-popup
      v-model="showPrePay"
      position="bottom"
      safe-area-inset-bottom
      lockScroll
      custom-style="min-height: 200px;max-height:600px"
      @close="handleClose"
    >
      <div class="grid grid-cols-1 p-30rpx box-border gap-y-10px">
        <div
          class="flex items-start gap-x-10px pb10px box-border border-b border-b-solid border-b-#eeeeee"
        >
          <wd-img
            width="180rpx"
            :src="
              productDetail?.detail?.skuList?.find((item) => item?.specSkuId === specSkuId)
                ?.imagePath || productDetail?.detail?.image?.[0]?.filePath
            "
            height="180rpx"
            mode="aspectFit"
          ></wd-img>
          <div class="flex flex-col gap-y-10px">
            <div class="flex items-baseline text-#FF7D26">
              <span class="text-20rpx font-500">¥</span>
              <span class="text-40rpx font-bold">
                {{ showPrice?.split('.')?.[0] }}
              </span>
              <span class="text-24rpx font-bold">.{{ showPrice?.split('.')?.[1] }}</span>
              <template v-if="productDetail?.detail?.specType === 20 && !specSkuId">
                <span class="text-40rpx font-bold mx-5px">-</span>
                <span class="text-20rpx font-500">¥</span>
                <span class="text-40rpx font-bold">
                  {{ productDetail?.detail?.highPrice?.split('.')?.[0] }}
                </span>
                <span class="text-24rpx font-bold">
                  .{{ productDetail?.detail?.highPrice?.split('.')?.[1] }}
                </span>
              </template>
            </div>
            <div class="text-24rpx text-#999999">
              库存：{{
                productDetail?.detail?.specType === 20
                  ? productDetail?.specData?.specList?.find(
                      (spc) => spc?.specSkuId === `${specSkuId}`,
                    )?.productStock ?? '-'
                  : productDetail?.detail?.productStock ?? '-'
              }}件
            </div>
          </div>
        </div>

        <div
          v-if="productDetail?.detail?.specType === 20 && productDetail?.specData"
          class="grid grid-cols-1 gap-y-5px grid-auto-rows-min"
        >
          <div
            class="flex flex-col gap-y-5px"
            v-for="(item, index) in productDetail?.specData?.specAttr"
            :key="item?.groupName"
          >
            <span class="text-26rpx text-#333333">{{ item?.groupName }}</span>
            <div class="flex items-center gap-10px flex-wrap">
              <button
                v-for="(ele, skuIndex) in item?.specItems"
                :key="ele?.specValue"
                @click="() => handleSpecChoose(ele, item?.groupName)"
                :disabled="!canSelectGroup(index, skuIndex)"
                class="custom-btn"
                :class="[
                  selectedSpecs[item.groupName] === ele.itemId ? 'btn-primary' : 'btn-info',
                  !canSelectGroup(index, skuIndex) ? 'btn-disabled' : '',
                ]"
                :title="ele?.specValue"
              >
                {{ ele?.specValue }}
              </button>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="text-24rpx text-#999999">数量</div>
          <wd-input-number
            v-model="buyNubmer"
            :min="0"
            :max="productDetail?.detail?.productStock"
          />
        </div>
        <wd-button
          :disabled="isOutOfStock"
          @click="goConfirm"
          custom-class="!w-full confirmBtn"
          block
        >
          确认
        </wd-button>
      </div>
    </wd-popup>
  </scroll-view>
</template>

<script lang="ts" setup>
import { addCart, checkIsSelf, fetchRecGoodsDetail, preBuy, toggleFav } from '@/service'
import { useUserStore } from '@/store'
import { useCartStore } from '@/store/cart'
import { useConfirmOrderStore } from '@/store/confirm-order'
import { getTotalProductCount } from '@/utils'
import { getPlatform, formatLargeNumber } from '@/utils'
import Mpservice from '@/components/mpservice/index.vue'
const userStore = useUserStore()

const cartStore = useCartStore()

const confirmOrderStore = useConfirmOrderStore()

defineOptions({
  name: 'GoodDetail',
})

const menuButtonInfo = ref({
  width: 0,
  height: 32,
  top: 20,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const { safeAreaInsets } = uni.getSystemInfoSync()

const productDetail = ref<Api.Home.GoodDetail>()

const productId = ref<string>('')

const storeId = ref<string>('')

// const productIsSelf = ref<boolean>()

const showPrice = ref('')

const selectedSpecs = ref<Record<string, number>>({}) // 以 groupName 为 key，itemId 为值

const specSkuId = ref<string>()
//是否商圈
let fromRoute = ref(null)
//是否无商品库存
const isOutOfStock = computed(() => {
  return productDetail.value?.detail?.productStock <= 0
})
/*关闭客服*/
const closeMpservice = () => {
  isMpservice.value = false
}
onLoad(async (params) => {
  if (params.id) {
    productId.value = params?.id as string
    await fetchDetail()
  }
  if (params.from) {
    fromRoute.value = params.from
  }

  storeId.value = params?.storeId ? params?.storeId : 0
})

const fetchDetail = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    const { data } = await fetchRecGoodsDetail({
      appId: import.meta.env.VITE_APPID,
      productId: productId.value,
    })
    console.log('🚀 ~ fetchDetail ~ data:', data)

    // const { data: selfFlag } = await checkIsSelf({
    //   shopSupplierId: data?.detail?.shopSupplierId,
    //   appId: import.meta.env.VITE_APPID,
    // })

    // productIsSelf.value = selfFlag

    showPrice.value = data?.detail?.productPrice

    productDetail.value = data

    confirmOrderStore.setGoodInfo(data)

    // 自动选择有库存的规格
    if (data?.detail?.specType === 20 && data?.specData) {
      const specAttr = data.specData.specAttr
      const specList = data.specData.specList || []

      // 找到第一个有库存的SKU
      const firstValidSku = specList.find((sku) => sku.productStock > 0)
      if (firstValidSku) {
        const skuIds = firstValidSku.specSkuId.split('_')
        // 自动选择对应的规格
        specAttr.forEach((group, index) => {
          selectedSpecs.value[group.groupName] = Number(skuIds[index])
        })
        showPrice.value = firstValidSku.specForm.productPrice
        specSkuId.value = firstValidSku.specSkuId
      } else {
        // 如果没有有库存的SKU，显示默认价格范围
        showPrice.value = `${data.detail.productPrice}-${data.detail.highPrice}`
      }
    }
    if (data?.detail.specType === 10 && data?.detail.skuList && data?.detail.skuList.length > 0) {
      specSkuId.value = data?.detail.skuList[0].specSkuId
      console.log('无规格', data?.detail?.specType, data?.detail.skuList[0].specSkuId)
    }
    uni.hideLoading()
  } catch (err) {
    uni.hideLoading()
    uni.showToast({
      title: '获取商品详情失败',
      duration: 1000,
      icon: 'none',
    })
  }
}

const showPrePay = ref(false)

const buttonType = ref<'buy' | 'cart'>('buy')
/*是否打开客服*/
const isMpservice = ref(false)
const handlePopUp = (type: 'buy' | 'cart') => {
  showPrePay.value = !showPrePay.value
  buttonType.value = type

  if (
    showPrePay.value &&
    productDetail.value?.detail?.specType === 20 &&
    productDetail.value?.specData
  ) {
    const specAttr = productDetail.value.specData.specAttr
    const specList = productDetail.value.specData.specList || []

    // 1. 检查当前已选规格是否完整且有效
    if (Object.keys(selectedSpecs.value).length === specAttr.length) {
      const idList = specAttr.map((group) => selectedSpecs.value[group.groupName])
      const skuId = idList.join('_')
      const validSku = specList.find((sku) => sku.specSkuId === skuId && sku.productStock > 0)

      if (validSku) {
        showPrice.value = validSku.specForm.productPrice
        specSkuId.value = validSku.specSkuId
        return
      }
    }

    // 2. 如果没有完整有效的规格选择，显示默认价格范围
    showPrice.value = productDetail.value.detail.productPrice
    if (productDetail.value.detail.highPrice !== productDetail.value.detail.productPrice) {
      // 如果有价格范围，显示范围
      showPrice.value = `${productDetail.value.detail.productPrice}-${productDetail.value.detail.highPrice}`
    }
  } else if (showPrePay.value && productDetail.value?.detail) {
    // 单规格商品
    showPrice.value = productDetail.value.detail.productPrice
  }
}

const handleClose = () => {
  showPrePay.value = false
}

const buyNubmer = ref(1)

watch(
  () => confirmOrderStore.selectedAddressId,
  (v) => {
    if (v !== null && v !== undefined) handlePreBuy()
  },
)

//直接下单
const handlePreBuy = async (callback?: () => void) => {
  if (buyNubmer.value === 0) {
    uni.showToast({ title: '请选择数量', icon: 'none' })
    return
  }

  const dataBody = {
    appId: import.meta.env.VITE_APPID,
    delivery: 10,
    isUsePoints: confirmOrderStore?.GoodInfo?.detail?.isPointsDiscount,
    paySource: getPlatform(),
    productId: `${confirmOrderStore?.GoodInfo?.detail?.productId}`,
    productNum: `${buyNubmer.value}`,
    storeId: storeId.value ?? 0,
    specSkuId: `${specSkuId?.value ?? '0'}`,
  }

  confirmOrderStore.setBuyNum(buyNubmer.value)

  confirmOrderStore.setSpecSkuId(specSkuId?.value ?? '0')

  try {
    console.log('🚀 ~ handlePreBuy ~ dataBody:', dataBody)

    const { data } = await preBuy(dataBody)

    confirmOrderStore.setOrderInfo(data)
    callback && callback()
  } catch (error) {}
}

const handleAddCart = async () => {
  if (buyNubmer.value === 0) {
    uni.showToast({ title: '请选择数量', icon: 'none' })
    return
  }
  // 检查多规格商品是否已选择规格
  const specAttrCount = productDetail.value?.specData?.specAttr?.length || 0
  if (
    productDetail.value?.detail?.specType === 20 &&
    Object.keys(selectedSpecs.value).length < specAttrCount
  ) {
    uni.showToast({ title: '请选择规格', icon: 'none' })
    return
  }
  const dataBody = {
    appId: import.meta.env.VITE_APPID,
    productId: confirmOrderStore?.GoodInfo?.detail?.productId,
    totalNum: buyNubmer.value,
    specSkuId: specSkuId.value,
    token: userStore?.token ?? '',
  }
  try {
    await addCart(dataBody)
    cartStore.getCart()
  } catch (error) {
    console.log('error', error)
    uni.showToast({
      title: error.data.msg || '添加失败，请稍后再试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    showPrePay.value = false
  }
}

const goConfirm = async () => {
  const specAttrCount = productDetail.value?.specData?.specAttr?.length || 0

  if (
    Object.keys(selectedSpecs.value).length < specAttrCount &&
    productDetail.value?.detail?.specType === 20
  ) {
    uni.showToast({ title: '请选择规格', icon: 'none' })
    return
  }

  if (buttonType.value === 'cart') {
    handleAddCart()
  } else {
    handlePreBuy(() =>
      uni.navigateTo({
        url: `/pages-sub/goods/confirm-order/index?storeId=${storeId.value ?? ''}`,
      }),
    )
  }
}

// const canSelectGroup = (groupIndex: number, skuIndex: number) => {
//   // let itemId
//   // let skuObj
//   // //查多规格sku库存，库存不足不可选
//   // //存在二级以上多规格sku
//   // if (
//   //   productDetail.value?.specData?.specAttr &&
//   //   productDetail.value?.specData?.specAttr.length > 1
//   // ) {
//   // } else {
//   //   //一级sku库存判断
//   //   itemId = productDetail.value?.specData?.specAttr[groupIndex].specItems[skuIndex].itemId
//   //   skuObj = productDetail.value?.specData?.specList.find((spc) => spc?.specSkuId == itemId)
//   //   console.log('skuObj', skuObj)
//   // }

//   // if (skuObj?.specForm?.stockNum < 1) return false
//   // 允许第一个组始终可选specList
//   if (groupIndex === 0) return true
//   // 前一个组的 groupName
//   const prevGroup = productDetail.value?.specData?.specAttr[groupIndex - 1]?.groupName
//   return !!selectedSpecs.value[prevGroup]
// }// ... 其他代码 ...
const canSelectGroup = (groupIndex: number, skuIndex: number) => {
  const specData = productDetail.value?.specData
  if (!specData) return false

  const currentItemId = specData.specAttr[groupIndex].specItems[skuIndex].itemId
  const specList = specData.specList || []

  // 1. 一级规格
  if (groupIndex === 0) {
    // 只要有任意SKU第一个规格等于当前itemId且有库存即可
    return specList.some((sku) => {
      const skuIds = sku.specSkuId.split('_')
      return skuIds[0] == currentItemId && sku.productStock > 0
    })
  }

  // 2. 多级规格
  // 检查前面规格是否都已选
  const selectedIds: (string | number)[] = []
  for (let i = 0; i < groupIndex; i++) {
    const groupName = specData.specAttr[i]?.groupName
    const selected = selectedSpecs.value[groupName]
    if (!selected) return false
    selectedIds.push(selected)
  }

  // 检查是否有SKU前面规格和当前规格都匹配且有库存
  return specList.some((sku) => {
    const skuIds = sku.specSkuId.split('_')
    // 前面规格必须完全匹配
    for (let i = 0; i < selectedIds.length; i++) {
      if (skuIds[i] != selectedIds[i]) return false
    }
    // 当前规格必须匹配
    if (skuIds[groupIndex] != currentItemId) return false
    // 有库存
    return sku.productStock > 0
  })
}
const handleSpecChoose = (item: { itemId: number; specValue: string }, groupName: string) => {
  selectedSpecs.value[groupName] = item.itemId

  // 更新价格显示
  const specAttr = productDetail.value?.specData?.specAttr || []
  const specList = productDetail.value?.specData?.specList || []

  if (Object.keys(selectedSpecs.value).length === specAttr.length) {
    const idList = specAttr.map((group) => selectedSpecs.value[group.groupName])
    const skuId = idList.join('_')
    const validSku = specList.find((sku) => sku.specSkuId === skuId && sku.productStock > 0)

    if (validSku) {
      showPrice.value = validSku.specForm.productPrice
      specSkuId.value = validSku.specSkuId
    }
  }
}

const selectedSpecText = computed(() => {
  const specAttr = productDetail.value?.specData?.specAttr || []
  const result: string[] = []

  for (const group of specAttr) {
    const selectedId = selectedSpecs.value[group.groupName]
    const selectedItem = group.specItems.find((item) => item.itemId === selectedId)
    if (selectedItem) {
      result.push(`${group.groupName}: ${selectedItem.specValue}`)
    }
  }

  return result.join(' / ')
})

const toggleFavorite = async () => {
  try {
    await toggleFav({
      appId: import.meta.env.VITE_APPID,
      productId: productId.value,
      token: userStore?.token ?? '',
      storeId: storeId.value,
    })
    fetchDetail()
  } catch (error) {}
}

const goHome = () => {
  if (fromRoute.value === 'store') {
    uni.redirectTo({
      url: `/pages-sub/home/<USER>/supplier/index?shopId=${productDetail.value?.detail?.shopSupplierId}&storeId=${storeId.value}&from=store`,
    })
  } else if (fromRoute.value === 'mall') {
    uni.redirectTo({
      url: `/pages-sub/home/<USER>/index`,
    })
  } else {
    uni.reLaunch({ url: '/pages/index/index' })
  }
}

const goCart = () => uni.navigateTo({ url: '/pages/cart/index' })

const openMaservice = () => {
  // if (productDetail.value.serviceType == 10) {
  //   isMpservice.value = true
  // } else if (productDetail.value.serviceType == 20) {
  //   if (productDetail.value.serviceUser && productDetail.value.serviceUser.serviceUserId == 0) {
  //     uni.showToast({
  //       title: '尚未设置客服',
  //       icon: 'none', //如果要纯文本，不要icon，将值设为'none'
  //       duration: 2000, //持续时间为 2秒
  //     })
  //   } else {
  //     const url = `/pages/message/chat/index?userId=${productDetail.value.serviceUser.serviceUserId}&productId=${productId.value}&shopSupplierId=${productDetail.value.detail?.supplier?.shopSupplierId}&nickName=${productDetail.value.serviceUser.nickName}&storeId=${storeId.value}`
  //     uni.navigateTo({ url })
  //   }
  // }

  if (productDetail.value.serviceUser) {
    if (productDetail.value.serviceUser && productDetail.value.serviceUser.serviceUserId == 0) {
      uni.showToast({
        title: '尚未设置客服',
        icon: 'none', //如果要纯文本，不要icon，将值设为'none'
        duration: 2000, //持续时间为 2秒
      })
    } else {
      const url = `/pages/message/chat/index?userId=${productDetail.value.serviceUser.serviceUserId}&productId=${productId.value}&shopSupplierId=${productDetail.value.detail?.supplier?.shopSupplierId}&nickName=${productDetail.value.serviceUser.nickName}&storeId=${storeId.value}`
      uni.navigateTo({ url })
    }
  } else if (productDetail.value.detail.supplier) {
    if (
      productDetail.value.detail.supplier &&
      productDetail.value.detail.supplier.serviceUserId == 0
    ) {
      uni.showToast({
        title: '尚未设置客服',
        icon: 'none', //如果要纯文本，不要icon，将值设为'none'
        duration: 2000, //持续时间为 2秒
      })
    } else {
      const url = `/pages/message/chat/index?userId=${productDetail.value.detail.supplier.serviceUserId}&productId=${productId.value}&shopSupplierId=${productDetail.value.detail?.supplier?.shopSupplierId}&nickName=${productDetail.value.serviceUser.nickName}&storeId=${storeId.value}`
      uni.navigateTo({ url })
    }
  }
}
const handleBack = () => uni.navigateBack()

const goSupplierIndex = (id: number) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${id}&storeId=${storeId.value}`,
  })
}

const goCommentList = () => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/comment-list/index?id=${productId.value}` })
}
</script>

<style lang="scss" scoped>
:deep(.cart-badge) {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}
:deep(.is-info) {
  border-color: #eeeeee !important;
}
//规格按钮
.custom-btn {
  padding: 10rpx 24rpx;
  font-size: 24rpx;
  border-radius: 12rpx;
  margin: 0;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  line-height: 1.4;
  background-color: transparent;
  border: 1rpx solid #e5e5e5;
  color: #666;
  transition: all 0.2s;

  &.btn-primary {
    border-color: #ff5704;
    color: #ff5704;
    background-color: #fff7f0;
  }

  &.btn-info {
    border-color: #e5e5e5;
    color: #666;
    background-color: #f5f5f5;
  }

  &.btn-disabled {
    background-color: #cccccc !important;
    color: white !important;
    border-color: #eeeeee !important;
    cursor: not-allowed;
  }
}
</style>

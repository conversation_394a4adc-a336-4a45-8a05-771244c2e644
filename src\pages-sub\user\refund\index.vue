<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '退款/售后',
  },
}
</route>

<template>
  <view class="flex overflow-hidden pb-2 box-border h-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="10"
      :fixed="false"
      class="flex-1"
      @query="queryList"
    >
      <template #top>
        <div class="tabs">
          <view :class="tab === 0 ? 'activeTab tab' : 'tab'" @click="switchTab(0)">
            <text>退款</text>
          </view>
          <view :class="tab === 1 ? 'activeTab tab' : 'tab'" @click="switchTab(1)">
            <text>售后</text>
          </view>
        </div>
      </template>
      <!-- 退款 -->
      <view v-if="tab === 0" class="order-list w-full">
        <view
          style="border-bottom: 1px solid #eeeeee"
          class="w-full bg-white p30rpx mb20rpx box-border"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <view class="text-28rpx flex justify-between items-center">
            <view class="flex items-center gap-x-15rpx">
              <div class="i-carbon-store text-32rpx text-black"></div>
              <text class="text-32rpx text-black" v-if="item.shopSupplierId > 0">
                {{ item.supplierName }} {{ item?.storeName ? `(${item?.storeName})` : '' }}
              </text>
            </view>
            <text class="text-28rpx text-red">{{ item.orderStatusText }}</text>
          </view>
          <!--多个商品显示-->
          <view class="product-list" @click="gotoOrder(item.orderId)">
            <!-- <scroll-view :scroll-x="true"> -->
            <view class="list pt20rpx pr100rpx box-border">
              <view
                class="flex justify-between items-center"
                v-for="(img, num) in item.product"
                :key="num"
              >
                <view class="cover w160rpx h160rpx mr10rpx">
                  <image :src="img.productImage" mode="aspectFit"></image>
                </view>
                <view class="text-28rpx flex-1">{{ img.productName }}</view>
              </view>
            </view>
            <!-- </scroll-view> -->
            <view class="py20rpx flex flex-col items-end justify-center">
              <view class="flex items-center">
                <view class="left-shadow text-24rpx">订单实付金额：</view>
                <view class="price text-22rpx theme-price">
                  ¥
                  <text class="text-40rpx theme-price">{{ item.payPrice }}</text>
                </view>
              </view>
              <view class="text-24rpx">共{{ item.totalNum }}件</view>
            </view>
          </view>
          <view class="flex flex-col justify-center items-end text-24rpx text-red">
            <text v-if="item.orderStatus == 21" class="count">退款处理中</text>
            <text v-if="item.orderStatus == 20" class="count">已退款</text>
          </view>
        </view>
      </view>
      <!-- 售后 -->
      <view v-if="tab === 1" class="content">
        <view
          class="w-full bg-white p30rpx mb20rpx box-border"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <view class="flex justify-between text-26rpx">
            <text>{{ item.createTime }}</text>
            <text class="text-red">{{ item.stateText }}</text>
          </view>
          <view class="pt20rpx flex justify-start items-center gap31rpx">
            <view class="w160rpx h160rpx">
              <image :src="item.orderProduct.productImage" mode="aspectFit"></image>
            </view>
            <view class="flex-1 pr20rpx">
              <view class="text-26rpx">{{ item.orderProduct.productName }}</view>
              <view class="pt10rpx">
                <text class="text-24rpx">{{ item.orderProduct.productAttr }}</text>
              </view>
            </view>
          </view>
          <view class="pt20rpx flex justify-end items-center text-24rpx">
            <view>
              商品金额：
              <text class="text-red">¥{{ item.orderProduct.totalPrice }}</text>
            </view>
          </view>
          <view class="pt10rpx flex justify-end items-center text-24rpx">
            <view>
              订单实付金额：
              <text class="text-red">¥{{ item.orderProduct.totalPayPrice || 0 }}</text>
            </view>
          </view>

          <view
            style="border-top: 1rpx solid #eeeeee"
            class="mt20rpx pt20rpx flex justify-end items-center gap20rpx"
          >
            <button
              v-if="
                item.isAgree == 0 &&
                item.plateStatus == 0 &&
                item.type == 30 &&
                (item.status == 0 || item.status == 10)
              "
              style="margin-right: 15rpx"
              class="m0 text-28rpx"
              @click="intervention(item.orderRefundId)"
            >
              申请平台介入
            </button>
            <text
              v-if="
                item.isAgree == 0 &&
                item.plateStatus == 10 &&
                item.type == 30 &&
                (item.status == 0 || item.status == 10)
              "
              style="margin-right: 15rpx"
              type="default"
              class="m0 text-red text-28rpx"
            >
              平台介入处理中
            </text>
            <button class="m0 text-28rpx" @click="gotoRefundDetail(item.orderRefundId)">
              查看详情
            </button>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { fetchRefundList, fetchUserOrderList, refundPlateApply } from '@/service'
import { computed, onMounted, ref } from 'vue'
// import RechargePopup from './part/recharge.vue';

// 响应式状态
const tab = ref<number>(0)
const loading = ref(false)
const tableData = ref<Api.User.UserPointsRecords[]>([])
const points = ref<number>(0)
const isOpen = ref<boolean>(false)
let dataList = ref([])

// 生命周期 - 挂载完成
onMounted(() => {})
onLoad((e) => {
  if (e.tab) {
    tab.value = Number(e.tab)
  }
})

// 生命周期 - 页面触底
onReachBottom(() => {})

const paging = ref()
const queryList = async (pageNo: number, pageSize: number) => {
  uni.showLoading({ title: '加载中' })
  try {
    if (tab.value === 0) {
      await getRefundCop(pageNo, pageSize)
    }
    if (tab.value === 1) {
      await getAfterSales(pageNo, pageSize)
    }
    uni.hideLoading()
    paging.value.complete(dataList.value)
  } catch (err) {
    uni.hideLoading()
    paging.value.complete(false)
  }
}
//获取售后数据
const getAfterSales = async (pageNo: number, pageSize: number) => {
  const { data } = await fetchRefundList({
    pageIndex: pageNo,
    pageSize: pageSize,
    appId: import.meta.env.VITE_APPID,
    state: -1,
  })
  dataList.value = data.records
}
//获取退款数据
const getRefundCop = async (pageNo: number, pageSize: number) => {
  const { data } = await fetchUserOrderList({
    pageIndex: pageNo,
    pageSize: pageSize,
    appId: import.meta.env.VITE_APPID,
    type: 'cancel',
    orderType: 0,
  })
  dataList.value = data.list.records
}
// 跳转积分商城
const gotoShop = () => {
  uni.navigateTo({ url: '/pages/plus/points/list/list' })
}
const switchTab = (index: number) => {
  tab.value = index
  paging.value?.reload()
}

// 申请平台介入
const intervention = async (orderRefundId: any) => {
  uni.showLoading({
    title: '加载中',
  })
  try {
    let res = await refundPlateApply({
      orderRefundId: orderRefundId,
      appId: Number(import.meta.env.VITE_APPID),
    })
    paging.value?.reload()
    uni.showToast({
      title: res.msg || '申请成功',
    })
  } catch (err) {
    console.log('error', err)

    uni.showToast({
      title: '申请失败请重试',
    })
  }
}

const gotoOrder = (orderRefundId: any) => {
  uni.navigateTo({ url: '/pages/plus/points/list/list' })
}

// 售后详情
const gotoRefundDetail = (orderRefundId: any) => {
  uni.navigateTo({ url: '/pages-sub/user/refund/refundDetail?order_refund_id=' + orderRefundId })
}
// 类型定义（实际项目建议单独文件）
interface PointsLogItem {
  id?: string
  description: string
  value: number
  createTime: string
}
</script>

<style lang="scss" scoped>
.tabs {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
}
.tab {
  box-sizing: border-box;
  padding: 0 34rpx;
  display: flex;
  height: 76rpx;
  justify-content: center;
  align-items: center;
  text {
    font-size: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 10rpx;
  }
}
.activeTab {
  color: #333;
  text {
    display: flex;
    border-bottom: 2px solid #ff7d26;
    font-weight: bold;
  }
}
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.loading-img {
  display: flex;
  margin-bottom: 10rpx;
}

.load1,
.load2,
.load3 {
  display: flex;
  margin: 0 5rpx;
  animation: loading 1.2s infinite ease-in-out;
}

.load1 view,
.load2 view,
.load3 view {
  width: 8rpx;
  height: 30rpx;
  margin: 0 2rpx;
  border-radius: 4rpx;
}

.load1 {
  animation-delay: -0.24s;
}
.load2 {
  animation-delay: -0.12s;
}

@keyframes loading {
  0%,
  100% {
    height: 30rpx;
  }
  50% {
    height: 15rpx;
  }
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}
</style>

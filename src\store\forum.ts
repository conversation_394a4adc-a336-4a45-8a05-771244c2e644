import { defineStore } from 'pinia'

export const useForumStore = defineStore(
  'forum',
  () => {
    const isFoucs = ref(false)

    const setIsFoucs = (val: boolean) => {
      isFoucs.value = val
    }

    const activeParentCommentId = ref<string | number>(0)

    const setActiveParentCommentId = (val: string) => {
      activeParentCommentId.value = val
    }

    const clearActiveParentCommentId = () => {
      activeParentCommentId.value = 0
    }

    return {
      activeParentCommentId,
      setActiveParentCommentId,
      clearActiveParentCommentId,
      isFoucs,
      setIsFoucs,
    }
  },
  { persist: true },
)

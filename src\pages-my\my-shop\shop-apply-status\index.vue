<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <div
    class="size-full grid grid-cols-1 grid-rows-[auto_1fr] px-20rpx pb60rpx gap-y-40rpx bg-#ffffff box-border"
  >
    <SimpleNavBar title="审核状态" />
    <div class="flex flex-col justify-between items-center">
      <div class="w-full flex-1 flex items-center justify-center">
        <div class="w-full">
          <wd-steps :active="active" align-center>
            <wd-step title="已签约" />
            <wd-step title="商户进件中" />
            <wd-step title="分账审核中" />
            <wd-step v-if="status === 1" title="审核通过" />
            <wd-step icon="close" v-if="status === 2" title="审核失败" />
          </wd-steps>
        </div>
      </div>
      <div class="w-full">
        <wd-button v-if="active === 0" @click="handleCheckEcNo" custom-class="!h90rpx" block>
          查看合同
        </wd-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { userApplyDetail } from '@/service'
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'

const { data } = useRequest(() => userApplyDetail(), { immediate: true })
const status = computed(() => data.value?.status)

const active = ref<number>(0)

watch(
  () => data?.value,
  (detail) => {
    if (!detail) return

    if (detail?.status === 2) {
      active.value = 4
      return
    }

    if (detail.contractId) {
      active.value = 2
    } else if (detail.merChantNo) {
      active.value = 1
    } else if (detail.ecNo) {
      active.value = 0
    } else {
      active.value = 0
    }
  },
  { immediate: true },
)

const handleCheckEcNo = () => {
  uni.navigateTo({ url: `/pages-sub/webview/webview?url=${data?.value?.resultUrl}` })
}
</script>

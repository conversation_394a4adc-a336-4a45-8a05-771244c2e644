<template>
  <wd-navbar
    :title="title"
    placeholder
    fixed
    left-arrow
    safeAreaInsetTop
    @click-left="handleClickLeft"
    :custom-class="customClass"
    :bordered="bordered"
  ></wd-navbar>
</template>

<script lang="ts" setup>
const props = withDefaults(
  defineProps<{ title: string; customClass?: string; bordered?: boolean; delta?: number }>(),
  {
    customClass: '',
    bordered: true,
    delta: 1,
  },
)
function handleClickLeft() {
  uni.navigateBack({ delta: props.delta })
}
</script>

<style lang="scss" scoped></style>

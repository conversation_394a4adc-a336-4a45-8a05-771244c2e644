import { defineStore } from 'pinia'

export const useSearchStore = defineStore(
  'search',
  () => {
    const searchList = ref<string[]>([])

    // 添加搜索记录（自动去重并把最新搜索放到前面）
    const addSearch = (keyword: string) => {
      if (!keyword.trim()) return
      const index = searchList.value.indexOf(keyword.trim())
      if (index !== -1) {
        searchList.value.splice(index, 1)
      }
      searchList.value.unshift(keyword.trim())
    }

    // 删除某一项搜索记录
    const removeSearch = (keyword: string) => {
      const index = searchList.value.indexOf(keyword)
      if (index !== -1) {
        searchList.value.splice(index, 1)
      }
    }

    // 清空所有搜索记录
    const clearSearch = () => {
      searchList.value = []
    }

    const searchStoreList = ref<string[]>([])

    // 添加搜索记录（自动去重并把最新搜索放到前面）
    const addStoreSearch = (keyword: string) => {
      if (!keyword.trim()) return
      const index = searchStoreList.value.indexOf(keyword.trim())
      if (index !== -1) {
        searchStoreList.value.splice(index, 1)
      }
      searchStoreList.value.unshift(keyword.trim())
    }

    // 删除某一项搜索记录
    const removeStoreSearch = (keyword: string) => {
      const index = searchStoreList.value.indexOf(keyword)
      if (index !== -1) {
        searchStoreList.value.splice(index, 1)
      }
    }

    // 清空所有搜索记录
    const clearStoreSearch = () => {
      searchStoreList.value = []
    }

    const searchKeyword = ref('')

    const setSearchKeyword = (keyword: string) => {
      searchKeyword.value = keyword
    }

    const clearSearchKeyword = () => {
      searchKeyword.value = ''
    }

    const goodsList = ref<Api.Home.GoodItem[]>([])

    const setGoodsList = (val: Api.Home.GoodItem[]) => {
      goodsList.value = val
    }

    const clearGoodsList = () => {
      goodsList.value = []
    }

    const clearAll = () => {
      clearSearchKeyword()
      clearGoodsList()
    }
    return {
      searchList,
      addSearch,
      removeSearch,
      clearSearch,

      searchStoreList,
      addStoreSearch,
      removeStoreSearch,
      clearStoreSearch,

      searchKeyword,
      setSearchKeyword,
      goodsList,
      setGoodsList,
      clearGoodsList,
      clearSearchKeyword,
      clearAll,
    }
  },
  {
    persist: true,
  },
)

const formatTime = require('../static/libs/util').formatTime
const sleep = require('../static/libs/util').sleep
const getToken = require('../static/libs/token').getToken
const SpeechSynthesizer = require('../static/libs/tts')

const fs = uni.getFileSystemManager()

interface AppConfig {
  AKID: string
  AKKEY: string
  URL: string
  APPKEY: string
}

export function useTextToSpeech(appConfig: AppConfig) {
  const ttsText = ref('')
  const ttsStart = ref(false)
  const ttsFile = ref<string | null>(null)
  let audioCtx: UniApp.InnerAudioContext | null = null

  let tts: any = null
  let saveFd: string | null = null

  const initTTS = async () => {
    const token = await getToken(appConfig.AKID, appConfig.AKKEY)

    tts = new SpeechSynthesizer({
      url: appConfig.URL,
      appkey: appConfig.APPKEY,
      token,
    })

    tts.on('meta', (msg: any) => {
      console.log('Meta info:', msg)
    })

    tts.on('data', (msg: ArrayBuffer) => {
      if (ttsFile.value) {
        try {
          fs.appendFileSync(ttsFile.value, msg, 'binary')
        } catch (e) {
          console.error('写入失败:', e)
        }
      }
    })

    tts.on('completed', async () => {
      console.log('TTS 完成')
      uni.hideLoading()
      await sleep(500)

      if (saveFd !== null) {
        fs.close({
          fd: saveFd,
          success: () => {
            audioCtx = uni.createInnerAudioContext()
            audioCtx.autoplay = true
            audioCtx.src = ttsFile.value!
            audioCtx.onPlay(() => console.log('播放中...'))
            audioCtx.onEnded(() => cleanup())
            audioCtx.onError(() => cleanup())
          },
          fail: (err) => {
            console.error('关闭文件失败', err)
          },
        })
      }
    })

    tts.on('closed', () => {
      console.log('TTS 连接关闭')
    })

    tts.on('failed', (msg: any) => {
      console.error('TTS 失败:', msg)
      cleanup()
    })
  }

  const startTTS = async (text: string, voice = 'aixia') => {
    if (!text) {
      uni.showToast({ title: '文本为空', icon: 'error' })
      return
    }
    if (ttsStart.value) {
      return
    }

    if (!tts) {
      await initTTS()
    }

    ttsStart.value = true
    ttsText.value = text
    uni.showLoading({ title: '语音合成中...', mask: true })

    const filename = `${formatTime(new Date())}.wav`
    const filePath = `${wx.env.USER_DATA_PATH}/${filename}`
    ttsFile.value = filePath

    return new Promise<void>((resolve, reject) => {
      fs.open({
        filePath,
        flag: 'a+',
        success: async (res) => {
          saveFd = res.fd
          const params = tts.defaultStartParams()
          params.text = text
          params.voice = voice

          try {
            await tts.start(params)
            console.log('TTS 启动成功')
            resolve()
          } catch (err) {
            console.error('TTS 启动失败', err)
            ttsStart.value = false
            uni.hideLoading()
            reject(err)
          }
        },
        fail: (err) => {
          console.error('文件打开失败', err)
          ttsStart.value = false
          uni.hideLoading()
          reject(err)
        },
      })
    })
  }

  const cleanup = () => {
    stopPlayback()
    if (ttsFile.value) {
      fs.unlink({
        filePath: ttsFile.value,
        success: () => {
          console.log('临时文件删除成功')
        },
        fail: (err) => {
          console.error('删除失败', err)
        },
      })
    }
    ttsFile.value = null
    saveFd = null
    ttsStart.value = false
    uni.hideLoading()
  }

  const stopPlayback = () => {
    if (audioCtx) {
      audioCtx.stop()
      audioCtx.destroy()
      audioCtx = null
    }
  }

  return {
    ttsText,
    ttsStart,
    startTTS,
    cleanup,
    stopPlayback,
  }
}

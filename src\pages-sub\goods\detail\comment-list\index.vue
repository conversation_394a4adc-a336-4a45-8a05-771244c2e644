<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>
<template>
  <div class="look-evaluate size-full flex flex-col bg-#ffffff">
    <SimpleNavBar title="商品评价" />
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="15"
      :fixed="false"
      class="flex-1"
      @query="queryList"
    >
      <template #top>
        <div class="h-100rpx w-full grid grid-cols-4">
          <div
            v-for="item in rateList"
            :key="item.label"
            @click="() => handleScoreChange(item?.value)"
            class="w-full flex items-center gap-x-5px justify-center"
          >
            <div :class="item.icon" class="text-40rpx text-#FF7D26"></div>
            <span
              :class="score === item?.value ? 'text-#333333 font-bold' : ''"
              class="text-#999999"
            >
              {{ item.label }}
            </span>
          </div>
        </div>
      </template>
      <!--评论列表-->
      <view class="comment-list px30rpx box-border">
        <view class="item" v-for="(item, index) in dataList" :key="index">
          <view class="cmt-user">
            <view class="flex items-center gap20rpx">
              <image
                class="w80rpx h80rpx rounded-40rpx bg-#eeeeee"
                :src="item.avatarUrl"
                mode="aspectFill"
              ></image>
              <text class="text-24rpx">{{ item.nickName }}</text>
            </view>
          </view>
          <view class="flex justify-between items-center p20rpx">
            <view class="" v-for="ele in rateList" :key="ele.label">
              <view v-if="item.score == ele.value" class="flex-1 flex justify-start gap-20rpx">
                <text class="icon iconfont i-hugeicons-happy"></text>
                <text class="gray9">{{ ele.label }}</text>
              </view>
              <!-- <view v-if="item.score == 20" class="d-c-c mr20rpx">
                <text class="icon iconfont i-hugeicons-meh"></text>
                <text class="ml10rpx gray9">中评</text>
              </view>
              <view v-if="item.score == 30" class="d-c-c mr20">
                <text class="icon iconfont i-hugeicons-sad-01"></text>
                <text class="ml10rpx gray9">差评</text>
              </view> -->
            </view>
            <text class="flex-1 flex justify-end text-24rpx text-#cccccc">
              {{ item.createTime }}
            </text>
          </view>
          <view class="px-20rpx text-24rpx gray3">{{ item.content }}</view>
          <view class="imgs d-s-c px20rpx">
            <view class="box" v-for="(imgs, img_num) in item.image" :key="img_num">
              <wd-img
                :src="imgs"
                :preview-src="imgs"
                :width="102"
                :height="102"
                mode="aspectFill"
                :enable-preview="true"
              />
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </div>
</template>

<script setup lang="ts">
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { fetchCommentList } from '@/service'

const rateList = [
  {
    label: '全部',
    icon: '',
    value: -1,
  },
  {
    label: '好评',
    icon: 'i-hugeicons-happy',
    value: 10,
  },
  {
    label: '中评',
    icon: 'i-hugeicons-meh',
    value: 20,
  },
  {
    label: '差评',
    icon: 'i-hugeicons-sad-01',
    value: 30,
  },
]

const productId = ref('')

const score = ref(-1)

onLoad((option) => {
  if (option) {
    productId.value = option?.id
  }
})

const paging = ref()
const dataList = ref<Api.Home.commentList[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await fetchCommentList({
      pageIndex: pageNo,
      pageSize: pageSize,
      productId: productId.value,
      score: score.value,
      appId: import.meta.env.VITE_APPID,
    })
    paging.value.complete(data?.comments?.records)
  } catch (err) {
    paging.value.complete(false)
  }
}

const handleScoreChange = (value: number) => {
  score.value = value
  paging.value?.reload()
}
</script>
<style lang="scss" scoped>
.look-evaluate .comment-list {
  background: #ffffff;
}

.look-evaluate .comment-list .item {
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  border-top: none;
  border-bottom: 1px solid #dddddd;
}

.tab-item.active {
  font-size: 30rpx;
}

.look-evaluate .iconfont {
  border-radius: 50%;
  font-size: 40rpx;
  text-align: center;
}

.look-evaluate .icon-pingjiahaoping {
  color: #f42222;
}

.look-evaluate .icon-pingjiazhongping {
  color: #f2b509;
}

.look-evaluate .icon-pingjiachaping {
  color: #999999;
}

.look-evaluate .imgs {
  flex-wrap: wrap;
}

.look-evaluate .imgs .box {
  margin-top: 10rpx;
  margin-right: 10rpx;
}

.look-evaluate .imgs .box:nth-child(3n) {
  margin-right: 0;
}

.look-evaluate .imgs .box,
.look-evaluate .imgs .box image {
  width: 210rpx;
  height: 210rpx;
}
</style>

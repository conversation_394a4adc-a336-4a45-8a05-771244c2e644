<template>
  <view
    class="recommend-product"
    v-if="state.isShow"
    :data-theme="state.theme"
    :class="state.theme || ''"
  >
    <view class="flex items-center justify-center h-70rpx">
      <text class="text-35rpx text-#333333 font-bold">{{ state.showName }}</text>
    </view>
    <view class="p-20rpx grid grid-cols-2 gap-10px">
      <view
        class="rounded-20rpx bg-white of-hidden"
        v-for="item in state.listData"
        :key="item.productId"
        @click="gotoProduct(item.productId)"
      >
        <div class="relative">
          <wd-img :src="item?.productImage" width="100%" height="460rpx" mode="aspectFit"></wd-img>
          <div
            v-if="item?.productStock <= 0"
            class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center pointer-events-none"
          >
            <span>售罄</span>
          </div>
        </div>
        <div class="w-full p-20rpx box-border grid grid-cols-1">
          <div class="text-24rpx text-#333333 line-clamp-1 font-600">
            {{ item?.productName }}
          </div>
          <!-- <div class="text-20rpx text-#FF7D26 font-500 mt4px mb12rpx">
                    超长续航蓝牙耳机热卖榜
                  </div> -->
          <div class="flex items-baseline gap-x-10px text-#FF7D26">
            <div class="flex items-baseline">
              <span class="text-20rpx font-500">¥</span>
              <span class="text-40rpx font-bold">
                {{ item?.productPrice?.split('.')?.[0] }}
              </span>
              <span class="text-24rpx font-bold">.{{ item?.productPrice?.split('.')?.[1] }}</span>
            </div>
            <span class="text-20rpx text-#666666 flex items-baseline">
              已售{{ formatLargeNumber(item?.productSales) }}+
            </span>
          </div>
        </div>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { formatLargeNumber } from '@/utils'
import { fetchRecommendProduct } from '@/service'
import { useUserStore } from '@/store'
const userStore = useUserStore()

// 组件Props
const props = defineProps<{
  location: number | string
}>()

// 响应式状态
const state = reactive({
  listData: [] as Api.Order.RecItem[],
  isShow: false,
  showName: '' as string,
  theme: '' as string,
})

// 获取推荐数据
const getData = async () => {
  state.isShow = false

  const { data } = await fetchRecommendProduct({
    location: props.location as number,
  })

  if (data.isRecommend) {
    state.isShow = true
    state.showName = data.recommendName
    state.listData = data.list
  }
}

// 生命周期钩子
onMounted(() => {
  getData()
})

// 跳转商品详情
const gotoProduct = (productId: number) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${productId}` })
}
</script>

<style lang="scss" scoped></style>

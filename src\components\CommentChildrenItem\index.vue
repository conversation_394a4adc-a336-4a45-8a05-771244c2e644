<template>
  <div class="mb-20rpx">
    <div>
      <span class="text-#999999 text-28rpx">
        {{ comment.nickName }}：
        {{ comment?.replyToNickName ? `回复${comment?.replyToNickName}：` : '' }}
      </span>
      <span class="text-#666666 text-28rpx" v-if="comment?.content">{{ comment?.content }}</span>
      <div class="w-200rpx h-200rpx" v-if="comment?.image">
        <wd-img
          :src="comment?.image"
          width="200rpx"
          height="200rpx"
          radius="16rpx"
          mode="aspectFit"
          :enable-preview="true"
        ></wd-img>
      </div>
      <span
        class="text-24rpx text-blue ml-20rpx"
        @click="handleCommentAction"
        v-if="comment?.isDelete != 1"
      >
        {{ comment.userId === userStore.userInfo.userId ? '操作' : '回复' }}
      </span>
    </div>
    <span class="text-#999999 text-22rpx mt-10rpx block">
      {{ comment?.createTime }} {{ comment?.city ? `(${comment?.city})` : '(未知)' }}
    </span>

    <!-- 子评论列表 -->
    <div v-if="comment.children?.length" class="p10rpx mt-10rpx">
      <CommentChildrenItem
        v-for="child in visibleChildren"
        :key="child.commentId"
        :comment="child"
        @showActionPopup="(payload) => emit('showActionPopup', payload)"
      />

      <!-- 展开/收起操作 -->
      <div
        v-if="hasMoreChildren"
        class="text-blue text-24rpx mt-10rpx cursor-pointer"
        @click="showMore"
      >
        展开更多回复（{{ remainingCount }}）
      </div>
      <div
        v-else-if="visibleCount > defaultVisibleCount"
        class="text-blue text-24rpx mt-10rpx cursor-pointer"
        @click="resetVisible"
      >
        收起回复
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CommentChildrenItem from '@/components/CommentChildrenItem/index.vue'
import { discourseAddComment } from '@/service'
import { useForumStore, useUserStore } from '@/store'

const forumStore = useForumStore()

const userStore = useUserStore()

// 回复
const handleCommentAction = () => {
  if (props.comment.userId === userStore.userInfo.userId) {
    // 触发父组件的操作弹窗
    emit('showActionPopup', {
      parentCommentId:
        props.comment.parentCommentId === '0'
          ? props.comment.commentId
          : props.comment.parentCommentId,
      commentId: props.comment.commentId,
    })
  } else {
    // 不是自己的评论，直接回复
    if (props.comment.parentCommentId === '0') {
      forumStore.setActiveParentCommentId(props.comment.commentId)
    } else {
      forumStore.setActiveParentCommentId(props.comment.parentCommentId)
    }
    forumStore.setIsFoucs(true)
  }
}

// 添加emit定义
const emit = defineEmits(['showActionPopup'])

const props = defineProps<{
  comment: Api.Forum.ReplyItem
}>()

const defaultVisibleCount = 10
const incrementCount = 10
const visibleCount = ref(defaultVisibleCount)

const totalChildren = computed(() => props.comment.children || [])
const visibleChildren = computed(() => totalChildren.value.slice(0, visibleCount.value))
const hasMoreChildren = computed(() => visibleCount.value < totalChildren.value.length)
const remainingCount = computed(() => totalChildren.value.length - visibleCount.value)

const showMore = () => {
  visibleCount.value += incrementCount
}
const resetVisible = () => {
  visibleCount.value = defaultVisibleCount
}
</script>

import { defineStore } from 'pinia'

const initOrderState: Api.Order.PreBuyData = {
  settledRule: null,
  orderData: null,
  supplierList: [],
  storeOpen: 0,
  templateArr: [],
}

const initGoodState: Api.Home.GoodDetail = {
  serviceType: 0,
  cartTotalNum: 0,
  commentData: [],
  serviceOpen: false,
  commentDataCount: 0,
  discount: null,
  share: null,
  detail: null,
  specData: null,
  storeOpen: 0,
  isFav: false,
  serviceUser: null,
}

export const useConfirmOrderStore = defineStore(
  'confirmOrder',
  () => {
    const OrderInfo = ref<Api.Order.PreBuyData>({ ...initOrderState })

    const setOrderInfo = (val: Api.Order.PreBuyData) => {
      OrderInfo.value = val
    }

    const clearUserInfo = () => {
      OrderInfo.value = { ...initOrderState }
    }

    const GoodInfo = ref<Api.Home.GoodDetail>({ ...initGoodState })

    const setGoodInfo = (val: Api.Home.GoodDetail) => {
      GoodInfo.value = val
    }

    const clearGoodInfo = () => {
      GoodInfo.value = { ...initGoodState }
    }

    const buyNum = ref(0)

    const setBuyNum = (val: number) => {
      buyNum.value = val
    }

    const clearBuyNum = () => {
      buyNum.value = 0
    }

    const selectedAddressId = ref(null)

    const setSelectedAddressId = (id: number) => {
      selectedAddressId.value = id
    }

    const resetSelectedAddressId = () => {
      selectedAddressId.value = null
    }

    const specSkuId = ref<string>('0')

    const setSpecSkuId = (id: string) => {
      specSkuId.value = id
    }

    const clearSpecSkuId = () => {
      specSkuId.value = null
    }

    return {
      OrderInfo,
      setOrderInfo,
      clearUserInfo,
      GoodInfo,
      setGoodInfo,
      clearGoodInfo,
      buyNum,
      setBuyNum,
      clearBuyNum,
      selectedAddressId,
      setSelectedAddressId,
      resetSelectedAddressId,
      specSkuId,
      setSpecSkuId,
      clearSpecSkuId,
    }
  },
  {
    persist: true,
  },
)

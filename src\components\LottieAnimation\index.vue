<template>
  <view>
    <canvas canvas-id="lottie" id="lottie" :style="customStyle" type="2d"></canvas>
  </view>
</template>

<script setup lang="ts">
// import animationData from '@/static/animations/Animation.json'
import lottie from 'lottie-miniprogram'

const props = withDefaults(
  defineProps<{ animationData: any; customStyle?: Partial<CSSStyleDeclaration> }>(),
  {
    customStyle: () => ({ width: '169px', height: '88px' }),
  },
)

const { proxy } = getCurrentInstance()

const aniRef = ref(null)

onMounted(() => {
  uni
    .createSelectorQuery()
    .in(proxy)
    .select('#lottie')
    .node((res) => {
      const canvas = res.node

      const context = canvas.getContext('2d')

      lottie.setup(canvas)

      aniRef.value = lottie.loadAnimation({
        renderer: 'canvas',
        loop: true,
        autoplay: true,
        animationData: props.animationData,
        rendererSettings: {
          context,
        },
      })
    })
    .exec()
})

onUnmounted(() => {
  aniRef.value.destroy()
  console.log('Lottie animation destroyed')
})
</script>

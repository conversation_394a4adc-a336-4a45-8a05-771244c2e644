<script setup lang="ts">
import { getSchemeUrl } from '@/service'
import { isWeixin, parseUrlParams, toQueryString } from '@/utils'

import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { useLocationStore } from '@/store/location'
import { getSceneData, setStorageIfValid, doLogin } from './utils'
import { useLoginSettingStore, useUserStore } from './store'

const locationStore = useLocationStore()
const userStore = useUserStore()
const loginSettingStore = useLoginSettingStore()
//#ifdef MP-WEIXIN
const accountInfo = uni.getAccountInfoSync()
//#endif

onLaunch(async (option) => {
  // uni.removeStorageSync('shopSupplierId')
  // uni.removeStorageSync('storeId')

  userStore.clearEnvVersion()

  locationStore.clearLocationInfo()

  loginSettingStore.clearLoginSetting()

  //#ifdef MP-WEIXIN
  userStore?.setEnvVersion(accountInfo?.miniProgram?.envVersion ?? '')
  //#endif
  await loginSettingStore.getLoginSetting()

  const query = option?.query || {}

  const scene = getSceneData(query)

  // 分销人id
  if (query?.refereeId || scene?.uid) {
    setStorageIfValid('refereeId', query?.refereeId || scene?.uid)
  }
  // 邀请有礼id
  if (query.invitationId) {
    setStorageIfValid('invitationId', query?.invitationId)
  }
  if (scene?.invitid) {
    setStorageIfValid('invitationId', scene?.invitid)
  }

  // 商户id
  if (query?.sid) {
    setStorageIfValid('shopSupplierId', query?.sid)
  }
  if (scene?.sid) {
    setStorageIfValid('shopSupplierId', scene?.sid)
  }

  // 门店id
  if (query?.storeId) {
    setStorageIfValid('storeId', query?.storeId)
  }
  if (scene?.storeId) {
    setStorageIfValid('storeId', scene?.storeId)
  }

  // #ifdef H5
  if (query?.appId) {
    setStorageIfValid('appId', query?.appId)
  }
  // #endif
  const hasSceneParam = !!option?.query?.scene
  const hasRefereeId = Number(option?.query?.refereeId) > 0
  const type = option?.query?.type
  uni.setStorageSync('type', type)

  if (hasSceneParam || hasRefereeId) {
    if (type !== 'offline') userStore?.clearUserInfo()
  }

  const getSchemeUrls = async () => {
    let params = {
      // path: "/pages/index/index",
      // query: "?uid=1",
    }
    const { data } = await getSchemeUrl(params)
    if (data) {
      let schemeUrl = data as string
      window.location.href = schemeUrl
    }
  }
  // #ifdef H5

  const params = parseUrlParams()
  if (params?.token) {
    userStore.setToken(params.token)
    await userStore.fetchUserCenterConfig()
  }

  if(params?.type==='noLogin'){
    uni.showModal({
      showCancel:false,
      title:'请授权微信完整服务'
    })
    return
  }

  // if (params?.type === 'offline') {
  //   const queryString = toQueryString(params)
  //   uni.navigateTo({
  //     url: `/pages-sub/offline/offline-pay?${queryString}`,
  //   })
  // }

  if (isWeixin() && !userStore.isLogined) {
    doLogin()
  } else if (isWeixin() && type == 'gzh' && userStore.isLogined) {
    getSchemeUrls()
  }
  // #endif
})

onShow(() => {
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>

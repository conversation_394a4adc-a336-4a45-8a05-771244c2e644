import { fetchUserCartList } from '@/service'
import { useUserStore } from '@/store/user'
import { defineStore } from 'pinia'

export const useCartStore = defineStore(
  'cart',
  () => {
    const userStore = useUserStore()
    const cartData = ref<Api.Home.CartData>()

    const getCart = async () => {
      try {
        const { data } = await fetchUserCartList({
          appId: import.meta.env.VITE_APPID,
          token: userStore.token,
        })
        cartData.value = data
      } catch (error) {}
    }

    const setCart = (val: Api.Home.CartData) => {
      cartData.value = val
    }

    const resetCart = () => {
      cartData.value = undefined
    }

    return {
      cartData,
      getCart,
      setCart,
      resetCart,
    }
  },
  {
    persist: true,
  },
)

<template>
  <z-paging
    ref="paging"
    v-model="dataList"
    :default-page-size="10"
    :auto-full-height="false"
    use-page-scroll
    :refresher-enabled="false"
    @query="queryList"
  >
    <div class="grid grid-cols-1 gap-y-10px">
      <div class="grid grid-cols-1 gap-y-10px">
        <div class="text-#333333 text-32rpx font-bold">店铺列表</div>
        <div class="drop-menu-container flex flex-col">
          <div class="grid grid-cols-3">
            <div
              @click="toggleTab('category')"
              class="w-full h70rpx flex items-center justify-center gap-x-5px"
            >
              <div
                :class="
                  searchParams.categoryId || searchParams.categoryPid
                    ? 'text-#FF7D26'
                    : 'text-#333333'
                "
                class="text-28rpx"
              >
                {{ categoryName }}
              </div>
              <div
                :class="`${activeTab === 'category' ? 'i-carbon-caret-up' : 'i-carbon-caret-down'} ${
                  searchParams.categoryId || searchParams.categoryPid
                    ? 'text-#FF7D26'
                    : 'text-#333333'
                }`"
                class="text-12px text-#333333"
              ></div>
            </div>
            <div
              @click="toggleTab('distance')"
              class="w-full h70rpx flex items-center justify-center gap-x-5px"
            >
              <div
                :class="searchParams.distance ? 'text-#FF7D26' : 'text-#333333'"
                class="text-28rpx text-#333333"
              >
                {{ distanceName }}
              </div>
              <div
                :class="`${activeTab === 'distance' ? 'i-carbon-caret-up' : 'i-carbon-caret-down'} ${searchParams.distance ? 'text-#FF7D26' : 'text-#333333'}`"
                class="text-12px"
              ></div>
            </div>
            <div
              @click="toggleTab('sort')"
              class="w-full h70rpx flex items-center justify-center gap-x-5px"
            >
              <div
                :class="searchParams.sortType ? 'text-#FF7D26' : 'text-#333333'"
                class="text-28rpx"
              >
                {{ sortName }}
              </div>
              <div
                :class="`${activeTab === 'sort' ? 'i-carbon-caret-up' : 'i-carbon-caret-down'} ${searchParams.sortType ? 'text-#FF7D26' : 'text-#333333'}`"
                class="text-12px text-#333333"
              ></div>
            </div>
          </div>
          <div class="content">
            <div v-if="activeTab === 'category'" class="min-h-340rpx w-full flex flex-col">
              <view class="wraper flex flex-1 of-auto">
                <wd-sidebar v-model="searchParams.categoryId" @change="handleChange">
                  <wd-sidebar-item
                    v-for="item in categories"
                    :key="item?.categoryId"
                    :value="item?.categoryId"
                    :label="item?.name"
                  />
                </wd-sidebar>
                <view class="content flex-1 bg-#eeeeee h-full">
                  <scroll-view
                    class="category"
                    scroll-y
                    scroll-with-animation
                    :show-scrollbar="false"
                    :scroll-top="scrollTop"
                    :throttle="false"
                  >
                    <div
                      :key="item?.categoryId"
                      @click="() => handleChildrenChange(item)"
                      class="p16px text-24rpx"
                      :class="
                        item?.categoryId === Number(searchParams.categoryPid)
                          ? 'text-#FF7D26'
                          : 'text-#333333'
                      "
                      v-for="item in childrenCategories"
                    >
                      {{ item?.name }}
                    </div>
                  </scroll-view>
                </view>
              </view>
            </div>
            <div
              v-if="activeTab === 'distance'"
              class="w-full px20px py15px box-border grid grid-cols-5 gap-14px"
            >
              <div
                v-for="item in distanceOption"
                :key="item.value"
                @click="() => handleDistanceChange(item)"
                :class="item.value === searchParams.distance ? 'bg-#FFECDF text-#FF7D26' : ''"
                class="h-56rpx w-full rd-3px bg-#eeeeee flex text-24rpx text-#333333 items-center justify-center"
              >
                {{ item?.label }}
              </div>
            </div>
            <div
              v-if="activeTab === 'sort'"
              class="w-full px20px py15px box-border grid grid-cols-4 gap-14px"
            >
              <div
                v-for="item in sortOption"
                :key="item.value"
                @click="() => handleSortChange(item)"
                :class="item.value === searchParams.sortType ? 'bg-#FFECDF text-#FF7D26' : ''"
                class="h-56rpx w-full rd-3px bg-#eeeeee text-24rpx flex text-#333333 items-center justify-center"
              >
                {{ item?.label }}
              </div>
            </div>
          </div>

          <!-- <wd-drop-menu :direction="dropDirection">
                <wd-drop-menu-item :before-toggle="handleBeforeToggle" :title="categoryName">
                  <div class="max-h-720rpx min-h-340rpx w-full flex flex-col">
                    <view class="wraper flex flex-1 of-auto">
                      <wd-sidebar v-model="searchParams.categoryId" @change="handleChange">
                        <wd-sidebar-item
                          v-for="item in categories"
                          :key="item?.categoryId"
                          :value="item?.categoryId"
                          :label="item?.name"
                        />
                      </wd-sidebar>
                      <view class="content flex-1">
                        <scroll-view
                          class="category"
                          scroll-y
                          scroll-with-animation
                          :show-scrollbar="false"
                          :scroll-top="scrollTop"
                          :throttle="false"
                        >
                          <div
                            :key="item?.categoryId"
                            @click="() => handleChildrenChange(item)"
                            class="p16px text-28rpx"
                            :class="
                              item?.categoryId === searchParams.categoryPid
                                ? 'text-#FF7D26'
                                : 'text-#333333'
                            "
                            v-for="item in categories[
                              categories?.findIndex(
                                (ele) => ele?.categoryId === searchParams?.categoryId,
                              ) ?? 0
                            ]?.children ?? []"
                          >
                            {{ item?.name }}
                          </div>
                        </scroll-view>
                      </view>
                    </view>
                  </div>
                </wd-drop-menu-item>
                <wd-drop-menu-item :before-toggle="handleBeforeOtherToggle" :title="distanceName">
                  <div class="w-full px20px py15px box-border grid grid-cols-5 gap-14px">
                    <div
                      v-for="item in distanceOption"
                      :key="item.value"
                      @click="() => handleDistanceChange(item)"
                      :class="item.value === searchParams.distance ? 'bg-#FFECDF text-#FF7D26' : ''"
                      class="h-56rpx w-full rd-3px bg-#F2F2F2 flex text-#333333 items-center justify-center"
                    >
                      {{ item?.label }}
                    </div>
                  </div>
                </wd-drop-menu-item>
                <wd-drop-menu-item :before-toggle="handleBeforeOtherToggle" :title="sortName">
                  <div class="w-full px20px py15px box-border grid grid-cols-4 gap-14px">
                    <div
                      v-for="item in sortOption"
                      :key="item.value"
                      @click="() => handleSortChange(item)"
                      :class="item.value === searchParams.sort ? 'bg-#FFECDF text-#FF7D26' : ''"
                      class="h-56rpx w-full rd-3px bg-#F2F2F2 flex text-#333333 items-center justify-center"
                    >
                      {{ item?.label }}
                    </div>
                  </div>
                </wd-drop-menu-item>
              </wd-drop-menu> -->
        </div>
      </div>
      <div class="grid grid-cols-1 gap-y-20px grid-auto-rows-min">
        <template v-if="loading">
          <view v-for="item in 10" :key="item" class="flex gap-x-8px">
            <wd-skeleton
              animation="gradient"
              theme="avatar"
              :row-col="[{ size: '160rpx', type: 'rect' }]"
            />
            <wd-skeleton
              animation="gradient"
              theme="paragraph"
              :custom-style="{ width: '100%', marginLeft: '12px' }"
              :row-col="[{ width: '100%' }, { width: '100%' }, { width: '100%' }]"
            />
          </view>
        </template>
        <div
          v-for="item in dataList"
          :key="item?.storeId"
          class="flex gap-x-8px items-center"
          @click="goSupplierDetail(item?.shopSupplierId, item?.storeId, item?.storeName)"
        >
          <wd-img
            width="160rpx"
            height="160rpx"
            mode="aspectFill"
            radius="10px"
            :src="item?.logoFilePath"
          ></wd-img>
          <div class="flex-1 grid grid-cols-1 gap-y-13px">
            <div class="grid grid-cols-1 gap-y-2px">
              <div class="flex items-center justify-between gap-x-5px">
                <span
                  v-if="item?.storeType === 30"
                  class="text-14px text-#333333 font-500 flex-1 line-clamp-1"
                >
                  {{ item?.supplierName }}({{ item?.storeName }})
                </span>
                <span v-else class="text-14px text-#333333 font-500 flex-1 line-clamp-1">
                  {{ item?.storeName }}
                </span>
                <span class="text-#FF2828 text-20rpx">
                  {{
                    getStatusBytime(item?.businessStartTime, item?.businessEndTime)
                      ? '营业中'
                      : '停业中'
                  }}
                </span>
              </div>
              <div class="flex items-center gap-x-5px">
                <wd-rate
                  :modelValue="getScore(item?.serverScore, 1)"
                  readonly
                  active-color="#FF2828"
                />
                <span class="text-20rpx text-#FF2828 font-500">{{ item?.serverScore }}</span>
              </div>
              <span class="text-24rpx text-#999999 line-clamp-1">
                {{ item?.area.replace(/\//g, '') }}{{ item?.address }}
              </span>
            </div>
            <div class="flex items-center justify-between">
              <div v-if="false" class="flex items-center">
                <div
                  class="bg-#FF7D26 rd-2px text-24rpx p2px box-border flex items-center justify-center text-#803909"
                >
                  积
                </div>
                <div class="text-24rpx bg-#FFC39B text-#FF8533 rd-tr-2px rd-br-2px p2px box-border">
                  最高返{{ item?.supplierCommissionRate }}%
                </div>
              </div>
              <div></div>
              <div class="flex items-end">
                <div class="i-carbon-location text-14px text-#333333"></div>
                <span class="text-#999999 text-20rpx">
                  {{ item?.distance ? `${item?.distance.toFixed(2)}km` : '' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </z-paging>
</template>
<script setup lang="ts">
import { fetchIndexStore, fetchAddress, fetchSupplierCategory } from '@/service'
import { getScore } from '@/utils'
import { useLocationStore } from '@/store'
import { useLoginSettingStore } from '@/store'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
const loginSettingStore = useLoginSettingStore()

const env = ref('')

// #ifdef MP-WEIXIN
const {
  miniProgram: { envVersion },
} = uni.getAccountInfoSync()
env.value = envVersion
// #endif
// const emits = defineEmits<{ scrollIntoView: [id: string] }>()

const searchParams = ref({
  categoryId: '',
  categoryPid: '',
  distance: '',
  sortType: '',
})

const activeTab = ref('') // '', 'category', 'distance', 'sort'

const toggleTab = (tab) => {
  // emits('scrollIntoView', activeTab.value === tab ? '' : 'myTarget')

  activeTab.value = activeTab.value === tab ? '' : tab
}

const scrollTop = ref<number>(0)
const { data: category } = useRequest(() => fetchSupplierCategory(), { immediate: true })

const categories = computed({
  get: () => category.value ?? [],
  set: (val) => {
    category.value = val
  },
})

const selectedCategory = computed(() =>
  categories.value.find(
    (ele) => ele?.categoryId === (searchParams.value.categoryId as unknown as number),
  ),
)
const childrenCategories = computed(() => selectedCategory.value?.children ?? [])

const categoryName = ref('全部分类')

const allCategory: any = {
  categoryId: '',
  isVisible: 'Y',
  name: '全部分类',
}

watch(
  () => categoryName.value,
  (newVal) => {
    console.log(newVal, 'newval')

    if (newVal !== '全部分类') {
      if (categories.value.indexOf(allCategory) === -1) {
        categories.value.unshift(allCategory)
      }
    } else {
      categories.value = categories.value.filter((item: any) => item.categoryId !== '')
    }
  },
)

const getStatusBytime = (businessStartTime: string, businessEndTime: string): boolean => {
  dayjs.extend(isBetween)
  const now = dayjs()

  // 提取传入时间的 HH:mm:ss 并构造成今天的时间
  const start = dayjs(now.format('YYYY-MM-DD') + ' ' + dayjs(businessStartTime).format('HH:mm:ss'))
  const end = dayjs(now.format('YYYY-MM-DD') + ' ' + dayjs(businessEndTime).format('HH:mm:ss'))
  let isShow = now.isBetween(start, end, 'second', '[]') // [] 表示包含开始结束
  return isShow
}

const handleChange = ({ value }) => {
  searchParams.value.categoryPid = ''
  searchParams.value.categoryId = value

  const current = categories.value.find((c) => c.categoryId === value)

  if (!current?.children?.length) {
    categoryName.value = current?.name || '全部分类'
    activeTab.value = ''
    paging.value?.reload()
  }
  // else {
  //   // 有子类，等待选择子类
  //   scrollTop.value = -1
  //   nextTick(() => (scrollTop.value = 0))
  // }
}
const handleChildrenChange = ({ name, categoryId }) => {
  searchParams.value.categoryPid = categoryId
  categoryName.value = name
  activeTab.value = '' // 选中后关闭面板
  paging.value?.reload()
}

const distanceName = ref('附近')
const distanceOption = ref([
  { label: '附近', value: '' },
  { label: '1km', value: 1 },
  { label: '2km', value: 2 },
  { label: '5km', value: 5 },
  { label: '10km', value: 10 },
  { label: '20km', value: 20 },
  { label: '30km', value: 30 },
  { label: '40km', value: 40 },
])

const handleDistanceChange = ({ label, value }) => {
  searchParams.value.distance = value
  distanceName.value = label
  activeTab.value = '' // 选中后关闭面板
  paging.value?.reload()
}

const sortName = ref('智能排序')
const sortOption = ref([
  { label: '智能排序', value: '' },
  { label: '评分优先', value: 2 },
  { label: '距离优先', value: 3 },
])
const handleSortChange = ({ label, value }) => {
  searchParams.value.sortType = value
  sortName.value = label
  activeTab.value = '' // 选中后关闭面板
  paging.value?.reload()
}

const locationStore = useLocationStore()

const handleFetchLocation = (callback: () => void) => {
  uni.getLocation({
    type: 'wgs84',
    altitude: false,
    success: async (result) => {
      if (result?.errMsg === 'getLocation:ok') {
        try {
          const { data } = await fetchAddress({
            lon: result?.longitude,
            lat: result?.latitude,
            ver: 1,
          })

          if (loginSettingStore.loginSetting?.isAudit && env.value === 'trial') {
            locationStore.setLocationInfo({
              poi:
                JSON.parse((data as unknown as string) ?? '')?.result?.addressComponent?.poi ?? '',
              cityName:
                JSON.parse((data as unknown as string) ?? '')?.result?.addressComponent?.city ?? '',
              location: {
                lat: 32.00335,
                lon: 118.73145,
              },
            })
          } else {
            locationStore.setLocationInfo({
              poi:
                JSON.parse((data as unknown as string) ?? '')?.result?.addressComponent?.poi ?? '',
              cityName:
                JSON.parse((data as unknown as string) ?? '')?.result?.addressComponent?.city ?? '',
              location: {
                lat: result?.latitude,
                lon: result?.longitude,
              },
            })
          }

          callback && callback()
        } catch (error) {
          console.log('🚀 ~ success: ~ error:', error)
        }
      } else {
        locationStore.setLocationInfo({
          poi: '',
          cityName: '',
          location: {
            lat: 32.00335,
            lon: 118.73145,
          },
        })
      }
    },
    fail: (e) => {
      console.log('🚀 ~ e:', e)
      console.log('fail')
    },
    complete: () => {},
  })
}

const paging = ref()
const dataList = ref<Api.Home.HomeStoreListItem[]>([])
const loading = ref(false)
const queryList = async (pageNo: number, pageSize: number) => {
  loading.value = true
  try {
    const handleData = async () => {
      const { data: store } = await fetchIndexStore({
        pageIndex: pageNo,
        pageSize,
        longitude: locationStore?.locationInfo?.location?.lon,
        latitude: locationStore?.locationInfo?.location?.lat,
        categoryId: searchParams.value?.categoryPid || searchParams.value?.categoryId,
        distance: searchParams.value.distance,
        sortType: searchParams.value.sortType,
      })
      if (store?.records && store?.records.length > 0) {
        store?.records.forEach((item) => {
          if (!item.serverScore) {
            item.serverScore = '5'
          }
        })
      }
      loading.value = false

      await nextTick()

      paging.value.complete(store?.records ?? [])
    }

    if (!locationStore?.locationInfo) {
      handleFetchLocation(handleData)
    } else {
      handleData()
    }

    // uni.getSetting({
    //   success(res) {
    //     if (res.authSetting['scope.userLocation']) {
    //       // 已授权，直接获取位置
    //       handleFetchLocation(handleData)
    //     } else {
    //       // 未授权，发起授权请求
    //       uni.authorize({
    //         scope: 'scope.userLocation',
    //         success() {
    //           handleFetchLocation(handleData)
    //         },
    //         fail() {
    //           uni.showModal({
    //             title: '提示',
    //             content: '需要获取您的位置信息，请前往设置开启权限。',
    //             success(res) {
    //               if (res.confirm) {
    //                 uni.openSetting()
    //               }
    //             },
    //           })
    //         },
    //       })
    //     }
    //   },
    // })

    // if (!locationStore?.locationInfo) {
    //   uni.authorize({
    //     scope: 'scope.userLocation',
    //     success: () => {
    //       handleFetchLocation(handleData)
    //     },
    //     fail() {
    //       uni.showModal({
    //         title: '提示',
    //         content: '需要获取您的位置信息，请前往设置开启权限。',
    //         success(res) {
    //           if (res.confirm) {
    //             uni.openSetting({
    //               success(settingData) {
    //                 if (settingData.authSetting['scope.userLocation']) {
    //                   handleFetchLocation(handleData)
    //                 }
    //               },
    //             })
    //           }
    //         },
    //       })
    //     },
    //   })
    // } else {
    //   handleData()
    // }
  } catch (err) {
    setTimeout(() => {
      loading.value = false
    }, 10)
    paging.value.complete(false)
  }
}

watch(
  () => locationStore?.locationInfo,
  (newVal) => {
    if (newVal) {
      paging.value?.reload()
    }
  },
)

const pageReachBottom = () => {
  paging.value?.pageReachBottom()
}

defineExpose({
  pageReachBottom,
})

const goSupplierDetail = (shopSupplierId: number, storeId: number, storeName: string) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${shopSupplierId}&storeId=${storeId}&storeName=${storeName}`,
  })
}
</script>

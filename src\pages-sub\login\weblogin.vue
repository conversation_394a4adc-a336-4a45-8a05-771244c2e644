<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '登录',
  },
}
</route>

<template>
  <view class="login-container">
    <view class="skip" @click="gotoPage('/pages/index/index')">跳过→</view>
    <view v-if="state.isLogin !== 0">
      <view class="login_topbpx">
        <view class="login_tit" v-if="state.isLogin === 2">快速登录</view>
        <view class="login_tit" v-if="state.isLogin === 1">账号登录</view>
        <view class="login_top" v-if="state.isLogin === 2">首次登录会自动注册</view>
      </view>
      <view class="group-bd">
        <view class="form-level d-s-c">
          <input
            class="login-input"
            :adjust-position="false"
            type="text"
            v-model="state.formData.mobile"
            placeholder="请填写手机号"
            :disabled="state.isSend"
          />
        </view>
        <view class="form-level flex items-center" v-if="state.isLogin === 1">
          <input
            class="login-input flex-1"
            :adjust-position="false"
            type="password"
            v-model="state.formData.password"
            placeholder="请输入密码"
          />
          <button class="get-code-btn text-#ff5704" @click="handleForgotPassword">
            忘记密码？
          </button>
        </view>

        <view class="form-level flex items-center" v-if="state.isLogin === 2 && state.smsOpen">
          <input
            class="login-input flex-1"
            type="number"
            v-model="state.formData.code"
            placeholder="请填写验证码"
          />
          <button class="get-code-btn" @click="handleSendCode" :disabled="state.isSend">
            {{ state.sendBtnTxt }}
          </button>
        </view>
      </view>
    </view>

    <view v-if="state.isLogin === 0">
      <view class="login_topbpx">
        <view class="login_tit">找回密码</view>
        <view class="login_top">请确认您的账号已绑定此手机号码</view>
      </view>
      <view class="group-bd text-28rpx">
        <view class="form-level flex items-center h98rpx">
          <input
            type="text"
            v-model="state.resetForm.mobile"
            placeholder="请填写手机号"
            :disabled="state.isSend"
          />
        </view>
        <view class="form-level flex items-center">
          <view class="flex-1 flex items-center h98rpx">
            <input
              class="flex-1"
              :adjust-position="false"
              type="number"
              v-model="state.resetForm.code"
              placeholder="请填写验证码"
            />
            <button class="get-code-btn" @click="handleSendCode" :disabled="state.isSend">
              {{ state.sendBtnTxt }}
            </button>
          </view>
        </view>
        <view class="form-level flex items-center h98rpx">
          <input
            type="password"
            :adjust-position="false"
            v-model="state.resetForm.password"
            placeholder="请输入密码"
          />
        </view>
        <view class="form-level flex items-center h98rpx">
          <input
            type="password"
            :adjust-position="false"
            v-model="state.resetForm.repassword"
            placeholder="请再次输入密码"
          />
        </view>
      </view>
    </view>

    <view class="flex items-center mt30rpx text-24rpx">
      <!--      <view-->
      <!--        class="icon iconfont icon-tijiaochenggong"-->
      <!--        :class="{ 'active agreement': state.isAgree, agreement: true }"-->
      <!--      ></view>-->
      <wd-checkbox v-model="state.isAgree" @click="toggleAgree"></wd-checkbox>
      我已阅读并接受
      <text class="text-#ff5704" @click="openAgreement('service')">《用户协议》</text>
      和
      <text class="text-#ff5704" @click="openAgreement('privacy')">《隐私政策》</text>
    </view>

    <button
      v-if="state.isLogin === 2"
      class="rounded-30rpx text-white bg-#ff5704 sub-btn"
      @click="handleLogin"
    >
      立即登录
    </button>
    <button
      v-if="state.isLogin === 1"
      class="rounded-30rpx text-white bg-#ff5704 fw-bold sub-btn"
      @click="handleLogin"
    >
      立即登录
    </button>
    <button
      v-if="state.isLogin === 0"
      class="rounded-30rpx text-white bg-#ff5704 sub-btn"
      @click="handleResetPassword"
    >
      确认
    </button>

    <view class="py-20rpx flex justify-center items-center text-#666666 text-30rpx">
      <view v-if="state.isLogin === 1" @click="switchToMobileLogin">手机登录</view>
      <view v-if="state.isLogin === 2" @click="switchToAccountLogin">账号登录</view>
      <view v-if="state.isLogin === 0" @click="switchToLogin">立即登录</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { fetchStoreList, getCodeType, getPolicy, sendCode, userResetpassword } from '@/service'

const userStore = useUserStore()

defineOptions({
  name: 'Login',
})

// 类型定义
interface FormData {
  mobile: string
  password: string
  code: string
  refereeId: string
  invitationId: string
}

interface ResetForm {
  mobile: string
  code: string
  password: string
  repassword: string
}

// 响应式状态
const state = reactive({
  formData: {
    mobile: '',
    password: '',
    code: '',
    refereeId: 0,
    invitationId: 0,
  } as FormData,
  resetForm: {
    mobile: '',
    code: '',
    password: '',
    repassword: '',
  } as ResetForm,
  isSend: false,
  sendBtnTxt: '获取验证码',
  second: 60,
  isLogin: 1, // 0: 找回密码, 1: 账号登录, 2: 手机登录
  smsOpen: false,
  isAgree: true,
  theme: '',
})

// 获取验证码开关状态
const fetchLoginSettings = async () => {
  try {
    const { data } = await getCodeType({})
    state.smsOpen = data.h5SmsOpen
    if (!state.smsOpen) {
      state.isLogin = 1
    }
  } catch (error) {}
}

// 生命周期钩子
onMounted(() => {
  fetchLoginSettings()
})

const redirect = ref('')
onLoad((option) => {
  if (option.redirect) {
    redirect.value = decodeURIComponent(option.redirect)
  }
})

// 发送验证码
const handleSendCode = async () => {
  const mobile = state.isLogin === 0 ? state.resetForm.mobile : state.formData.mobile
  const types = state.isLogin === 0 ? 'sms' : 'login'
  console.log(state.isLogin, types, mobile)
  if (!/^1[3-9]\d{9}$/.test(mobile)) {
    uni.showToast({ title: '手机号格式错误', icon: 'none' })
    return
  }
  state.isSend = true
  state.sendBtnTxt = `${state.second}秒`
  state.second--

  const timer = setInterval(() => {
    if (state.second <= 0) {
      clearInterval(timer)
      state.isSend = false
      state.sendBtnTxt = '获取验证码'
      state.second = 60
      return
    }
    state.sendBtnTxt = `${state.second}秒`
    state.second--
  }, 1000)

  try {
    const { data } = await sendCode({
      mobile: mobile,
      type: types,
    })
    console.log(data)
    // 模拟发送验证码请求
    uni.showToast({ title: '验证码发送成功', icon: 'none' })
  } catch (error) {
    console.log(error)
  }
}

//登录
const getUserInfo = () => {
  console.log('获取用户信息')
}
// 处理登录
const handleLogin = () => {
  if (!state.isAgree) {
    uni.showToast({ title: '请阅读并同意用户协议及隐私政策', icon: 'none' })
    return
  }
  uni.showLoading({ title: '正在登录' })
  if (state.isLogin === 2) {
    if (state.smsOpen && !state.formData.code) {
      uni.showToast({ title: '验证码不能为空', icon: 'none' })
      return
    }
    // 手机登录逻辑
    console.log('手机登录:', state.formData)
    userStore.smsLogin(state.formData)
  } else {
    if (!state.formData.password) {
      uni.showToast({ title: '密码不能为空', icon: 'none' })
      return
    }
    // 账号登录逻辑
    console.log('账号登录:', state.formData)
    userStore.phoneLogin(state.formData)
  }
  setTimeout(() => {
    uni.hideLoading()
  }, 1500)
}

// 处理密码重置
const handleResetPassword = async () => {
  if (!/^1[3-9]\d{9}$/.test(state.resetForm.mobile)) {
    uni.showToast({ title: '手机号格式错误', icon: 'none' })
    return
  }
  if (!state.resetForm.code) {
    uni.showToast({ title: '验证码不能为空', icon: 'none' })
    return
  }
  if (state.resetForm.password.length < 6) {
    uni.showToast({ title: '密码至少6位', icon: 'none' })
    return
  }
  if (state.resetForm.password !== state.resetForm.repassword) {
    uni.showToast({ title: '两次密码不一致', icon: 'none' })
    return
  }

  uni.showLoading({ title: '正在重置密码' })
  try {
    const { data } = await userResetpassword(state.resetForm)
    console.log(data)
    uni.showToast({ title: '密码重置成功', icon: 'none' })
  } catch (error) {
    console.log(error)
  } finally {
    uni.hideLoading()
  }
}

// 切换到手机登录
const switchToMobileLogin = () => {
  state.isLogin = 2
}

// 切换到账号登录
const switchToAccountLogin = () => {
  state.isLogin = 1
}

// 切换到登录页面
const switchToLogin = () => {
  state.isLogin = 1
}

// 处理忘记密码
const handleForgotPassword = () => {
  state.isLogin = 0
}

// 打开协议页面
const openAgreement = (type: 'service' | 'privacy') => {
  uni.navigateTo({
    url: `/pages-sub/webview/ue?type=${type}`,
  })
}

// 切换协议勾选状态
const toggleAgree = () => {
  //   state.isAgree = !state.isAgree
}

// 跳转页面
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
</script>

<style lang="scss" scoped>
// @import "./login.scss"; // 保持原样式引入方式

.login-container {
  height: 100%;
  padding: 30rpx;
  background: #fff;

  .skip {
    text-align: right;
    padding: 20rpx;
    font-size: 28rpx;
    color: #666;
  }

  .login_topbpx {
    margin-bottom: 40rpx;

    .login_tit {
      font-size: 40rpx;
      font-weight: bold;
      color: rgb(153, 153, 153) #333;
    }

    .login_top {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #999;
    }
  }

  .form-level {
    margin-bottom: 30rpx;
    border-bottom: 1rpx solid #e5e5e5;

    .login-input {
      height: 80rpx;
      font-size: 28rpx;
    }

    .get-code-btn {
      background: none;
      font-size: 24rpx;
      margin-left: 20rpx;
    }
  }

  .sub-btn {
    margin-top: 60rpx;
    height: 90rpx;
    line-height: 90rpx;
    font-size: 30rpx;
  }

  .agreement {
    font-size: 28rpx;
    margin-right: 10rpx;
    color: #666;

    &.active {
      color: #18a0fb;
    }
  }
}
</style>

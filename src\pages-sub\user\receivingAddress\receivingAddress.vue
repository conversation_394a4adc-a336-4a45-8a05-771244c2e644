<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '设置地址',
  },
}
</route>
<template>
  <view v-if="!loading">
    <view v-if="listData.length > 0" class="pbenv">
      <view class="address-list bg-white">
        <view
          class="py20rpx"
          @click="radioChange(item.addressId)"
          v-for="(item, index) in listData"
          :key="index"
        >
          <view class="info flex-1">
            <view class="flex items-center justify-between">
              <text>{{ item.name }}</text>
              <text class="text-#999 text-26rpx">{{ item.phone }}</text>
            </view>
            <view class="pt20rpx text-#333 text-26rpx mb10rpx">
              {{ item.region.province }}{{ item.region.city }}{{ item.region.region
              }}{{ item.detail }}
            </view>
          </view>
          <view class="flex items-center justify-between">
            <view class="">
              <radio
                style="transform: scale(0.6)"
                :color="getThemeColor()"
                :value="item.addressId + ''"
                :checked="defaultId == item.addressId"
              />
              <text class="text-black text-26rpx">默认地址</text>
            </view>
            <view class="flex items-center text-#999 text-26rpx gap-30rpx">
              <view class="icon-box plus flex gap-10rpx" @click.stop="delAddress(item.addressId)">
                <image
                  class="add_icon_img"
                  src="https://file.shanqianqi.com/image/2025/06/24/864018dc3394420298d996cc2d990acc.png"
                  mode="aspectFill"
                ></image>
                <text class="gray9">删除</text>
              </view>
              <view class="none_line"></view>
              <view class="icon-box plus flex gap-10rpx" @click.stop="editAddress(item.addressId)">
                <image
                  class="add_icon_img"
                  src="https://file.shanqianqi.com/image/2025/06/24/7197972a90a14375a144396fc8c08b2c.png"
                  mode="aspectFill"
                ></image>
                <text class="gray9">编辑</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="p30 flex items-center justify-center" v-else>
      <!-- <image
        class="list-null-image"
        src="/static/list-null.png"
        mode=""
      ></image> -->
      <text class="text-32rpx text-#999">暂无地址信息</text>
    </view>
    <view class="add-btn theme-btn bg-#ff5704 text-white" @click="addAddress()">新增收货地址</view>
  </view>
</template>

<script setup lang="ts">
import { fetchUserAddressList, setUserAddressDefault, userAddressDelete } from '@/service'
import { useUserStore } from '@/store'
import { reactive, ref } from 'vue'
import { useConfirmOrderStore } from '@/store/confirm-order'

const confirmOrderStore = useConfirmOrderStore()

const userStore = useUserStore()

// 定义数据类型
interface Region {
  province: string
  city: string
  region: string
}
// 修改 AddressItem 接口定义
interface AddressItem {
  addressId: number
  name: string
  phone: string
  region: Region
  detail: string
}

// 响应式数据
const loading = ref(true)
const listData = ref<AddressItem[]>([])
const defaultId = ref(0)
const options = reactive<{ source?: string }>({})

const needBack = ref(false)
// 生命周期钩子
onLoad((opts: any) => {
  if (opts?.delta) needBack.value = true
  options.source = opts.source
})

onShow(() => {
  uni.showLoading({
    title: '加载中',
  })
  getData()
})

const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
// 在 getData 模拟请求中使用以下数据

// 获取数据
const getData = async () => {
  const { data } = await fetchUserAddressList({
    token: userStore.token,
    appId: import.meta.env.VITE_APPID,
  })
  listData.value = data.list
  defaultId.value = data.defaultId
  loading.value = false
  uni.hideLoading()
}

// 新增地址
const addAddress = () => {
  let delta = 1
  if (options.source === 'order') {
    delta = 2
  }
  // gotoPage(`/pages/user/address/add/add?delta=${delta}`)
  // 在当前页面跳转时传递参数
  uni.navigateTo({
    url: `/pages-sub/user/receivingAddress/addReceivingAddress?delta=${delta}`,
  })
}

// 单选按钮变更
const radioChange = async (id: number) => {
  // defaultId.value = id
  await setUserAddressDefault({
    token: userStore.token,
    appId: import.meta.env.VITE_APPID,
    addressId: id,
  })
  const { data } = await fetchUserAddressList({
    token: userStore.token,
    appId: import.meta.env.VITE_APPID,
  })
  confirmOrderStore.setSelectedAddressId(data.defaultId)
  getData()
  if (needBack.value) uni.navigateBack()
  // _post(
  //   'user/address/setDefault',
  //   {
  //     addressId: id,
  //   },
  //   (res: any) => {
  //     if (options.source === 'order') {
  //       // #ifndef H5
  //       uni.navigateBack()
  //       // #endif
  //       // #ifdef H5
  //       history.go(-1)
  //       // #endif
  //     }
  //     getData()
  //   },
  // )
}

// 编辑地址
const editAddress = (id: number) => {
  gotoPage(`/pages-sub/user/receivingAddress/settingReceivingAddress?addressId=${id}`)
  // gotoPage(`/pages-sub/user/receivingAddress/settingReceivingAddress=${id}`)
}

// 删除地址
const delAddress = (id: number) => {
  uni.showModal({
    title: '提示',
    content: '您确定要移除当前收货地址吗?',
    success: async (o) => {
      if (o.confirm) {
        await userAddressDelete({
          token: userStore.token,
          appId: import.meta.env.VITE_APPID,
          addressId: id,
        })
        const { data } = await fetchUserAddressList({
          token: userStore.token,
          appId: import.meta.env.VITE_APPID,
        })
        confirmOrderStore.setSelectedAddressId(data.defaultId)
        getData()
        // _get(
        //   'user/address/delete',
        //   {
        //     addressId: id,
        //   },
        //   (result: any) => {
        //     if (result.code === 1) {
        //       uni.showToast({
        //         title: '删除成功',
        //         duration: 2000,
        //       })
        //       getData()
        //     }
        //   },
        // )
      }
    },
  })
}

// 获取主题颜色
const getThemeColor = () => {
  // 实际项目中应返回主题色值
  return '#ff5704'
}

// 模拟请求方法（实际项目中替换为真实接口调用）
const _get = (url: string, params: any, callback: (res: any) => void) => {
  // 模拟异步请求
  setTimeout(() => {
    callback({
      data: {
        list: [
          {
            addressId: 1,
            name: '张三',
            phone: '13800138000',
            region: {
              province: '广东省',
              city: '深圳市',
              region: '南山区',
            },
            detail: '科技园123号',
          },
        ],
        defaultId: 1,
      },
    })
  }, 500)
}

const _post = (url: string, params: any, callback: (res: any) => void) => {
  // 模拟异步请求
  setTimeout(() => {
    callback({ code: 1 })
  }, 500)
}

// 主题函数
const theme = () => {
  return '' // 实际项目中实现主题逻辑
}
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.address-list {
  border-top: 16rpx solid #f2f2f2;
  padding: 0 20rpx;
  padding-bottom: 90rpx;
}

.foot-btns {
  padding: 0;
}
.add-btn {
  position: fixed;
  left: 24rpx;
  bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  margin: 20rpx auto;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 692rpx;
  height: 88rpx;
  border-radius: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: 800;
}
.foot-btns .btn-red {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 0;
}

.none_add {
  padding: 314rpx 214rpx 60rpx 214rpx;
}

.no_add {
  width: 322rpx;
  height: 180rpx;
}

.no_add_add {
  width: 320rpx;
  height: 80rpx;
  border: 2rpx solid #ffb7b7;
  border-radius: 40rpx;
  text-align: center;
  line-height: 80rpx;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 500;
  margin: 0 auto;
}

.add_add {
  height: 64rpx;
  line-height: 64rpx;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #0777cd;
  padding: 0 35rpx;
  border-bottom: 1rpx solid #d9d9d9;
}

.defaul_add {
  padding: 9rpx 14rpx 10rpx 15rpx;
  font-size: 22rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #f6220c;
}

.add_icon_img {
  width: 30rpx;
  height: 30rpx;
}

.none_line {
  width: 1rpx;
  height: 44rpx;
  background: #d9d9d9;
}
.add_add-btn {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  width: 690rpx;
  margin: 20rpx 30rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pbenv {
  padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
  box-sizing: border-box;
}
</style>

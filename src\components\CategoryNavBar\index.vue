<template>
  <div
    :style="{ paddingTop: menuButtonInfo.top + 'px', background: themeStore?.navColor }"
    class="pb20rpx transition-all-300"
  >
    <div
      :style="{
        height: menuButtonInfo.height + 'px',
      }"
      class="box-border flex gap-x-20px pr-26rpx"
    >
      <div @click="handleBack" class="flex justify-between w-full">
        <div class="i-carbon-chevron-left text-22px text-white font-bold"></div>
        <span class="text-18px text-white font-bold">
          {{ leftTitle }}
        </span>
        <div />
      </div>
    </div>
    <div
      :style="{ height: menuButtonInfo.height + 'px' }"
      class="flex items-center flex-1 of-hidden px-26rpx box-border gap-x-16rpx mt10px"
    >
      <div
        class="flex flex-1 of-hidden items-center h-full of-hidden box-border pr-1px gap-x-10rpx rd-30rpx"
      >
        <FakeSearch
          class="flex-1 of-hidden h-full"
          :handle-go-search="handleSearch"
          :keywords="keywords"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import FakeSearch from '@/components/FakeSearch/index.vue'
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()
defineProps<{ leftTitle?: string; keywords: string[] }>()

const menuButtonInfo = ref({
  width: 0,
  height: 32,
  top: 20,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const handleBack = () => uni.navigateBack()

// 搜索
const handleSearch = () => {
  uni.navigateTo({ url: '/pages-sub/search/index?from=foods' })
}
</script>

<style lang="scss" scoped>
//
</style>

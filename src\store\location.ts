import { defineStore } from 'pinia'

interface LocationInfo {
  poi: string
  cityName?: string
  location: {
    lat: number
    lon: number
  }
}

export const useLocationStore = defineStore(
  'location',
  () => {
    const locationInfo = ref<LocationInfo>()

    const setLocationInfo = (info: LocationInfo) => {
      locationInfo.value = info
    }

    const clearLocationInfo = () => {
      locationInfo.value = undefined
    }

    return {
      locationInfo,
      setLocationInfo,
      clearLocationInfo,
    }
  },
  { persist: true },
)

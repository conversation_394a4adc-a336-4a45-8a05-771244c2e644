<template>
  <view class="apply-agent">
    <view class="banner d-c-c d-c" v-if="topBackground !== ''">
      <image :src="topBackground" mode="widthFix" style="height: 390px"></image>
    </view>

    <!-- 申请成功 -->
    <template v-if="!isApplying">
      <view class="form-wrap text-30rpx">
        <view class="text-40rpx flex justify-center py20rpx box-border word-title">
          {{ words.apply?.wordTitle || '' }}
        </view>
        <form>
          <view class="form-item border-b">
            <view class="field-name">邀请人</view>
            {{ refereeName }}
          </view>
          <view class="form-item border-b">
            <view class="field-name">姓名</view>
            <input
              class="flex-1 ml20"
              type="text"
              v-model="form.name"
              placeholder-class="grary"
              placeholder="请输入姓名"
            />
          </view>
          <view class="form-item border-b">
            <view class="field-name">手机号</view>
            <input
              class="flex-1 ml20"
              type="number"
              v-model="form.mobile"
              placeholder-class="grary"
              placeholder="请输入手机"
            />
          </view>
          <!-- <view class="form-item border-b">
            <view class="field-name">身份证</view>
            <input
              class="flex-1 ml20"
              type="text"
              v-model="form.identity"
              placeholder-class="grary"
              placeholder="请输入身份证"
            />
          </view>
          <view class="form-item border-b">
            <view class="field-name">银行卡</view>
            <input
              class="flex-1 ml20"
              type="text"
              v-model="form.bankCard"
              placeholder-class="grary"
              placeholder="请输入银行卡"
            />
          </view>
          <view class="form-item border-b">
            <view class="field-name">开户行</view>
            <input
              class="flex-1 ml20"
              type="text"
              v-model="form.bankName"
              placeholder-class="grary"
              placeholder="请输入开户行"
            />
          </view> -->
          <!-- <view class="d-c-c p-20-0 f28">
            <checkbox-group @change="changeFunc">
              <checkbox value="checkbox" :checked="isRead" />
            </checkbox-group>
            <text>我已阅读并了解</text>
            <text class="red" @click="isPopup = true">
              【{{ words.apply?.license || "" }}】
            </text>
          </view> -->
          <view class="mt30">
            <template>
              <!-- <button class="btn-red" @click="formSubmit">提交</button> -->
              <wd-button
                type="primary"
                custom-class="!mx-26rpx "
                size="large"
                @click="formSubmit"
                block
              >
                提交
              </wd-button>
            </template>
          </view>
        </form>
      </view>
    </template>

    <!-- 分销商审核中 -->
    <template v-if="isApplying">
      <view class="d-c-c pt30">
        <text style="font-size: 100rpx" class="icon iconfont icon-icon_xianshi-xian"></text>
      </view>
      <view class="px30rpx text-30rpx text-center">
        {{ words.apply?.waitAudit || '' }}
      </view>
      <view class="p30 d-c-c">
        <button type="primary" class="btn-red" @click="gotoShop">
          {{ words.apply?.gotoMall || '' }}
        </button>
      </view>
    </template>

    <!-- 协议弹窗 -->
    <!-- <Popup :show="isPopup" msg="申请协议">
      <scroll-view class="agreement-content f25 lh150" scroll-y @scroll="handleScroll">
        <view v-html="agreement"></view>
      </scroll-view>
      <view class="ww100 pt20 d-c-c">
        <button type="primary" class="btn-red" @click="isPopup = false">
          我已阅读
        </button>
      </view>
    </Popup> -->
  </view>
</template>

<script setup lang="ts">
import { agentApply, agentApplyStatus } from '@/service/api/agent'
import { useUserStore } from '@/store'

const userStore = useUserStore()
// 定义表单数据类型
interface FormData {
  name: string
  mobile: string
  //   refereeId?: string
  identity: string
  bankCard: string
  bankName: string
}

// 响应式数据
const isPopup = ref(false)
const isRead = ref(false)
const agreement = ref('')
const isApplying = ref(false)
const refereeName = ref('平台')
const words = ref<{
  apply?: {
    wordTitle?: string
    license?: string
    submit?: string
    waitAudit?: string
    gotoMall?: string
  }
}>({})
const form = reactive<any>({
  name: '',
  mobile: '',
  // refereeId: '',
  identity: '',
  bankCard: '',
  bankName: '',
})
const topBackground = ref('')
const temlIds = ref<string[]>([])

// 生命周期 - 组件挂载
onMounted(() => {
  getData()
})

// 获取数据
const getData = async () => {
  uni.showLoading({ title: '加载中' })
  // 模拟接口数据
  //   const mockData = {
  //     apply: '/static/apply-bg.png',
  //     isApplying: false,
  //     refereeName: '张三',
  //     words: {
  //       apply: {
  //         wordTitle: '申请成为分销商',
  //         license: '分销商协议',
  //         submit: '提交申请',
  //         waitAudit: '审核中，客服将尽快处理',
  //         gotoMall: '前往商城',
  //       },
  //     },
  //     templateArr: ['template-id-123'],
  //     license: { license: '<p>协议内容</p>' },
  //   }
  try {
    const { data } = await agentApplyStatus({
      ...form,
      appId: import.meta.env.VITE_APPID,
    })

    if (userStore?.userInfo?.mobile) form.mobile = userStore?.userInfo?.mobile

    topBackground.value = data.apply
    isApplying.value = data.isApplying
    refereeName.value = data.refereeName || '平台'
    words.value = data.words
    temlIds.value = data.templateArr
    agreement.value = data.license.license
    uni.setNavigationBarTitle({ title: words.value.apply?.wordTitle || '' })
    uni.hideLoading()
  } catch (err) {}
}

// 提交表单
const formSubmit = async () => {
  const formdata = form

  if (!formdata.name) {
    uni.showToast({ title: '请输入姓名！', icon: 'none' })
    return
  }
  if (!formdata.mobile) {
    uni.showToast({ title: '请输入手机号！', icon: 'none' })
    return
  }
  // if (!formdata.identity) {
  //   uni.showToast({ title: '请输入身份证！', icon: 'none' })
  //   return
  // }
  // if (!formdata.bankCard) {
  //   uni.showToast({ title: '请输入银行卡！', icon: 'none' })
  //   return
  // }
  // if (!formdata.bankName) {
  //   uni.showToast({ title: '请输入开户行！', icon: 'none' })
  //   return
  // }
  if (formdata.mobile.length !== 11) {
    uni.showToast({ title: '手机有误,请重填！', icon: 'none' })
    return
  }
  // const idRegex = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/
  // if (!idRegex.test(formdata.identity)) {
  //   uni.showToast({ title: '身份证有误,请重填！', icon: 'none' })
  //   return
  // }
  // const bankCardRegex = /^\d{16,19}$/ // 银行卡号通常为16到19位数字
  // if (!bankCardRegex.test(formdata.bankCard)) {
  //   uni.showToast({ title: '银行卡号有误,请重填！', icon: 'none' })
  //   return
  // }
  //   if (!isRead.value) {
  //     uni.showToast({ title: '请同意协议！', icon: 'none' })
  //     return
  //   }

  uni.showLoading({ title: '正在提交', mask: true })
  // 实际提交逻辑
  try {
    const res = await agentApply({
      ...form,
    })
    uni.hideLoading()
    uni.showToast({ title: res.msg || '申请成功' })
    uni.navigateTo({ url: '/pages/my/index' })
  } catch (err) {
    uni.hideLoading()
    uni.showToast({ title: '申请失败，请稍后重试' })
  }
}

// 前往商城
const gotoShop = () => {
  uni.navigateTo({
    url: '/pages/index/index',
  })
}

// 协议状态变更
const changeFunc = (e: any) => {
  isRead.value = e.detail.value.length > 0
}

// 弹窗滚动处理
const handleScroll = (e: any) => {
  // 滚动处理逻辑
}
</script>

<style lang="scss" scoped>
/* 样式保持与原代码一致，无需修改 */
.apply-agent {
  background-color: #f5f5f5;
}

.banner {
  height: 300rpx;
  overflow: hidden;
  margin-bottom: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.banner image {
  width: 100%;
}

.form-wrap {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  font-size: 30rpx;
}

.form-item {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.field-name {
  width: 120rpx;
  font-size: 32rpx;
  color: #333;
}

.flex-1 {
  flex: 1;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin: 0 30rpx;
}

.red {
  color: #ff3333;
  text-decoration: underline;
}

.btn-red {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #ff3333;
  color: #fff;
  border-radius: 45rpx;
  font-size: 34rpx;
}

.agreement-content {
  height: 600rpx;
  padding: 30rpx;
  color: #666;
}

.ww100 {
  width: 100%;
}
.word-title {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  font-weight: bold;
  font-size: 40rpx;
  color: #333333;
}
.border-b {
  border-bottom: 1rpx solid #eeeeee;
}
.d-c-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
.p-20-0 {
  padding: 20rpx 0;
}
.f28 {
  font-size: 28rpx;
}
.mt30 {
  margin-top: 30rpx;
}
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="">
    <z-paging
      ref="paging"
      empty-view-text="没有发布哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      :paging-style="{ background: '#F7F7FA', 'padding-bottom': `${bottom || 15}px` }"
      fixed
      :show-scrollbar="false"
    >
      <template #top>
        <wd-navbar left-arrow safeAreaInsetTop fixed placeholder @click-left="handleBack">
          <template #title>
            <view class="">
              <span class="ml-20rpx">我的发布</span>
            </view>
          </template>
        </wd-navbar>
      </template>
      <div class="list content-container">
        <div
          class="li mb-20rpx"
          v-for="(item, index) in dataList"
          :key="item?.postId"
          @click="details(item.postId)"
        >
          <div class="li-top flex justify-between">
            <div class="li-top-l flex">
              <image :src="item?.avatarUrl"></image>
              <div class="flex flex-col justify-center">
                <span class="text-24rpx mb-5rpx">{{ item?.nickName }}</span>
                <div class="flex">
                  <span class="text-22rpx text-#999999">{{ item?.updateTime }}</span>
                </div>
              </div>
            </div>
            <div class="" @click.stop="showPicker(item.postId)">
              <img
                src="https://file.shanqianqi.com/image/2025/06/25/10abd4489e3b46308255f1e8560e938b.png"
                alt=""
                class="h-30rpx w-30rpx"
              />
            </div>
          </div>
          <div class="li-cont">
            <span class="text-26rpx">
              {{ item?.title }}
            </span>
          </div>
          <div class="li-img mt-15rpx" v-if="item?.images?.length > 0">
            <image
              v-for="(img, imgIndex) in item?.images"
              :key="imgIndex"
              :src="img"
              class="w-160rpx h-160rpx rounded-16rpx mr-15rpx mb-15rpx"
            ></image>
          </div>
          <div class="li-more mt-15rpx flex items-center justify-around">
            <div class="flex items-center gap-x-5rpx">
              <image
                class="w-40rpx h-40rpx"
                src="https://file.shanqianqi.com/image/2025/06/24/b17beaacc78944b69ec4f85d17876218.png"
              ></image>
              <span>{{ formatCount(item?.collectCount ?? 0) }}</span>
            </div>
            <div class="flex items-center gap-x-5rpx">
              <image
                class="w-40rpx h-40rpx"
                src="https://file.shanqianqi.com/image/2025/06/24/35a0af52d1f444ff9b67413b8e85159f.png"
              ></image>
              <span>{{ formatCount(item?.commentsCount ?? 0) }}</span>
            </div>
            <div class="flex items-center gap-x-5rpx">
              <image
                class="w-40rpx h-40rpx"
                src="https://file.shanqianqi.com/image/2025/06/24/91d17d7940f84f96b477ee4f31c11494.png"
              ></image>
              <span>{{ formatCount(item?.likesCount ?? 0) }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 删除确认选择器 -->
      <wd-action-sheet
        title="确认删除发布内容"
        v-model="showActionSheet"
        :actions="actions"
        @select="handleActionSelect"
        cancel-text="取消"
      ></wd-action-sheet>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { fetchMyArticle, discourseDeleteDiscourse } from '@/service'

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const paging = ref()
const dataList = ref<Api.Forum.MyArticleItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await fetchMyArticle({ pageIndex, pageSize, mainCategoryId: '1' })
      paging?.value?.complete(data?.records)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

// 格式化数字显示
const formatCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  }
  return count.toString()
}

// 操作菜单相关
const showActionSheet = ref(false)
const currentIndex = ref('')
const actions = ref([
  {
    name: '删除',
    color: '#FF4D4F',
  },
])

// 显示选择器
const showPicker = (e: string) => {
  currentIndex.value = e
  showActionSheet.value = true
}

// 处理选择操作
const handleActionSelect = async ({ item }: { item: { name: string } }) => {
  if (item.name === '删除') {
    uni.showModal({
      title: '提示',
      content: '确定要删除这发布内容吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            uni.showLoading({ title: '删除中...', mask: true })

            await discourseDeleteDiscourse({
              postId: currentIndex.value,
            })
            uni.hideLoading()
            uni.showToast({ title: '删除成功', duration: 2000, icon: 'success' })
            paging.value?.reload()
          } catch (error) {
            console.error('删除失败:', error)
            uni.showToast({
              title: '删除失败',
              icon: 'none',
            })
          } finally {
            uni.hideLoading()
            showActionSheet.value = false
          }
        }
      },
    })
  } else {
    showActionSheet.value = false
  }
}

//列表详情
const details = (e) => {
  console.log('e', e)
  uni.navigateTo({
    url: `/pages-category/forum/details/index?postId=${e}`,
  })
}
// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1, // 返回的页面数
  })
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
}

.list {
  margin-top: 25rpx;
  padding: 0 25rpx;
  .li {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx 40rpx;
    .li-top-l {
      image {
        width: 70rpx;
        height: 70rpx;
        border-radius: 20rpx;
        background-color: #ffe5d4;
        margin-right: 20rpx;
      }
    }
    .li-cont {
      margin-top: 10rpx;
      line-height: 1.6;
    }
    .li-more {
      span {
        margin-left: 10rpx;
        color: #666;
      }
    }
  }
}
</style>

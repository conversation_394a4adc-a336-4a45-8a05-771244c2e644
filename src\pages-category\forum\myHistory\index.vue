<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '全部浏览历史',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f6f6f6 pb-2 box-border h-full">
    <z-paging
      ref="paging"
      empty-view-text="没有浏览记录哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      :paging-style="{ background: '#F7F7FA' }"
      fixed
      :show-scrollbar="false"
    >
      <template #top>
        <wd-navbar
          left-arrow
          :bordered="false"
          safeAreaInsetTop
          fixed
          placeholder
          @click-left="handleBack"
        >
          <template #title>
            <view class="">
              <span class="ml-20rpx">全部浏览历史</span>
            </view>
          </template>
        </wd-navbar>
      </template>
      <view class="pl-25rpx pr-25rpx mt-25rpx">
        <!-- 列表循环 -->
        <div class="overflow-hidden mb-25rpx" v-for="item in dataList" :key="item?.postId">
          <div class="flex bg-#ffffff rounded-15rpx p-30rpx flex-col">
            <div class="flex items-center" @click="goDetail(item.postId)">
              <div class="text-#333333 text-26rpx flex-1 mr-15rpx">
                {{ item?.title }}
              </div>
              <div class="w-120rpx h-120rpx rounded-16rpx mr-15rpx">
                <img :src="item?.images?.[0] || ''" class="w-120rpx h-120rpx rounded-16rpx" />
              </div>
            </div>
            <div class="flex justify-between items-center mt-10rpx">
              <div class="flex items-center">
                <img
                  :src="item?.avatarUrl || ''"
                  class="w-70rpx h-70rpx rounded-50% bg-#FF7D26 mr-15rpx"
                />
                <span class="text-#333333 text-26rpx">{{ item?.nickName }}</span>
              </div>
              <div class="flex">
                <span class="text-#666666 text-22rpx">
                  {{ item?.updateTime ?? item?.createTime }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </view>
      <template #bottom>
        <div
          @click="handleClear"
          :style="{ paddingBottom: `${bottom || 15}px` }"
          class="bg-#ffffff border-#f5f5f5 w-100% h-160rpx flex items-center justify-center text-#333333 text-36rpx pt-20rpx box-border"
        >
          清空全部
        </div>
      </template>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { fetchUserFootprints, clearFootprints } from '@/service'

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const paging = ref()
const dataList = ref<Api.Forum.LikeAndCollectionItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await fetchUserFootprints({ pageIndex, pageSize })
      paging?.value?.complete(data?.records)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

// 删除项目

const handleClear = async () => {
  try {
    await clearFootprints()

    uni.showToast({
      title: '清除成功',
      icon: 'none',
      mask: true,
    })

    paging.value?.reload()
  } catch (error) {}
}
// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1, // 返回的页面数
  })
}
//进入详情
const goDetail = (e) => {
  uni.navigateTo({
    url: `/pages-category/forum/details/index?postId=${e}`,
  })
}
</script>

<style lang="scss" scoped>
.action {
  border-radius: 0 15rpx 15rpx 0;
}

.li-r {
  transition: all 0.3s;

  &:active {
    opacity: 0.7;
  }
}
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="20"
      :auto="false"
      fixed
      @query="queryList"
    >
      <template #top>
        <SimpleNavBar title="提现明细" />
        <wd-tabs @change="handleTabChange" :model-value="tab" auto-line-width>
          <block v-for="item in tabs" :key="item.key">
            <wd-tab :title="`${item.label}`" :name="item.key"></wd-tab>
          </block>
        </wd-tabs>
      </template>
      <div class="grid grid-cols-1 px24rpx box-border gap-y-20rpx pt20rpx">
        <div
          v-for="item in dataList"
          :key="item.id"
          class="h-100rpx border-rd-20rpx bg-[#ffffff] px30rpx py20rpx flex items-center justify-between box-border px68rpx relative gap-x-20rpx"
        >
          <div class="w-92rpx h-84rpx absolute top-0 left-0">
            <wd-img
              width="92rpx"
              height="84rpx"
              :src="statusMap[item?.applyStatus]"
              mode="aspectFill"
            ></wd-img>
          </div>
          <div class="flex items-baseline gap-y-5px text-24rpx text-#333333">
            <span class="text-24rpx text-#999999">提现日期：</span>
            <span class="text-24rpx text-#999999">{{ item?.createTime ?? '' }}</span>
          </div>
          <div
            v-if="item.applyStatus !== 50"
            class="text-24rpx flex items-center gap-x-10px flex-1 justify-end"
          >
            <!-- <span :class="item.applyStatusText === '审核通过' ? 'text-#32d500' : 'text-#999999'">
              {{ item.applyStatusText }}
            </span> -->
            <span :class="item?.applyStatus === 30 ? 'text-#DDDDDD' : 'text-#FF4040'">
              {{ item.money }}元
            </span>
          </div>
          <!-- #ifdef  MP-WEIXIN -->
          <wd-button
            v-if="item.applyStatus === 50"
            @click="handleCheckWx"
            custom-class="!m-unset"
            size="small"
          >
            确认收款
          </wd-button>
          <!-- #endif -->
          <!-- #ifdef  H5 -->
          <wd-button
            v-if="isWeixin() && item.applyStatus === 50"
            custom-class="!m-unset"
            @click="handleCheckMp"
            size="small"
          >
            确认收款
          </wd-button>
          <!-- #endif -->
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { userCashList, userCashReceipt, getWxSignPackage } from '@/service'
import { isWeixin, getPlatform } from '@/utils'
//#ifdef H5
import jweixin from 'weixin-js-sdk'
//#endif

const tabs = ref([
  { label: '全部', key: -1 },
  { label: '待提现', key: 10 },
  { label: '已提现', key: 40 },
  { label: '已驳回', key: 30 },
])

const status = ref(-1)

const statusMap = {
  10: 'https://file.shanqianqi.com/image/2025/06/12/ce7672e700c0483f89e23409161eb67d.png',
  40: 'https://file.shanqianqi.com/image/2025/06/12/f5558433ce4849f0b816f6b4c7ea0989.png',
  30: 'https://file.shanqianqi.com/image/2025/06/12/2d33951db9854b9b8adc318c819ded85.png',
}

const tab = ref(0)

const paging = ref()
const dataList = ref<Api.User.CashListItem[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await userCashList({
      pageIndex: pageNo,
      pageSize,
      status: status.value,
    })
    paging.value.complete(data?.list?.records ?? [])
  } catch (err) {
    paging.value.complete(false)
  }
}

onLoad((e) => {
  if (e?.type) {
    status.value = Number(e?.type)
    tab.value = tabs.value.findIndex((item) => item.key === Number(e.type))
  }
  setTimeout(() => {
    console.log('🚀 ~ onLoad ~ status:', status)

    paging.value?.reload()
  }, 500)
})

const handleTabChange = ({ name }) => {
  status.value = name
  nextTick(() => {
    paging.value?.reload()
  })
}

const handleCheckWx = (item: Api.User.CashListItem) => {
  const id = item.id
  const mchId = item.mchId
  const appId = item.wxAppId
  const packageInfo = item.packageInfo
  if (uni.canIUse('requestMerchantTransfer')) {
    uni.requestMerchantTransfer({
      mchId: mchId,
      appId: appId,
      package: packageInfo,
      success: async (res) => {
        // res.err_msg将在页面展示成功后返回应用时返回ok，并不代表付款成功
        console.log('success:', res)

        await userCashReceipt({ id })

        nextTick(() => {
          paging.value?.reload()
        })
      },
      fail: (res) => {
        console.log('fail:', res)
      },
    })
  } else {
    uni.showModal({
      content: '你的微信版本过低，请更新至最新版本。',
      showCancel: false,
    })
  }
}

const handleCheckMp = async (item: Api.User.CashListItem) => {
  const id = item.id
  const mchId = item.mchId
  const appId = item.wxAppId
  const packageInfo = item.packageInfo

  // #ifdef H5
  const { data: signPackage } = await getWxSignPackage({
    url: window.location.href,
    paySource: getPlatform(),
  })

  jweixin.config({
    debug: false,
    appId: signPackage?.signPackage?.appId,
    timestamp: signPackage.signPackage?.timestamp,
    nonceStr: signPackage.signPackage?.nonceStr,
    signature: signPackage.signPackage?.signature,
    jsApiList: ['requestMerchantTransfer'] as any,
  })

  jweixin.ready(function () {
    jweixin.checkJsApi({
      jsApiList: ['requestMerchantTransfer'] as any,
      success: function (res) {
        if (res.checkResult['requestMerchantTransfer']) {
          WeixinJSBridge.invoke(
            'requestMerchantTransfer',
            {
              mchId: mchId,
              appId: appId,
              package: packageInfo,
            },
            async (res) => {
              if (res.err_msg === 'requestMerchantTransfer:ok') {
                await userCashReceipt({ id })

                nextTick(() => {
                  paging.value?.reload()
                })
              }
            },
          )
        } else {
          alert('你的微信版本过低，请更新至最新版本。')
        }
      },
    })
  })

  jweixin.error(function (res) {
    console.log(res)
  })
  // #endif
}
</script>

<style lang="scss" scoped>
//
</style>

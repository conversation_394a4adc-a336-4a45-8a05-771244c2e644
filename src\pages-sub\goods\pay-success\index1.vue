<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="container">
    <!-- 成功状态 -->
    <view class="status-section">
      <image class="success-icon" src="/static/success.png"></image>
      <text class="status-text">支付成功</text>
      <text class="amount">¥ {{ orderInfo.payPrice }}</text>
    </view>

    <!-- 订单信息 -->
    <view class="order-info">
      <view class="info-item">
        <text class="label">订单编号：</text>
        <text class="value">{{ orderInfo.orderNo }}</text>
      </view>
      <view v-if="orderInfo.onlineMoney > 0" class="info-item">
        <text class="label">在线支付金额：</text>
        <text class="value">{{ orderInfo.onlineMoney }}</text>
      </view>
      <view v-if="orderInfo.balance > 0" class="info-item">
        <text class="label">余额支付抵扣：</text>
        <text class="value">{{ orderInfo.balance }}</text>
      </view>
      <view v-if="orderInfo.payShanBean > 0" class="info-item">
        <text class="label">善豆支付抵扣：</text>
        <text class="value">{{ orderInfo.payShanBean }}</text>
      </view>
      <view v-if="orderInfo.payRedPocket > 0" class="info-item">
        <text class="label">红包支付抵扣：</text>
        <text class="value">{{ orderInfo.payRedPocket }}</text>
      </view>
      <view class="info-item">
        <text class="label">支付时间：</text>
        <text class="value">{{ orderInfo.payTime }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <!-- #ifdef MP-WEIXIN -->
      <button class="btn secondary" @click="navigateToIndex">返回首页</button>
      <!-- #endif -->
      <button class="btn primary" @click="goOrderList">查看订单</button>
    </view>
    <!-- #ifdef H5 -->
    <wx-open-launch-weapp
      id="launch-btn"
      v-if="isWx && Object.keys(signPackage).length"
      appid="wxd5477367f0b4e8b7"
    >
      <component :is="'script'" type="text/wxtag-template" style="display: block">
        <slot>
          <div style="width: 100%; display: flex">
            <button
              style="
                height: 90rpx;
                border-radius: 45rpx;
                font-size: 34rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 20px;
                background: linear-gradient(to right, #ff6a4c, #ff396c);
                color: #fff;
                border: none;
              "
              class="btn primary"
            >
              立即前往善新年小程序
            </button>
          </div>
        </slot>
      </component>
    </wx-open-launch-weapp>

    <!--      <button v-if="isWx" class="btn secondary" @click="toXcx">善新年小程序</button>-->
    <button v-if="isAli && userDetail.mobile" class="btn primary" @click="navigateToIndex">
      返回首页
    </button>
    <button v-if="isAli && !userDetail.mobile" class="btn primary" @click="bain">绑定手机号</button>
    <!-- #endif -->

    <!-- 底部提示 -->
    <!-- <view class="tips">
      <view>支付成功后，订单已进入处理流程</view>
      <view>如有疑问，请联系客服：xxx-xxx-xxxx</view>
    </view> -->

    <kfPopupDengl ref="refpopup3" style="width: 100%" />
  </view>
</template>

<script>
import { bindMobile1, getIndex, getSchemeUrl, getWxSignPackage, paySuccessDetail } from '@/service'
import { isWeixin, isAlipay, gotoPage, getPlatform, mpMessage } from '@/utils'
import kfPopupDengl from '@/components/kf-popup-dengl/kf-popup-dengl.vue'

export default {
  components: { kfPopupDengl },
  data() {
    return {
      orderInfo: {},
      isWx: false,
      isAli: false,
      signPackage: {},
      form: {
        phone: '',
        password: '',
        rpassword: '',
      },
      userDetail: {},
    }
  },
  onLoad(options) {
    // 实际开发中应从路由参数获取订单信息
    if (options.orderId) {
      this.getData(options.orderId)
    }
    // #ifdef H5
    if (isWeixin()) {
      // this.getSchemeUrls()
      this.getTemplateId()
      this.isWx = true
    }
    if (isAlipay()) {
      this.getUserData()
      this.isAli = true
    }
    // #endif
  },
  // computed
  //       checkIsWeixin() {
  //     return isWeixin()
  //   },
  //   checkIsAlipay() {
  //     return isAlipay()
  //   },
  // },
  methods: {
    async getTemplateId() {
      // #ifdef H5
      const { data: signPackage } = await getWxSignPackage({
        url: window.location.href,
        paySource: getPlatform(),
      })
      this.signPackage = signPackage.signPackage
      mpMessage(signPackage.signPackage)
      // #endif
    },

    async getData(orderId) {
      try {
        const { data } = await paySuccessDetail({
          orderId: orderId,
        })
        this.orderInfo = data
      } catch (error) {}
    },

    // 获取验证码开关状态
    async getUserData() {
      try {
        const { data } = await getIndex({
          source: getPlatform(),
        })
        this.userDetail = data.user
      } catch (error) {}
    },

    async getSchemeUrls() {
      let params = {
        // path: "/pages/index/index",
        // query: "?uid=1",
      }
      try {
        const { data } = await getSchemeUrl(params)
        if (data) {
          this.schemeUrl = data
        }
      } catch (error) {}
    },

    // 返回首页
    navigateToIndex() {
      uni.reLaunch({
        url: '/pages/index/index',
      })
    },

    toXcx() {
      location.href = this.schemeUrl
      return
    },

    bain() {
      let that = this
      // #ifdef H5
      if (isAlipay()) {
        that.$refs.refpopup3.open({
          title: '绑定手机号',
          cancelText: '取消',
          confirmText: '确定',
          success: (e) => {
            console.log('e', e)
            that.form.phone = e.phoneNumber
            that.bainMobile()
          },
          fail: () => {},
          complete: () => {},
        })
      }
      // #endif
    },

    async bainMobile() {
      let self = this
      if (this.form.phone !== '') {
        let type = 'xcx'
        //#ifdef H5
        if (isWeixin()) {
          type = 'gzh'
        } else if (isAlipay()) {
          type = 'zfb'
        }
        //#endif
        //#ifdef APP
        type = 'app'
        //#endif
        // 绑定手机号

        try {
          const { data } = await bindMobile1({
            phone: this.form.phone,
            password: this.form.password,
            rpassword: this.form.rpassword,
            type: type,
          })
          console.log('data', data)
          if (data == '绑定成功') {
            self.$refs.refpopup3.close()
            this.bainPhone = false
            uni.removeStorageSync('token')
            uni.removeStorageSync('userId')
            uni.removeStorageSync('mobile')
            // 获取登录前页面
            let url = '/' + uni.getStorageSync('currentPage')
            let pageOptions = uni.getStorageSync('currentPageOptions')
            if (Object.keys(pageOptions).length > 0) {
              url += '?'
              for (let i in pageOptions) {
                url += i + '=' + pageOptions[i] + '&'
              }
              url = url.substring(0, url.length - 1)
            }
            // 执行回调函数
            gotoPage(url, 'reLaunch')
          }
        } catch (error) {
          console.log(error)
        } finally {
          uni.hideLoading()
        }
      }
    },

    // 查看订单
    goOrderList() {
      uni.reLaunch({ url: '/pages-sub/myOrder/index?dataType=all' })
    },
  },
}
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f8f8f8;
  min-height: 100vh;
  //padding: 40rpx;
}

.status-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 80rpx 0 60rpx;

  .success-icon {
    width: 120rpx;
    height: 120rpx;
  }

  .status-text {
    font-size: 42rpx;
    font-weight: bold;
    color: #09bb07;
    margin-top: 30rpx;
  }

  .amount {
    font-size: 56rpx;
    font-weight: bold;
    color: #e54d42;
    margin-top: 20rpx;
  }
}

.order-info {
  width: 80%;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .label {
      color: #888;
      font-size: 30rpx;
    }

    .value {
      color: #333;
      font-size: 30rpx;
    }
  }
}

.action-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-top: 80rpx;
  margin-bottom: 10px;

  .btn {
    width: 45%;
    height: 90rpx;
    border-radius: 45rpx;
    font-size: 34rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &.secondary {
      background: #fff;
      color: #333;
      border: 1rpx solid #ddd;
    }

    &.primary {
      background: linear-gradient(to right, #ff6a4c, #ff396c);
      color: #fff;
      border: none;
    }
  }
}

.tips {
  margin-top: 60rpx;
  text-align: center;
  font-size: 26rpx;
  color: #999;
  line-height: 2;
}
</style>

<template>
  <wd-popup
    v-model="visible"
    position="center"
    custom-style="width: 600rpx; border-radius: 20rpx; padding: 0;"
    @close="handleClose"
  >
    <view class="parse-popup">
      <view class="popup-header">
        <text class="header-title">收货信息智能解析</text>
        <wd-icon name="close" size="32rpx" @click="handleClose"></wd-icon>
      </view>

      <view class="popup-content">
        <!-- 输入区域 -->
        <view class="input-section">
          <view class="section-header">
            <text class="section-title">粘贴地址信息</text>
            <view class="clipboard-btn" @click="manualGetClipboard">
              <text class="clipboard-text">📋 获取剪贴板</text>
            </view>
          </view>
          <view class="textarea-wrapper">
            <textarea
              v-model="parseInputText"
              placeholder="请粘贴收货信息，支持多种格式"
              :maxlength="500"
              class="custom-textarea"
              :show-count="false"
            />
            <view class="char-count">{{ parseInputText.length }}/500</view>
          </view>
          <view class="format-tips">
            <text class="tip-title">支持格式：</text>
            <text class="tip-item">
              • 结构化：收货人: 张三, 手机号码: 138xxxxxxxx, 所在地区: 北京市朝阳区, 详细地址:
              xxx街道
            </text>
            <text class="tip-item">
              • 自由格式：张三 138xxxxxxxx 北京市朝阳区xxx街道xxx小区xxx号楼xxx室
            </text>
          </view>
        </view>

        <!-- 解析按钮 -->
        <view class="action-buttons">
          <wd-button
            type="primary"
            size="large"
            :loading="parsing"
            @click="parseAddressInfo"
            :disabled="!parseInputText.trim()"
            block
          >
            {{ parsing ? '解析中...' : '智能解析' }}
          </wd-button>
        </view>

        <!-- 解析结果 -->
        <view v-if="parsedResult" class="result-section">
          <text class="section-title">解析结果</text>
          <view class="result-item">
            <text class="result-label">收件人：</text>
            <text class="result-value">{{ parsedResult.name || '未识别' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">手机号：</text>
            <text class="result-value">{{ parsedResult.phone || '未识别' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">所在地区：</text>
            <text class="result-value">{{ parsedResult.region || '未识别' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">详细地址：</text>
            <text class="result-value">{{ parsedResult.detail || '未识别' }}</text>
          </view>

          <view class="confirm-buttons">
            <wd-button
              type="default"
              size="large"
              @click="handleClose"
              custom-style="flex: 1; margin-right: 20rpx; border: 1rpx solid #dcdfe6; color: #606266;"
            >
              取消
            </wd-button>
            <wd-button
              type="primary"
              size="large"
              @click="confirmParsedResult"
              custom-style="flex: 1;"
            >
              确定
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 解析结果类型
interface ParsedResult {
  name: string
  phone: string
  region: string
  detail: string
}

// Props
interface Props {
  modelValue: boolean
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', result: ParsedResult): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const parseInputText = ref('')
const parsing = ref(false)
const parsedResult = ref<ParsedResult | null>(null)

// 重置数据
const resetData = () => {
  parseInputText.value = ''
  parsedResult.value = null
  parsing.value = false
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      // 弹窗打开时，检查剪贴板内容
      checkClipboardData()
    } else {
      // 弹窗关闭时，重置数据
      resetData()
    }
  },
  { immediate: true },
)

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 关闭时清空数据
  resetData()
}

// 检查剪贴板内容并询问用户
const checkClipboardData = async () => {
  try {
    const res = await uni.getClipboardData()
    if (res.data && res.data.trim()) {
      const clipboardContent = res.data.trim()

      // 简单判断是否可能是地址信息（包含中文字符和数字）
      const hasChineseAndNumber =
        /[\u4e00-\u9fa5]/.test(clipboardContent) && /\d/.test(clipboardContent)

      if (hasChineseAndNumber && clipboardContent.length > 10) {
        // 显示确认对话框
        uni.showModal({
          title: '检测到剪贴板内容',
          content: `检测到剪贴板中有可能的地址信息，是否使用？\n\n${clipboardContent.substring(0, 50)}${clipboardContent.length > 50 ? '...' : ''}`,
          confirmText: '使用',
          cancelText: '取消',
          success: (modalRes) => {
            if (modalRes.confirm) {
              parseInputText.value = clipboardContent
            }
            // 无论确认还是取消，都保持弹窗打开状态
          },
        })
      }
    }
  } catch (error) {
    console.log('获取剪贴板失败:', error)
  }
}

// 手动获取剪贴板内容
const manualGetClipboard = async () => {
  try {
    const res = await uni.getClipboardData()
    if (res.data && res.data.trim()) {
      parseInputText.value = res.data.trim()
      uni.showToast({
        title: '已获取剪贴板内容',
        icon: 'success',
      })
    } else {
      uni.showToast({
        title: '剪贴板为空',
        icon: 'none',
      })
    }
  } catch (error) {
    console.log('获取剪贴板失败:', error)
    uni.showToast({
      title: '获取剪贴板失败',
      icon: 'none',
    })
  }
}

// 智能解析地址信息
const parseAddressInfo = async () => {
  if (!parseInputText.value.trim()) return

  parsing.value = true

  try {
    // 模拟解析过程
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const text = parseInputText.value.trim()
    const result: ParsedResult = {
      name: '',
      phone: '',
      region: '',
      detail: '',
    }

    // 优化后的解析逻辑
    parseStructuredAddress(text, result)

    parsedResult.value = result
  } catch (error) {
    uni.showToast({
      title: '识别失败，请重试',
      icon: 'none',
    })
  } finally {
    parsing.value = false
  }
}

// 解析结构化地址信息
const parseStructuredAddress = (text: string, result: ParsedResult) => {
  // 1. 先尝试解析结构化格式（如：收货人: xxx, 手机号码: xxx, 所在地区: xxx, 详细地址: xxx）
  const structuredPatterns = {
    name: /(?:收货人|姓名|联系人)[:：]\s*([^,，\s]+)/,
    phone: /(?:手机号码?|电话|联系电话)[:：]\s*(\d{11})/,
    region: /(?:所在地区|地区|省市区)[:：]\s*([^,，]+?)(?=,|，|$)/,
    detail: /(?:详细地址|地址|详址)[:：]\s*([^,，]+?)(?=,|，|$)/,
  }

  // 尝试匹配结构化格式
  let hasStructuredMatch = false

  for (const [key, pattern] of Object.entries(structuredPatterns)) {
    const match = text.match(pattern)
    if (match) {
      result[key as keyof ParsedResult] = match[1].trim()
      hasStructuredMatch = true
    }
  }

  // 2. 如果没有结构化匹配，使用智能解析
  if (!hasStructuredMatch) {
    parseUnstructuredAddress(text, result)
  } else {
    // 3. 对结构化解析的结果进行优化
    optimizeStructuredResult(result)
  }
}

// 解析非结构化地址信息
const parseUnstructuredAddress = (text: string, result: ParsedResult) => {
  // 手机号正则匹配（更精确）
  const phoneRegex = /(?:^|[^\d])1[3-9]\d{9}(?=[^\d]|$)/
  const phoneMatch = text.match(phoneRegex)
  if (phoneMatch) {
    result.phone = phoneMatch[0].replace(/[^\d]/g, '')
  }

  // 姓名识别（优化逻辑）
  let nameText = text
  if (result.phone) {
    nameText = nameText.replace(result.phone, '')
  }

  // 匹配中文姓名（2-4个字符，排除地名）
  const nameRegex = /^[\u4e00-\u9fa5a-zA-Z]{1,8}(?=\s|$|1[3-9])/
  const nameMatch = nameText.trim().match(nameRegex)
  if (nameMatch && !isLocationName(nameMatch[0])) {
    result.name = nameMatch[0]
  }

  // 地址解析
  let addressText = text
  if (result.name) {
    addressText = addressText.replace(new RegExp(result.name, 'g'), '')
  }
  if (result.phone) {
    addressText = addressText.replace(new RegExp(result.phone, 'g'), '')
  }

  // 省市区识别（更精确的正则）
  const regionRegex =
    /([\u4e00-\u9fa5]{2,}(?:省|市|自治区|特别行政区))([\u4e00-\u9fa5]{2,}(?:市|州|盟|地区))?(?:([\u4e00-\u9fa5]{2,}(?:区|县|市|旗)))?/
  const regionMatch = addressText.match(regionRegex)

  if (regionMatch) {
    const [fullMatch, province, city, district] = regionMatch
    result.region = `${province}${city || ''}${district || ''}`
    addressText = addressText.replace(fullMatch, '')
  }

  // 详细地址（清理后的剩余部分）
  result.detail = addressText
    .replace(/\s+/g, '')
    .replace(/^[,，\-\s]+|[,，\-\s]+$/g, '')
    .trim()
}

// 优化结构化解析结果
const optimizeStructuredResult = (result: ParsedResult) => {
  // 清理地区信息中的多余字符
  if (result.region) {
    result.region = result.region.replace(/[,，\s]+$/, '').trim()
  }

  // 清理详细地址中的多余字符
  if (result.detail) {
    result.detail = result.detail.replace(/[,，\s]+$/, '').trim()
  }

  // 清理姓名中的多余字符
  if (result.name) {
    result.name = result.name.replace(/[,，\s]+$/, '').trim()
  }
}

// 判断是否为地名（简单的地名过滤）
const isLocationName = (name: string): boolean => {
  const locationKeywords = [
    '省',
    '市',
    '区',
    '县',
    '街道',
    '路',
    '街',
    '巷',
    '号',
    '栋',
    '楼',
    '室',
    '层',
  ]
  return locationKeywords.some((keyword) => name.includes(keyword))
}

// 确认使用解析结果
const confirmParsedResult = () => {
  if (parsedResult.value) {
    emit('confirm', parsedResult.value)
    uni.showToast({
      title: '地址信息已填入',
      icon: 'success',
    })
    handleClose() // 这里会自动调用 resetData()
  }
}
</script>

<style lang="scss" scoped>
.parse-popup {
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 40rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .header-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .popup-content {
    padding: 30rpx;

    .section-title {
      display: block;
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 20rpx;
    }

    .input-section {
      margin-bottom: 30rpx;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .clipboard-btn {
          padding: 8rpx 16rpx;
          background-color: #f0f9ff;
          border: 1rpx solid #007aff;
          border-radius: 6rpx;
          cursor: pointer;
          transition: all 0.2s;

          &:active {
            background-color: #e6f3ff;
            transform: scale(0.98);
          }

          .clipboard-text {
            font-size: 24rpx;
            color: #007aff;
          }
        }
      }

      .textarea-wrapper {
        position: relative;
        border: 1rpx solid #e4e7ed;
        border-radius: 8rpx;
        background-color: #fff;
        transition: border-color 0.2s;

        &:focus-within {
          border-color: #007aff;
        }

        .custom-textarea {
          width: 100%;
          height: 200rpx;
          padding: 16rpx 80rpx 16rpx 16rpx; /* 右侧留出空间给字符计数 */
          border: none;
          outline: none;
          resize: none;
          font-size: 28rpx;
          line-height: 1.4;
          color: #333;
          background: transparent;
          overflow-y: auto;
          box-sizing: border-box;

          &::placeholder {
            color: #c0c4cc;
            font-size: 28rpx;
          }
        }

        .char-count {
          position: absolute;
          bottom: 8rpx;
          right: 12rpx;
          font-size: 24rpx;
          color: #909399;
          background-color: rgba(255, 255, 255, 0.95);
          padding: 4rpx 8rpx;
          border-radius: 6rpx;
          border: 1rpx solid rgba(224, 224, 224, 0.8);
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
          z-index: 10;
          pointer-events: none; /* 防止阻挡文本框的滚动 */
        }
      }

      .format-tips {
        margin-top: 16rpx;
        padding: 16rpx;
        background-color: #f8f9fa;
        border-radius: 8rpx;
        border-left: 3rpx solid #007aff;

        .tip-title {
          display: block;
          font-size: 24rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }

        .tip-item {
          display: block;
          font-size: 22rpx;
          color: #666;
          line-height: 1.4;
          margin-bottom: 6rpx;
          word-break: break-all;
          overflow-wrap: break-word;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .action-buttons {
      margin-bottom: 30rpx;
      display: flex;
      justify-content: center;
    }

    .result-section {
      .result-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16rpx;
        padding: 12rpx 16rpx;
        background-color: #f8f9fa;
        border-radius: 6rpx;

        .result-label {
          font-size: 28rpx;
          color: #666;
          width: 120rpx;
          flex-shrink: 0;
          font-weight: 500;
        }

        .result-value {
          font-size: 28rpx;
          color: #333;
          flex: 1;
          word-break: break-all;
          overflow-wrap: break-word;
          line-height: 1.3;
        }
      }

      .confirm-buttons {
        display: flex;
        align-items: center;
        margin-top: 30rpx;
        gap: 0;

        :deep(.wd-button) {
          height: 88rpx;
          border-radius: 8rpx;
          font-size: 32rpx;
          font-weight: 500;
        }
      }
    }
  }
}
</style>

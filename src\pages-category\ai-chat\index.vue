<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full grid grid-cols-1 grid-rows-[1fr]">
    <template v-if="!stratSpeech">
      <z-paging
        ref="paging"
        empty-view-text="嘿，我是你的语音助手！初次见面很开心。我可以答你的各种问题，给你提供各种帮助，可以陪你聊天，你想问点什么呢？"
        v-model="dataList"
        use-chat-record-mode
        chat-adjust-position-offset="0px"
        inside-more
        :paging-style="{
          'background-color': '#ffffff',
          'padding-bottom': `${bottom || 15}px`,
          'box-sizing': 'border-box',
        }"
        auto-to-bottom-when-chat
        bottom-bg-color="#ffffff"
        empty-view-img="https://file.shanqianqi.com/image/2025/06/24/30659dcedbe44d7889903529faec0a26.png"
        :empty-view-img-style="{
          width: '200rpx',
          height: '200rpx',
          'margin-bottom': '24rpx',
        }"
        :empty-view-title-style="{
          'padding-left': '40rpx',
          'padding-right': '40rpx',
          'padding-top': '30rpx',
          'line-height': '40rpx',
          'padding-bottom': '40rpx',
          'margin-left': '24rpx',
          'margin-right': '24rpx',
          background: '#F7F7F7',
          'border-radius': '20rpx',
          color: '#333333',
        }"
        :default-page-size="10"
        @query="queryList"
        :fixed="false"
      >
        <template #top>
          <div>
            <wd-navbar
              fixed
              placeholder
              safeAreaInsetTop
              :bordered="false"
              left-arrow
              @click-left="handleClickLeft"
            >
              <template #title>智能语音</template>
            </wd-navbar>
          </div>
        </template>
        <div class="px26rpx box-border flex flex-col gap-y-34rpx">
          <div v-for="(item, index) in dataList" :key="index" class="w-full">
            <div
              v-if="item?.role === 'assistant'"
              style="transform: scaleY(-1)"
              class="flex justify-start"
            >
              <div v-if="item?.content === 'loading'" class="bg-#F7F7F7 rd-20rpx">
                <wd-loading />
              </div>
              <div v-else class="bg-#F7F7F7 rd-20rpx text-#333333 break-all max-w-80%">
                <zero-markdown-view :markdown="convertLatexToDollarSyntax(item?.content)" />
              </div>
            </div>

            <!-- 用户提问 -->
            <div
              v-else-if="item?.role === 'user'"
              style="transform: scaleY(-1)"
              class="flex justify-end"
            >
              <div class="bg-#0099FF rd-20rpx text-white break-all max-w-80%">
                <zero-markdown-view :markdown="convertLatexToDollarSyntax(item?.content)" />
              </div>
            </div>
          </div>
          <div
            v-if="dataList.length"
            class="grid grid-cols-1 place-items-center gap-y-24rpx pt40rpx"
            style="transform: scaleY(-1)"
          >
            <wd-img
              width="200rpx"
              height="200rpx"
              src="https://file.shanqianqi.com/image/2025/06/24/30659dcedbe44d7889903529faec0a26.png"
              mode="aspectFill"
            ></wd-img>
            <div class="bg-#F7F7F7 px40rpx py30rpx lh-40rpx rd-20rpx">
              嘿，我是你的语音助手！初次见面很开心。我可以回答你的各种问题，给你提供各种帮助，可以陪你
              聊天，你想问点什么呢？
            </div>
          </div>
        </div>
        <template #bottom>
          <div class="w-full px26rpx box-border pt26rpx">
            <div
              class="w-full h-90rpx border-rd-30rpx flex items-center gap-x-20rpx bg-[#ffffff] shadow-[0rpx_8rpx_20rpx_#00000033,0rpx_0rpx_0rpx_8rpx_#def2ff] px-40rpx box-border"
            >
              <wd-img
                @click="stratSpeech = !stratSpeech"
                src="
              https://file.shanqianqi.com/image/2025/06/28/fce2cbf4c2e944e88c200a21f992e6f6.png
              "
                width="60rpx"
                height="60rpx"
                mode="aspectFill"
              ></wd-img>
              <div v-if="!voiceState?.isRecording" class="flex-1 h-full">
                <wd-input
                  v-if="!showVoice"
                  custom-class="!bg-transparent !w-full !h-full custom-input"
                  type="text"
                  @confirm="handleSend"
                  no-border
                  :readonly="isAnswering"
                  confirm-type="send"
                  v-model="sendInputVal"
                  placeholder="请输入您要咨询的内容"
                />
                <div
                  v-else
                  @touchstart.stop.prevent="handleTouchStart"
                  @touchmove.stop.prevent="handleTouchMove"
                  @touchend.stop.prevent="handleTouchEnd"
                  class="w-full h-full flex items-center justify-center text-#5B5EE8 text-30rpx"
                >
                  按住说话
                </div>
              </div>
              <div
                @touchstart.stop.prevent="handleTouchStart"
                @touchmove.stop.prevent="handleTouchMove"
                @touchend.stop.prevent="handleTouchEnd"
                v-else
                class="flex-1"
              >
                <LottieAnimation
                  :custom-style="{ width: '100%', height: '180rpx' }"
                  :animation-data="animationData"
                />
              </div>

              <wd-img
                @click="showVoice = !showVoice"
                :src="
                  !showVoice
                    ? 'https://file.shanqianqi.com/image/2025/06/25/254cb8e73fe8459080585453391bc4ac.png'
                    : 'https://file.shanqianqi.com/image/2025/06/25/5f10636c56374a35aa2c40144bcfc4c2.png'
                "
                width="60rpx"
                height="60rpx"
                mode="aspectFill"
              ></wd-img>
            </div>
          </div>
        </template>
      </z-paging>

      <!-- 历史记录button -->
      <l-fab axis="xy" v-model:offset="cartOffset" magnetic="x">
        <div
          @click="showHistory = !showHistory"
          class="rd-1/2 flex items-center justify-center z-1000 transition-all-300"
        >
          <wd-img
            src="https://file.shanqianqi.com/image/2025/06/27/8dd684edf87b454fb89e61488d42ac19.png"
            width="100rpx"
            height="100rpx"
            mode="aspectFill"
          ></wd-img>
        </div>
      </l-fab>
      <!-- 历史记录popup -->
      <wd-popup
        v-model="showHistory"
        position="right"
        @before-enter="handleBeforeEnter"
        :zIndex="10000"
        lockScroll
        :custom-style="`width:50%;padding-top:${top + 44}px`"
        @close="showHistory = !showHistory"
      >
        <z-paging
          ref="historyPaging"
          v-model="historyList"
          inside-more
          :show-loading-more-no-more-view="false"
          empty-view-text="没有记录哦~"
          :default-page-size="10"
          :paging-style="{
            paddingLeft: '46rpx',
            paddingRight: '46rpx',
            paddingBottom: '60rpx',
            boxSizing: 'border-box',
          }"
          @query="queryHistoryList"
          :fixed="false"
        >
          <template #top>
            <div class="flex items-center justify-center">
              <wd-img
                src="https://file.shanqianqi.com/image/2025/06/24/30659dcedbe44d7889903529faec0a26.png"
                width="120rpx"
                height="120rpx"
                mode="aspectFill"
              ></wd-img>
            </div>
          </template>
          <div class="grid grid-cols-1 gap-y-60rpx grid-auto-rows-min pt60rpx">
            <div
              v-for="item in historyList"
              :key="item?.aiChatId"
              @click="handleDetialClick(item?.aiChatId)"
              class="flex items-center flex-col gap-y-12rpx"
            >
              <div class="flex justify-center justify-between w-full">
                <span class="text-32rpx text-#333333 font-500">
                  {{ item?.aiChatQuestionJson?.[0]?.audio_txt }}
                </span>
                <div class="i-hugeicons-delete-02 w-36rpx h-36rpx text-#333333"></div>
              </div>
              <div
                v-if="item?.aiChatContentJson?.[0]?.content"
                class="text-28rpx text-#333333 line-clamp-2"
              >
                {{ item?.aiChatContentJson?.[0]?.content }}
              </div>
            </div>
          </div>
        </z-paging>
      </wd-popup>
    </template>

    <Speech @handle-stop="stratSpeech = !stratSpeech" v-else />
  </view>
</template>

<script lang="ts" setup>
import ZeroMarkdownView from '@/pages-category/uni_modules/zero-markdown-view/components/zero-markdown-view/zero-markdown-view.vue'
import LottieAnimation from '@/components/LottieAnimation/index.vue'
import animationData from './animations/AIVoice.json'
import { useContinuousVoiceInput } from '@/hooks/useContinuousVoiceInput'
import { fetchUserAIChatList, addAiChat, fetchUserAIChatDetail } from '@/service'
import { useUserStore } from '@/store'
import { useFabOffset } from '@/hooks/useFabOffset'
import { convertLatexToDollarSyntax } from '@/utils'
import Speech from './components/Speech/index.vue'

const { cartOffset } = useFabOffset()

const userStore = useUserStore()

const showVoice = ref(false)

const stratSpeech = ref(false)

// #ifdef MP-WEIXIN

const {
  voiceState,
  startContinuousVoice,
  stopVoice,
  initVoiceListeners: initVoiceContinuousListeners,
} = useContinuousVoiceInput()

const isCanceling = ref(false)
const startY = ref(0)

/*
语音识别
*/
// 手指按下时
const handleTouchStart = (e: TouchEvent) => {
  console.log('🚀 ~ handleTouchStart ~ e:', e)
  startY.value = e.touches[0].clientY
  isCanceling.value = false
  uni.vibrateShort()
  startContinuousVoice()
}

// 手指按下时
const handleTouchMove = (e: TouchEvent) => {
  console.log('🚀 ~ handleTouchMove ~ e:', e)
  const moveY = e.touches[0].clientY
  const distance = startY.value - moveY
  console.log('🚀 ~ handleTouchMove ~ distance:', distance)
  if (distance > 20) {
    // 上滑超过 80px 就标记为取消状态
    isCanceling.value = true
  } else {
    isCanceling.value = false
  }
}

// 手指松开时
const handleTouchEnd = () => {
  if (isCanceling.value) {
    console.log('取消发送录音')
    // 取消录音处理逻辑
  } else {
    console.log('发送语音')
    // 正常录音提交逻辑
    uni.vibrateShort()
    stopVoice()
  }
}

// initVoiceListeners(async (res) => {
//   console.log('🚀 ~ initVoiceListeners ~ res:', res)
//   handleSend({ value: res?.result })
// })

/*
语音对话
*/

initVoiceContinuousListeners((res) => {
  const resultText = res.result
  handleSend({ value: resultText })
})

// #endif

const {
  safeAreaInsets: { bottom, top },
} = uni.getSystemInfoSync()

const showHistory = ref(false)

// 聊天历史记录
const historyPaging = ref()
const historyList = ref<Api.AI.ChatHistoryItem[]>([])
const queryHistoryList = async (pageIndex: number, pageSize: number) => {
  try {
    const { data } = await fetchUserAIChatList({
      userId: userStore?.userInfo?.userId,
      pageIndex,
      pageSize,
    })
    console.log('🚀 ~ queryHistoryList ~ data:', data)
    historyPaging?.value?.complete(data?.records)
  } catch (err) {
    historyPaging?.value?.complete(false)
  }
}

const handleBeforeEnter = () => {
  historyPaging?.value?.reload()
}

const handleDetialClick = async (aiChatId: number | string) => {
  chatId.value = aiChatId
  const { data } = await fetchUserAIChatDetail({ aiChatId })
  // 倒序组合成扁平列表
  // 一问一答合并为扁平数组
  const chatList = data?.aiChatQuestionJson.flatMap((question, index) => {
    const answer = data?.aiChatContentJson[index]
    return [
      {
        role: answer.role,
        content: answer.content,
        audio_response: answer.audio_response,
        aiChatId,
      },
      {
        role: 'user',
        content: question.audio_txt,
        audio_response: null,
        aiChatId,
      },
    ]
  })

  paging.value?.complete(chatList)
  console.log('🚀 ~ chatList ~ chatList:', chatList)
}

// 聊天列表
const paging = ref()
const dataList = ref<Api.AI.ChatListItem[]>([])
const queryList = async () => {
  try {
    paging?.value?.complete([])
  } catch (err) {
    paging?.value?.complete(false)
  }
}

const sendInputVal = ref('')

const chatId = ref<string | number>('')

const handleSend = async ({ value }) => {
  await paging.value?.addChatRecordData({
    role: 'user',
    content: value,
  })

  handleAnswer(value)
}

async function* streamGenerator(text: string, interval = 10) {
  for (const char of text) {
    yield char
    await new Promise((resolve) => setTimeout(resolve, interval))
  }
}

const isAnswering = ref(false)

const handleAnswer = async (value?: string) => {
  isAnswering.value = true

  paging.value?.addChatRecordData({
    role: 'assistant',
    content: 'loading',
  })

  try {
    const { data } = await addAiChat({
      aiChatId: chatId.value,
      audio_txt: value,
      userId: userStore?.userInfo?.userId,
      session_id: userStore?.userInfo?.userId,
      model: 'deepseek-r1:7b',
    })

    sendInputVal.value = ''

    chatId.value = data?.aiChatId

    const totalAnswerStr = data?.content
    let currentAnswerStr = ''
    for await (const char of streamGenerator(totalAnswerStr)) {
      if (char === null) {
        currentAnswerStr = totalAnswerStr
        break
      }
      currentAnswerStr += char
      dataList.value[0].content = currentAnswerStr
    }
    dataList.value[0] = data
  } catch (error) {
  } finally {
    isAnswering.value = false
  }
}

// 返回
const handleClickLeft = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
:deep(.custom-input .wd-input__body) {
  height: 100% !important;
}
:deep(.custom-input .wd-input__value) {
  height: 100% !important;
}
:deep(.l-fab) {
  background: transparent;
}
</style>

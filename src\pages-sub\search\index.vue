<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '搜索',
  },
}
</route>

<template>
  <view
    class="flex flex-col size-full"
    :style="{
      paddingTop: menuButtonInfo.top + 'px',
    }"
  >
    <z-paging
      ref="paging"
      :default-page-size="10"
      v-model="dataList"
      class="flex-1 of-hidden"
      :show-default-loading-more-text="false"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <div class="flex flex-col gap-x-20rpx pl26rpx box-border gap-y-20px">
          <div class="flex items-center" :style="{ height: menuButtonInfo.height + 2 + 'px' }">
            <div @click="handleBack" class="i-carbon-chevron-left text-18px"></div>
            <Search
              ref="SearchRef"
              @change="handleSearchChange"
              :placeholder="`请输入想要搜索的${searchFrom === 'mall' ? '商品' : '店铺'}`"
              :handle-search="handleSearch"
              class="flex-1 of-hidden h-full"
              :style="{ marginRight: (menuButtonInfo.width + 15) * 2 + 'rpx' }"
              :from="searchFrom"
            />
          </div>
        </div>
      </template>
      <div
        v-if="recommendList.length === 0"
        class="px26rpx box-border mt20px grid grid-cols-1 grid-auto-rows-min gap-y-20px"
      >
        <!-- 历史搜索 -->
        <div
          v-if="searchFrom === 'mall' && searchStore.searchList.length"
          class="flex flex-col gap-y-10px"
        >
          <div class="flex items-center justify-between">
            <span class="text-#333333 font-bold">历史搜索</span>
            <div
              @click="isDeleting = !isDeleting"
              class="i-hugeicons-delete-02 text-22px text-#333333"
            ></div>
          </div>
          <div class="flex items-center">
            <div class="flex items-center gap-10px flex-wrap">
              <wd-tag
                custom-class="!flex !items-center"
                v-for="item in searchStore.searchList"
                @close="() => handleDelSearch(item)"
                @click="() => handleSearch(item)"
                :key="item"
                :closable="isDeleting"
                round
                type="success"
              >
                {{ item }}
              </wd-tag>
            </div>
          </div>
        </div>
        <div
          v-if="searchFrom === 'store' && searchStore.searchStoreList.length"
          class="flex flex-col gap-y-10px"
        >
          <div class="flex items-center justify-between">
            <span class="text-#333333 font-bold">历史搜索</span>
            <div
              @click="isDeleting = !isDeleting"
              class="i-hugeicons-delete-02 text-22px text-#333333"
            ></div>
          </div>
          <div class="flex items-center">
            <div class="flex items-center gap-10px flex-wrap">
              <wd-tag
                custom-class="!flex !items-center"
                v-for="item in searchStore.searchStoreList"
                @close="() => handleDelStoreSearch(item)"
                @click="() => handleSearch(item)"
                :key="item"
                :closable="isDeleting"
                round
                type="success"
              >
                {{ item }}
              </wd-tag>
            </div>
          </div>
        </div>
        <!-- 热门搜索 -->
        <div class="flex flex-col gap-y-10px">
          <div class="flex items-center justify-between">
            <span class="text-#333333 font-bold">热门搜索</span>
          </div>
          <div class="flex items-center">
            <div class="flex items-center gap-x-10px flex-wrap">
              <wd-tag
                @click="() => handleSearch(item?.hotLabelName)"
                v-for="item in dataList"
                :key="item?.hotLabelName"
                round
                type="primary"
              >
                {{ item?.hotLabelName }}
              </wd-tag>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="px26rpx box-border mt20px grid grid-cols-1 grid-auto-rows-min gap-y-20px">
          <div
            @click="() => handleSearch(item?.labelName)"
            v-for="item in recommendList"
            :key="item?.labelId"
            class="flex items-center justify-between"
          >
            <div class="flex items-center gap-10px flex-wrap">
              <div class="i-carbon-search"></div>
              <span>{{ item?.labelName }}</span>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import Search from '@/components/Search/index.vue'
import { getAllHotProductLabelsByType, getLikeProductLabelList } from '@/service'
import { useSearchStore } from '@/store/search'

const searchFrom = ref('')
onLoad((e) => {
  searchFrom.value = e?.from || ''
})

const searchStore = useSearchStore()

const menuButtonInfo = ref({
  width: 0,
  height: 32,
  top: 20,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const isDeleting = ref(false)

const paging = ref()
const dataList = ref<Api.Search.HotSearchItem[]>([])

const queryList = async () => {
  let type = 20
  if (searchFrom.value === 'store') {
    type = 20
  } else if (searchFrom.value === 'mall') {
    type = 10
  }
  try {
    const { data } = await getAllHotProductLabelsByType({
      appId: import.meta.env.VITE_APPID,
      type: type,
    })
    paging.value.complete(data)
  } catch (err) {
    paging.value.complete(false)
  }
}

const searchVal = ref('')
const debouncedSearch = ref('')
const recommendList = ref<Api.Search.LinkSearchItem[]>([])

let timer: ReturnType<typeof setTimeout> | null = null
const handleSearchChange = (val: string) => {
  searchVal.value = val
  if (timer) clearTimeout(timer)
  timer = setTimeout(() => {
    debouncedSearch.value = val
  }, 300)
}

const SearchRef = ref()

onShow(() => {
  searchVal.value = ''
  debouncedSearch.value = ''
  recommendList.value = []
  SearchRef.value?.handleSearchComplete()
})

watch(
  () => debouncedSearch.value,
  async (newVal) => {
    if (newVal !== null && newVal !== undefined) {
      const { data } = await getLikeProductLabelList({ labelName: newVal })
      recommendList.value = data
    }
  },
)
const handleSearch = (search: string) => {
  if (!search) return
  uni.navigateTo({ url: `/pages-sub/goods/list/index?search=${search}&from=${searchFrom.value}` })
}

const handleDelSearch = (item: string) => {
  searchStore.removeSearch(item)
}

const handleDelStoreSearch = (item: string) => {
  searchStore.removeStoreSearch(item)
}

const handleBack = () => uni.navigateBack()
</script>

<style lang="scss" scoped>
//
</style>

<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <view class="bg-#f2f2f2 pb-2 box-border flex flex-col transition-all-300 of-hidden relative">
    <!-- 功能栏 -->
    <div
      :style="{
        height: menuButtonInfo.height + 'px',
        top: dynamicTop + 'px',
        width: `calc(100% - ${dynamicWidthOffset + 10}px)`,
      }"
      class="flex items-center z300 transition-all-200 absolute left-0"
    >
      <!-- 位置 -->
      <div id="location" class="h-full pl26rpx box-border flex items-center transition-all-300">
        <div @click="hanldeLocationChoose" class="flex items-center gap-x-10rpx font-bold">
          <div class="i-carbon-location-filled text-12px font-bold transition-all-300"></div>
          <div class="flex-1 line-clamp-1 text-26rpx text-#333333 transition-all-300">
            {{ locationStore.locationInfo?.cityName ?? '请选择位置' }}
          </div>
          <div
            class="i-carbon-caret-down text-12px text-#333333 font-bold transition-all-300"
          ></div>
        </div>
      </div>

      <!-- 搜索 -->
      <div
        class="h-full flex flex-1 items-center px-26rpx box-border gap-x-16rpx transition-all-300"
      >
        <Search
          class="flex-1 of-hidden h-full"
          :handle-go-search="handleSearch"
          :keywords="['美食', '火锅', '自助餐', '甜品', '饮品', '烤肉']"
        />
      </div>
    </div>

    <div
      class="transition-all-300 fixed top-0 left-0 w-full z99"
      :style="[
        bgOpacity && {
          paddingTop: menuButtonInfo.top + 'px',

          height: menuButtonInfo.height + 10 + 'px',
        },
        bgOpacity && { backgroundColor: `${themeStore?.navColor}` },
      ]"
    ></div>

    <!-- :scroll-into-view="targetId" -->
    <scroll-view
      @scrolltolower="handleReachBottom"
      scroll-y
      enable-flex
      class="main-scroll flex-1"
      @scroll="handlePageScroll"
    >
      <div class="grid grid-cols-1 gap-y-20rpx">
        <!-- 背景图 -->

        <wd-swiper
          v-model:current="currentSwiper"
          @change="onSwiperChange"
          height="652rpx"
          autoplay
          :indicator="false"
          :interval="5000"
          :list="swiperList"
          value-key="imgUrl"
        >
          <!-- <swiper-item
            :item-id="item?.imgUrl"
            v-for="item in swiperList"
            :key="item?.imgUrl"
            class="!border-none"
          >
            <div
              :style="{
                backgroundImage: `url(${item.imgUrl})`,
                backgroundSize: 'cover',
                backgroundPositionY: 'top',
                backgroundRepeat: 'no-repeat',
              }"
              class="flex h-652rpx transition-all-300"
            />
          </swiper-item> -->
        </wd-swiper>

        <div class="px-20rpx pb-20rpx grid grid-cols-1 gap-y-20rpx grid-rows-min mt--122rpx z-10">
          <!-- 导航 -->
          <scroll-view scroll-x class="bg-#ffffff rd-8px w-full px-14px box-border">
            <div class="flex flex-nowrap gap-x-14px">
              <div
                class="grid grid-cols-5 gap-y-10px gap-x-20px py12px min-w-full box-border grid-rows-[auto_auto]"
                v-for="(group, inxex) in activityList"
                :key="inxex"
              >
                <template v-for="item in group" :key="item.linkUrl">
                  <div
                    v-if="item?.isVisible === 'Y'"
                    @click="() => hanldeGoPage(item.linkUrl)"
                    class="w-full aspect-square flex flex-col items-center gap-y-14rpx justify-center"
                  >
                    <wd-img
                      :src="item.imgUrl"
                      width="96rpx"
                      height="96rpx"
                      mode="widthFix"
                    ></wd-img>
                    <span class="text-24rpx text-#333333">{{ item.text }}</span>
                  </div>
                </template>
              </div>
            </div>
          </scroll-view>

          <!-- 热销 -->
          <!-- <div class="rd-8px w-full flex flex-col p-10px box-border gap-y-10px">
            <div class="flex w-full items-center justify-between">
              <div class="flex items-end flex-1 of-hidden gap-x-6px relative h-44rpx">
                <span class="text-32rpx text-#333333 font-bold z-1 flex items-end">
                  善美优选
                  <span class="text-20rpx text-#333333 font-normal">/shanmeiyouxuan</span>
                </span>
                <div
                  class="bg-gradient-to-r from-#FF7D26 to-#FFFFFF w-full h-14rpx absolute bottom-0 left-0 z-0 rd-30rpx"
                ></div>
              </div>
              <div class="flex-1"></div>
            </div>

             <div class="grid grid-cols-1 gap-y-10px">
              <div
                class="bg-#ffffff shadow-light rd-20rpx p20rpx box-border w-full flex flex-col gap-y-20rpx"
              >
                <div class="flex items-start justify-between">
                  <div class="flex items-center gap-x-20rpx">
                    <wd-img width="80rpx" height="80rpx" mode="aspectFit"></wd-img>
                    <div class="flex flex-col gap-y-5px">
                      <span class="text-24rpx text-#333333 font-600">时尚女装</span>
                      <span class="text-20rpx text-#FF7D26">水墨画意风 21万+人在看</span>
                    </div>
                  </div>
                  <span class="text-#666666 text-20rpx">已售600+</span>
                </div>
                <scroll-view :show-scrollbar="false" scroll-x enable-flex class="flex items-center">
                  <div
                    v-for="item in dataList"
                    :key="item.productId"
                    @click="() => goGoodsDetail(item.productId)"
                    class="flex flex-col gap-y-10rpx mr-20rpx"
                  >
                    <wd-img
                      width="200rpx"
                      height="200rpx"
                      :src="item?.productImage"
                      mode="aspectFit"
                      custom-class="!rd-20rpx"
                    ></wd-img>
                    <div class="flex items-baseline gap-x-12rpx text-#FF7D26">
                      <div class="flex items-baseline">
                        <span class="text-20rpx font-500">¥</span>
                        <span class="text-40rpx font-bold">
                          {{ item?.productPrice?.split('.')?.[0] }}
                        </span>
                        <span class="text-24rpx font-bold">
                          .{{ item?.productPrice?.split('.')?.[1] }}
                        </span>
                      </div>
                      <span class="text-20rpx text-#666666 line-through">
                        ¥{{ item?.linePrice }}
                      </span>
                    </div>
                  </div>
                </scroll-view>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-10px">
              <div
                v-for="item in dataList"
                :key="item.productId"
                @click="() => goGoodsDetail(item.productId)"
                class="flex flex-col bg-#ffffff shadow-[0rpx_4rpx_8rpx_0rpx_rgba(0,0,0,0.02)] rd-20rpx of-hidden box-border"
              >
                <wd-img
                  :src="item?.productImage"
                  width="100%"
                  height="460rpx"
                  mode="aspectFit"
                ></wd-img>
                <div class="w-full p-20rpx box-border grid grid-cols-1">
                  <div class="text-24rpx text-#333333 line-clamp-1 font-600">
                    {{ item?.productName }}
                  </div>
                  <div class="text-20rpx text-#FF7D26 font-500 mt4px mb12rpx">
                    超长续航蓝牙耳机热卖榜
                  </div>
                  <div class="flex items-baseline gap-x-10px text-#FF7D26">
                    <div class="flex items-baseline">
                      <span class="text-20rpx font-500">¥</span>
                      <span class="text-40rpx font-bold">
                        {{ item?.productPrice?.split('.')?.[0] }}
                      </span>
                      <span class="text-24rpx font-bold">
                        .{{ item?.productPrice?.split('.')?.[1] }}
                      </span>
                    </div>
                    <span class="text-20rpx text-#666666 flex items-baseline">
                      已售 {{ item?.productSales }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div> -->

          <!-- 广告 -->
          <div v-if="advertisementData?.style?.layout === -2" class="grid grid-cols-2 gap-x-5px">
            <div
              class="w-full border border-solid border-#ffffff rd-10px box-border bg-gradient-to-b from-#FFE7DE to-#ffffff flex flex-col justify-between gap-y-2px p8px"
            >
              <div class="flex items-center justify-between">
                <span class="text-28rpx text-#333333 font-500">VR体验馆</span>
                <div class="flex items-center gap-x-2px">
                  <span class="text-20rpx text-#999999">抢购中</span>
                  <div class="i-carbon-chevron-right text-10px text-#333333 font-bold"></div>
                </div>
              </div>
              <wd-img
                width="314rpx"
                height="262rpx"
                mode="aspectFill"
                :src="advertisementData?.data?.[0]?.imgUrl"
              ></wd-img>
            </div>
            <div class="grid grid-rows-[auto_auto] gap-y-5px">
              <div
                class="size-full border border-solid border-#ffffff rd-10px box-border bg-gradient-to-b from-#FEF5D1 to-#ffffff py7px px-10px box-border flex flex-col justify-between"
              >
                <div class="flex items-center justify-between">
                  <span class="text-28rpx text-#333333 font-500">游戏娱乐</span>
                  <div
                    class="flex items-center justify-center w-140rpx h30rpx bg-#FF7D26 rd-14px text-20rpx text-#ffffff"
                  >
                    优惠低至百元
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex flex-col items-center gap-y-5px">
                    <wd-img
                      width="110rpx"
                      height="72rpx"
                      :src="advertisementData?.data?.[1]?.imgUrl"
                      mode="aspectFill"
                    ></wd-img>
                    <span class="text-#FF7D26 text-20rpx font-500">PSP游戏体验</span>
                  </div>
                  <div class="flex flex-col items-center gap-y-5px">
                    <wd-img
                      width="110rpx"
                      height="72rpx"
                      :src="advertisementData?.data?.[2]?.imgUrl"
                      mode="aspectFill"
                    ></wd-img>
                    <span class="text-#FF7D26 text-20rpx font-500">线下打靶训练</span>
                  </div>
                </div>
              </div>
              <div
                class="size-full border border-solid border-#ffffff rd-10px box-border bg-gradient-to-b from-#DEF5E1 to-#ffffff py7px px-10px box-border flex flex-col justify-between"
              >
                <div class="flex items-center justify-between">
                  <span class="text-28rpx text-#333333 font-500">医疗牙科</span>
                  <div
                    class="flex items-center justify-center w-140rpx h30rpx bg-#FF7D26 rd-14px text-20rpx text-#ffffff"
                  >
                    好品质更用心
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex flex-col items-center gap-y-5px">
                    <wd-img
                      width="110rpx"
                      height="72rpx"
                      :src="advertisementData?.data?.[3]?.imgUrl"
                      mode="aspectFill"
                    ></wd-img>
                    <span class="text-#38E791 text-20rpx font-500">检查129元起</span>
                  </div>
                  <div class="flex flex-col items-center gap-y-5px">
                    <wd-img
                      width="110rpx"
                      height="72rpx"
                      :src="advertisementData?.data?.[4]?.imgUrl"
                      mode="aspectFill"
                    ></wd-img>
                    <span class="text-#38E791 text-20rpx font-500">牙齿清洁99元起</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 店铺 -->
          <div class="grid grid-rows-[1fr] gap-y-10px" id="myTarget">
            <StoreList ref="StoreRef" />
          </div>
        </div>
      </div>
    </scroll-view>
    <!-- 搜索 -->
  </view>
</template>

<script lang="ts" setup>
import Search from '@/components/FakeSearch/index.vue'
import { fetchIndexGoods } from '@/service'
import { useThemeStore } from '@/store/theme'
import { getPlatform, chunkCategoryList } from '@/utils'
import { useLocationStore, useLoginSettingStore } from '@/store'
import StoreList from '@/components/StoreList/index.vue'
import { handleLocationSuccess } from '@/utils'

defineOptions({
  name: 'Home',
})
const menuButtonInfo = ref({
  height: 32,
  top: 20,
  width: 0,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const themeStore = useThemeStore()
const locationStore = useLocationStore()
const loginSettingStore = useLoginSettingStore()

// 选择位置
const hanldeLocationChoose = () => {
  uni.chooseLocation({
    success: handleLocationSuccess(locationStore),
    fail: (err) => {
      console.log(err)
    },
  })
}

onUnmounted(() => {
  themeStore.resetNavColor()
})

const { data: homeData } = useRequest(
  () =>
    fetchIndexGoods({
      source: getPlatform(),
    }),
  { immediate: true },
)

// 导航
const activityList = computed(() =>
  chunkCategoryList(
    homeData.value?.page?.items?.find((item) => item?.name === '首页导航')?.data ?? [],
  ),
)

// 背景图
const swiperList = computed(
  () => homeData.value?.page?.items?.find((item) => item?.name === '首页图片轮播')?.data ?? [],
)
// 广告
const advertisementData = computed<Api.Home.PageItems>(() =>
  homeData.value?.page?.items?.find((item) => item?.name === '首页图片橱窗'),
)

watch(
  () => swiperList.value,
  (val) => {
    if (val && val.length > 0) {
      handleChangeBtnColor()
    }
  },
)

const currentSwiper = ref(0)

function onSwiperChange(current) {
  currentSwiper.value = current?.current

  handleChangeBtnColor()
}

const handleChangeBtnColor = () => {
  themeStore.setNavColor(swiperList.value[currentSwiper.value]?.background)
}

const bgOpacity = ref(0)

const dynamicTop = ref(menuButtonInfo.value.top)

const dynamicWidthOffset = ref(menuButtonInfo.value.width)

const maxScroll = 100
const handlePageScroll = (e) => {
  const scrollTop = e.detail.scrollTop

  if (scrollTop < 80) {
    bgOpacity.value = 0
  } else {
    const opacity = Math.min((scrollTop - 80) / (maxScroll - 80), 1)
    bgOpacity.value = opacity
  }
}

const StoreRef = ref()

const handleReachBottom = () => {
  if (!StoreRef.value) return
  StoreRef.value.pageReachBottom()
}

// 搜索
const handleSearch = () => {
  uni.navigateTo({ url: '/pages-sub/search/index?from=store' })
}

const hanldeGoPage = (url: string) => {
  if (!url) {
    uni.showToast({
      title: '即将上线,敬请期待',
      icon: 'none',
    })
    // uni.navigateTo({ url: '/pages-my/rating/index' })
    return
  }
  uni.navigateTo({ url: `/${url}` })
}
</script>

<style>
:deep(.wd-drop-menu__list) {
  background: unset !important;
}
:deep(.wd-sidebar-item) {
  font-size: 26rpx !important;
}
</style>

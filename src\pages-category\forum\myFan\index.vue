<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的粉丝',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#ffffff pl-25rpx pr-25rpx pt-30rpx h-100%">
    <z-paging
      ref="paging"
      empty-view-text="没有粉丝哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      :paging-style="{ background: '#F7F7FA', 'padding-bottom': `${bottom || 15}px` }"
      fixed
      :show-scrollbar="false"
    >
      <template #top>
        <wd-navbar
          left-arrow
          :safeAreaInsetTop="true"
          :placeholder="true"
          fixed
          @click-left="handleBack"
        >
          <template #title>
            <view class="">
              <span class="ml-20rpx">我的粉丝</span>
            </view>
          </template>
        </wd-navbar>
      </template>

      <div class="list pl-25rpx pr-25rpx mt-20rpx">
        <div
          class="li flex justify-between mb-20rpx items-center"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <div class="li-l flex">
            <img
              :src="item.avatarUrl || ''"
              class="w-90rpx h-90rpx rounded-50% bg-#FF7D26 mr-15rpx"
              mode="aspectFill"
            />
            <div class="flex flex-col justify-center">
              <span class="text-30rpx">{{ item.nickName }}</span>
              <!-- <span class="text-22rpx text-#999999">{{ item.signature || '暂无签名' }}</span> -->
            </div>
          </div>
          <div
            class="li-r h-60rpx rounded-90rpx w-130rpx text-center flex items-center justify-center"
            :class="item.isMutual ? 'bg-#F2F2F2' : 'bg-#ff7d2633'"
            @click="handleFollow(item)"
          >
            <span class="text-23rpx" :class="item.isMutual ? 'text-#999999' : 'text-#FF7D26'">
              {{ item.isMutual ? '互相关注' : '回关' }}
            </span>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { centerMyFollow, discourseFollower, discourseCancelFollower } from '@/service'
const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const paging = ref()
const defaultPage = reactive({
  action: false,
})

const dataList = ref<Api.Forum.MyFollow[]>([])

// 分页请求
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await centerMyFollow({ pageIndex, pageSize, type: 'follower' })
      paging?.value?.complete(data?.records)
      console.log('data', data)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}
//取消关注
const handleFollow = async (item) => {
  console.log('item', item)

  try {
    uni.showLoading({ title: '处理中...', mask: true })

    if (item.isMutual) {
      // 取消关注
      await discourseCancelFollower({
        userId: item.followerId,
      })
      defaultPage.action = false
      // uni.showToast({
      //   title: '已取消关注',
      //   icon: 'none',
      // })
    } else {
      console.log(111)
      // 添加关注
      await discourseFollower({
        userId: item.followerId,
      })
      defaultPage.action = true
      // uni.showToast({
      //   title: '关注成功',
      //   icon: 'none',
      // })
    }
    paging.value.reload()
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1, // 返回的页面数
  })
}
</script>

<style lang="scss" scoped>
.list {
  .li {
    padding: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .li-r {
      transition: all 0.3s;

      &:active {
        opacity: 0.7;
      }
    }
  }
}
</style>

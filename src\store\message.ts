import { defineStore } from 'pinia'
import { fetchChatList } from '@/service'
export const useMessageStore = defineStore(
  'message',
  () => {
    const messageData = ref([])
    const getMessageData = async () => {
      const res = await fetchChatList()

      messageData.value = res.data.list
    }
    const setMessageData = (val) => {
      messageData.value = val
    }
    const resetMessageData = () => {
      messageData.value = []
    }

    return {
      messageData,
      getMessageData,
      setMessageData,
      resetMessageData,
    }
  },
  {
    persist: true,
  },
)

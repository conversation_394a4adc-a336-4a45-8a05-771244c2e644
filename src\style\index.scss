// @import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 修改按主题色
  // --wot-color-theme: #37c2bc;

  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;
  background: #f2f2f2;
}

.sign-top {
  background: url('https://file.shanqianqi.com/image/2025/06/21/c296b5c493234efd8e4e8c5695f50c24.png')
    no-repeat;
  background-size: cover;
  width: 216rpx;
  height: 187rpx;
}
// .sin-in-top-bg {
//   background: url('@/static/images/signin-succ.png') no-repeat;
//   background-attachment: fixed;
//   background-position: top;
//   background-size: cover;
// }
.voicebg {
  width: 325rpx;
  height: 154rpx;
  background: url('https://file.shanqianqi.com/image/2025/06/21/305e0af8b2c34f85b51944005476421a.png')
    no-repeat;
  background-size: contain;
}

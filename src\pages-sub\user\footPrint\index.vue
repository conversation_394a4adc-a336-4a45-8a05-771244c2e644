<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的足迹',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden pb-2 box-border h-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="20"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <SimpleNavBar title="我的足迹" />
        <wd-tabs @change="handleTabChange" v-model="tab" auto-line-width>
          <block v-for="item in tabs" :key="item.key">
            <wd-tab :title="`${item.label}`" :name="item.label"></wd-tab>
          </block>
        </wd-tabs>
      </template>
      <div v-for="group in groupedList" :key="group.date" class="pt-20rpx">
        <span class="px20rpx">{{ group.date }}</span>
        <view
          class="grid gap-10px p20rpx box-border transition-all-300"
          :class="tabIndex === 0 ? 'grid-cols-2' : 'grid-cols-1'"
        >
          <view
            v-for="item in group?.items"
            :key="item?.detail?.productId"
            @click="
              () =>
                goGoodsDetail(
                  item?.detail?.productId,
                  item?.detail?.productStatus,
                  item?.userVisit?.storeId,
                )
            "
            class="flex flex-col bg-#ffffff shadow-[0rpx_4rpx_8rpx_0rpx_rgba(0,0,0,0.02)] rd-20rpx of-hidden box-border"
          >
            <template v-if="tabIndex === 0">
              <wd-img
                :src="item?.detail?.image?.[0]?.filePath"
                width="100%"
                height="460rpx"
                mode="aspectFit"
              ></wd-img>

              <div class="right p-20rpx box-border grid grid-cols-1">
                <div class="text-24rpx text-#333333 line-clamp-1 font-600">
                  {{ item?.detail?.productName }}
                </div>
                <!-- <div class="text-20rpx text-#FF7D26 font-500 mt4px mb12rpx">超长续航蓝牙耳机热卖榜</div> -->
                <div class="flex items-baseline gap-x-10px text-#FF7D26">
                  <div class="flex items-baseline">
                    <span class="text-20rpx font-500">¥</span>
                    <span class="text-40rpx font-bold">
                      {{ item?.detail?.productPrice?.split('.')?.[0] }}
                    </span>
                    <span class="text-24rpx font-bold">
                      .{{ item?.detail?.productPrice?.split('.')?.[1] }}
                    </span>
                  </div>
                  <span class="text-20rpx text-#666666">
                    已售{{ formatLargeNumber(item?.productSales) }}+
                  </span>
                </div>
              </div>
            </template>

            <template v-else-if="tabIndex === 1">
              <div
                @click.stop="
                  () => goSupplierIndex(item.detail?.shopSupplierId, item.detail?.storeId)
                "
                class="bg-#ffffff shadow-light rd-20rpx p20rpx box-border w-full flex flex-col gap-y-20rpx"
              >
                <div class="flex items-start justify-between">
                  <div class="flex items-center gap-x-20rpx">
                    <wd-img
                      v-if="item?.detail?.logoFilePath"
                      width="100rpx"
                      height="100rpx"
                      :src="item?.detail?.logoFilePath"
                      mode="aspectFit"
                    ></wd-img>
                    <div v-else class="i-carbon-store text-40rpx text-#FF7D26"></div>
                    <div class="flex flex-col gap-y-10rpx">
                      <span class="text-32rpx text-#333333 font-600 line-clamp-1">
                        {{ item?.detail?.name }}
                        {{ item?.detail?.storeName ? `(${item?.detail?.storeName})` : '' }}
                      </span>
                      <div class="flex items-center gap-x-10px">
                        <wd-rate :modelValue="getScore(item?.detail?.serverScore, 1)" readonly />
                        <span class="text-24rpx text-#666666">
                          {{ item?.detail?.serverScore ?? '' }} 分
                        </span>
                        <!-- <span class="text-24rpx text-#666666">
                          {{ store?.favCount ?? '' }} 人关注
                        </span> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </view>
        </view>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { fetchVisitHistory } from '@/service'
import { getScore, formatLargeNumber } from '@/utils'
import { useUserStore } from '@/store'
import dayjs from 'dayjs'

const userStore = useUserStore()
onLoad(() => {})

const tabs = ref([
  { label: '商品', key: 20 },
  { label: '商户(门店)', key: 10 },
])

const tab = ref('商品')

const tabIndex = computed(() => tabs.value.findIndex((item) => item.label === tab.value))

const paging = ref()
const dataList = ref<any>([])
const groupedList = computed<any>(() => {
  const groupedByDay = {}

  dataList.value.forEach((item) => {
    const dateKey = dayjs(item?.userVisit?.updateTime ?? item?.userVisit?.createTime).format(
      'MM月DD日',
    )
    if (!groupedByDay[dateKey]) {
      groupedByDay[dateKey] = []
    }
    groupedByDay[dateKey].push(item)
  })

  const List = Object.entries(groupedByDay).map(([date, items]) => ({
    date,
    items,
  }))

  List.sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf())
  return List
})

const queryList = async (pageNo: number, pageSize: number) => {
  uni.showLoading({ title: '加载中' })
  try {
    const { data } = await fetchVisitHistory({
      pageIndex: pageNo,
      pageSize: pageSize,
      visitType: tabs.value[tabIndex.value]?.key,
      userId: userStore.userInfo?.userId,
    })

    uni.hideLoading()
    paging.value.complete(data?.records)
  } catch (err) {
    uni.hideLoading()
    paging.value.complete(false)
  }
}

const goGoodsDetail = (id: number, productStatus: any, storeId: any) => {
  if (productStatus !== 10) {
    uni.showToast({
      duration: 1000,
      title: '该商品已下架',
    })
  }
  if (tabIndex.value === 1) {
    return
  }
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}&storeId=${storeId}` })
}

const handleTabChange = () => {
  paging.value.reload()
}

const goSupplierIndex = (id: number, storeId: any) => {
  uni.navigateTo({ url: `/pages-sub/home/<USER>/supplier/index?shopId=${id}&storeId=${storeId}` })
}
</script>

<style lang="scss" scoped>
//
</style>

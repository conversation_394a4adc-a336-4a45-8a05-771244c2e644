<template>
  <view class="kf_popup" :class="[{'zero-bottom':position=='bottom'}]" v-if="showPrivacy">
    <view class="kf_popup-container" :style="{'--color':color,'--bgcolor':bgcolor}">
      <view class="delicon" @click="handleRefuse">
        <image :src="imgURl1" mode="widthFix"></image>
      </view>
      <view class="title">
        {{ title }}
      </view>
      <view class="phone">
        <view class="text">
          手机号：
        </view>
        <!-- <button class="input btn " open-type="getPhoneNumber" @getphonenumber="getPhone">
          <view class="i_left">
            <text v-show="!phoneNumber" class="placeholder">绑定手机号</text>
            <text v-show="phoneNumber" class="">{{phoneNumber}}</text>
          </view>
          <image class="i_right" :src="imgURl3" mode=""></image>
        </button> -->


        <view class="input btn ">
          <!-- <view class="input "> -->
          <view class="i_left">
            <input class="placeholder" v-model="phoneNumber" placeholder="绑定手机号"/>
          </view>
          <!-- </view> -->
        </view>

      </view>
      <view class="footer">
        <view class="btn disagree-btn" @click="handleRefuse">{{ cancelText }}
        </view>
        <button id="agree-btn" class="btn agree-btn" @click="handleAgree">
          {{ confirmText }}
        </button>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: "kf-privacy",
  emits: ['agree', 'disagree'],
  props: {
    position: { //位置
      type: String,
      default: 'center'
    },
    color: { //按钮颜色
      type: String,
      default: 'rgba(52, 106, 255, 1)'
    },
    bgcolor: { //背景颜色
      type: String,
      default: '#ffffff'
    },
  },
  data() {
    return {
      imgURl1: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADOSURBVEiJzdZLDoMgEIDhf/BieImum17E9CLizsRL2Iu1dGGfUXSoZFI2LsD5BAcGAELofdcOI4Vb1w5jCL0HkBB6L9E9ELkcT4e6FALRA0S51U5i1by7Y5GZfSIAEqtGljr2zCwVS7YGlEAARDtwDzKDfsU078ygXEw7dhHSBsj5oCS0FSh3iVehVMDpmfcfN6Fl7CuEKjNVUBrTp7/TQnvb/yydSTKYpLfJhjU5gkwOVZMyYVL4TEp5SWQNc1Gu55IIwBTjubHhZVhcIO8Ydggnvp1njAAAAABJRU5ErkJggg==",
      imgURl2: "data:image/png;base64,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",
      imgURl3: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAWCAYAAAD0OH0aAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADGSURBVDiNtZHNEYIwEEbfUgEl0IId2EDucmAogRIMFTiWwHDg7FCAlGAJlmAH64GY4UdMOPidksx7s99spO16C5yBusiNJZDEwQhSOjko1II8Fc1iJAFou94KUiqaobwQrlv15HOIlWR6iZFmQoyULIUiN1bRxsEpSjVdxGrCdBJK5SQ/aVNYSm71zarSIgNCCqBoBgy/Kh2Bu38QHsXJHL4KW/B43AGvhBA8E2JgL8TCALIHBkgQLrHwKCg39/VB2MfV+k/ezoB9hDSNlLMAAAAASUVORK5CYII=",
      title: "温馨提示", //头部文字
      confirmText: "同意",
      cancelText: "拒绝",
      content: "",
      funcConfirm: null, //确认方法
      funcCancel: null, //取消方法
      funcComplete: null,
      showPrivacy: false, //弹框显示隐藏
      avatarUrl: "",
      nickname: '',
      phoneNumber: "",
      session_key: ''
    };
  },
  methods: {
    //登录
    getPhone(e) {
      console.log("切换昵称", e)
      // if (!e.detail.iv) { return }
      // if (!e.detail.encryptedData) { return }
      // // uni.showLoading({ title: ' ' })
      // 	const phoneData = {
      // 		iv: e.detail.iv,
      // 		encryptedData: e.detail.encryptedData,
      // 		sessionKey: this.session_key
      // 	}
      // 	this.$http.xxxxxxxxxxx(phoneData).then(phone => {
      // 		console.log("手机号", phone)
      // 		this.$auth.setItem("userinfo", phone.data.Userinfo)
      // 		this.$auth.setItem("token", phone.data.Userinfo.token)
      // 		this.phoneNumber = phone.data.phoneNumber
      // 		getApp().globalData.permission = true
      // 		// uni.hideLoading()
      // 	})
    },
    //更换昵称
    nicknameonBlur(e) {
      console.log("切换昵称", e)
      this.nickname = e.detail.value;
    },
    //更换头像
    onChooseAvatar(e) {
      const file = e.detail.avatarUrl; // 获取用户选择的头像文件
      this.avatarUrl = e.detail.avatarUrl;
      // uni.uploadFile({
      // 	url: this.$http.commonUpload(),
      // 	filePath: this.avatarUrl,
      // 	name: 'file',
      // 	success: (res) => {
      // 		// 上传成功，获取服务器返回的头像路径
      // 		const data = JSON.parse(res.data);
      // 		console.log(JSON.parse(res.data))
      // 		this.avatarUrl = data.data.fullurl
      // 	},
      // 	fail: (err) => {
      // 		// 上传失败，处理错误逻辑
      // 		console.error(err);
      // 	}
      // });
    },
    //显示
    open({title = "温馨提示", content = "", cancelText = "拒绝", confirmText = "同意", success, fail, complete}) {
      this.title = title
      this.content = content
      this.cancelText = cancelText
      this.confirmText = confirmText
      this.funcConfirm = success
      this.funcCancel = fail
      this.funcComplete = complete
      this.showPrivacy = true;
    },
    //隐藏
    close() {
      this.showPrivacy = false;
    },
    // 点击同意
    handleAgree() {
      const reg = /^1[3-9]\d{9}$/
      if (!reg.test(this.phoneNumber)) {
        return uni.showToast({
          title: '手机号格式错误',
          mask: false,
          icon: "error",
          duration: 1000,
        })
      }
      if (!this.phoneNumber) {
        return uni.showToast({
          title: "请先获取获取手机号",
          mask: false,
          icon: "error",
          duration: 1000,
        });
      }
      // this.close();
      this.funcConfirm && this.funcConfirm({
        phoneNumber: this.phoneNumber,
      })
      this.funcComplete && this.funcComplete()
    },
    // 点击取消
    handleRefuse() {
      this.close();
      this.funcCancel && this.funcCancel()
      this.funcComplete && this.funcComplete()
    },
  },
  created() {
  },
};
</script>

<style lang="scss">
.kf_popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
  animation: fadeIn 0.2s linear both;
}

.kf_popup-container {
  position: relative;
  box-sizing: border-box;
  border-radius: 24rpx;
  // min-height: 700rpx;
  background: var(--bgcolor);
  padding: 50rpx;
  font-size: 14px;
  animation: fadeInBig 0.2s 0.2s linear both;
  backdrop-filter: blur(10rpx); //毛玻璃属性
  .delicon {
    position: absolute;
    right: 42rpx;
    top: 30rpx;

    image {
      width: 26rpx;
      height: 26rpx;
    }
  }

  .title {
    font-size: 34rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 49.24rpx;
    color: rgba(56, 56, 56, 1);
    text-align: center;
  }

  .avatar::after {
    border: none;
  }

  .avatar {
    padding: 0;
    margin: 0;
    background-color: transparent;
    border-radius: 0;
    border: none !important;
    text-align: center;
    margin-top: 40rpx;
    line-height: 0;

    image {
      width: 136rpx;
      height: 136rpx;
      border-radius: 50%;
    }
  }

  .nickname {
    margin-top: 60rpx;
    height: 62rpx;
    line-height: 62rpx;
    padding: 0 6rpx 0 14rpx;
    box-sizing: border-box;
    display: flex;

    .text {
      font-size: 28rpx;
      font-weight: 400;
      letter-spacing: 0rpx;
      line-height: 40.54rpx;
      color: rgba(56, 56, 56, 1);
      text-align: left;
      vertical-align: top;
    }

    .input {
      flex: 1;
      margin-left: 30rpx;
      border-bottom: 1rpx solid rgba(237, 237, 237, 1.00);
      padding-bottom: 10rpx;

      input {
        padding: 0 10rpx;
      }
    }
  }

  .phone {
    margin-top: 39rpx;
    height: 62rpx;
    line-height: 62rpx;
    padding: 0 6rpx 0 14rpx;
    box-sizing: border-box;
    display: flex;

    .btn::after {
      box-shadow: none;
      border: 0
    }

    .btn {
      background: transparent;
      font-size: 28rpx;
    }

    .input {
      align-items: center;
      flex: 1;
      justify-content: space-between;
      margin: 0 0 0 30rpx;
      border-bottom: 1rpx solid rgba(237, 237, 237, 1.00);
      padding: 0 0 10rpx 0;
      display: flex;

      .i_left {
        margin-left: 10rpx;

        .placeholder {
          color: rgba(163, 167, 179, 1);
        }
      }

      .i_right {
        margin-right: 5rpx;
        height: 19.3rpx;
        width: 10.6rpx;
      }
    }
  }

  .content {
    text-align: center;
    color: rgba(112, 112, 112, 1);
    margin-top: 36rpx;
    font-size: 26rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 37.64rpx;
    margin-bottom: 52rpx;

    text {
      color: var(--color);
    }
  }

  .footer {
    margin-top: 63rpx;
    display: flex;
    justify-content: space-between;
    // 重置微信小程序的按钮样式
    button:after {
      border: none;
    }

    .btn {
      text-align: center;
      white-space: nowrap;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      border-radius: 100rpx;
    }

    .btn + .btn {
      margin-left: 16rpx;
    }

    .disagree-btn {
      background: rgba(240, 240, 240, 1);
      font-size: 32rpx;
      font-weight: 500;
      letter-spacing: 0rpx;
      color: rgba(112, 112, 112, 1);
      min-width: 230rpx;
      height: 84rpx;
      line-height: 84rpx;
    }

    .agree-btn {
      line-height: 84rpx;
      padding: 0;
      min-width: 280rpx;
      height: 84rpx;
      font-size: 32rpx;
      font-weight: 400;
      letter-spacing: 0rpx;
      color: rgba(255, 255, 255, 1);
      margin: 0;
      background: linear-gradient(90deg, rgba(52, 106, 255, 1) 0%, rgba(104, 130, 247, 1) 100%);
    }
  }
}

.zero-bottom {
  align-items: flex-end;

  .kf_popup-container {
    width: 100%;
    animation: fadeIn 0.2s linear both;
    animation: fadeInUp 0.2s 0.2s linear both;
    // padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
    border-radius: 24px 24px 0 0;
  }

  .footer {
    padding: 0 40rpx;

    .btn {
      min-width: 250rpx;
    }
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInBig {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>

{
  // 控制字体系列。
  // "editor.fontFamily": "Consolas, '微软雅黑', monospace,'宋体'",
  //   "editor.fontFamily": "'Fira Code', Monaco, 'Courier New', monospace",
  //   "editor.fontLigatures": "'ss01', 'ss02' off, 'ss03', 'ss04', 'ss05', 'ss07', 'cv29', 'cv28', 'cv13'",
  // "editor.fontLigatures": "'ss01', 'ss02', 'ss03', 'ss04', 'ss05', 'zero', 'cv01', 'cv02', 'cv05'",
  // "editor.fontWeight": "400", // Light
  "disableLigatures.mode": "Line",

  // 控制选取范围是否有圆角
  "editor.roundedSelection": true,
  "editor.fontSize": 13,
  // 建议小组件的字号
  "editor.suggestFontSize": 14,
  // 在“打开的编辑器”窗格中显示的编辑器数量。将其设置为 0 可隐藏窗格。
  "explorer.openEditors.visible": 1,
  // 是否已启用自动刷新
  "git.autorefresh": true,
  // 以像素为单位控制终端的字号，这是 editor.fontSize 的默认值。
  "terminal.integrated.fontSize": 14,
  // 控制终端游标是否闪烁。
  "terminal.integrated.cursorBlinking": true,
  // 一个制表符等于的空格数。该设置在 `editor.detectIndentation` 启用时根据文件内容进行重写。
  // Tab Size
  "editor.tabSize": 2,
  // By default, common template. Do not modify it!!!!!
  "editor.detectIndentation": false,
  "files.associations": {
    "*.string": "html",
    "*.vue": "vue",
    "*.wxss": "css",
    "*.wxml": "wxml",
    "*.wxs": "javascript",
    "*.cjson": "jsonc",
    "*.js": "javascript",
    "*.wpy": "vue",
    ".env*": "dotenv",
    "pages.json": "jsonc",
    "manifest.json": "jsonc"
  },
  // 为指定的语法定义配置文件或使用带有特定规则的配置文件。
  "emmet.syntaxProfiles": {
    "vue-html": "html",
    "vue": "html"
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true
  },
  // 添加 vue 支持

  "html.format.enable": false,
  "javascript.format.enable": false,
  "typescript.format.enable": false,
  "javascript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": false,
  // 开启eslint自动修复js/ts功能
  // #每次保存的时候自动格式化
  "editor.formatOnSave": true,
  "editor.formatOnType": true,
  // #每次保存的时候将代码按eslint格式进行修复
  "editor.codeActionsOnSave": {
    "source.fixAll.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "eslint.format.enable": true,
  "editor.maxTokenizationLineLength": 200000,
  "editor.suggestSelection": "first",
  "editor.quickSuggestions": {
    "strings": true
  },
  "explorer.confirmDelete": false,
  "color-highlight.enable": true,
  "workbench.preferredDarkColorTheme": "Default Light+",
  "[dart]": {
    "editor.formatOnSave": false,
    "editor.formatOnType": false,
    "editor.rulers": [80],
    "editor.selectionHighlight": false,
    "editor.suggest.snippetsPreventQuickSuggestions": false,
    "editor.suggestSelection": "first",
    "editor.tabCompletion": "onlySnippets",
    "editor.wordBasedSuggestions": "off"
  },
  "explorer.confirmDragAndDrop": false,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "security.workspace.trust.untrustedFiles": "open",
  "gitlens.hovers.currentLine.over": "line",
  "[scss]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "git.confirmSync": false,
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "workbench.colorTheme": "Default Light+",
  "workbench.colorCustomizations": {
    "[Default Light+]": {
      "editor.background": "#C7EDCC",
      "sideBar.background": "#C7EDCC"
    }
    // "editor.lineHighlightBackground": "#C7EDCC",
    // "editor.lineHighlightBorder": "#9fced11f"
  },
  "[markdown]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.unicodeHighlight.allowedLocales": {
    "cs": true
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "tabnine.experimentalAutoImports": true,
  "gutterpreview.paths": {
    "@": "${workspaceRoot}/src"
  },
  "vue.splitEditors.layout.left": ["script", "scriptSetup", "styles"],
  "gitlens.gitCommands.skipConfirmations": ["fetch:command", "switch:command"],
  "diffEditor.ignoreTrimWhitespace": false,
  "i18n-ally.extract.parsers.babel": {
    "i18n-ally.localesPaths": ["src/i18n"], // 语言包地址
    "i18n-ally.keystyle": "nested",
    "i18n-ally.sortKeys": true,
    "i18n-ally.namespace": false,
    // "i18n-ally.pathMatcher": "{locale}/{namespaces}.{ext}",
    "i18n-ally.enabledParsers": ["ts", "js", "json"],
    "i18n-ally.sourceLanguage": "en",
    "i18n-ally.displayLanguage": "zh-CN",
    "i18n-ally.enabledFrameworks": ["vue", "react"]
  },
  "i18n-ally.displayLanguage": "zh-CN",
  "remote.SSH.remotePlatform": {
    "**************": "linux"
  },
  "git.autofetch": true,
  "dart.flutterSdkPath": "F:\\flutter\\flutter",
  "dart.debugExternalPackageLibraries": true,
  "dart.debugSdkLibraries": false,
  "turboConsoleLog.quote": "'",
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.tokenColorCustomizations": {
    "[*Light*]": {
      "textMateRules": [
        {
          "scope": "ref.matchtext",
          "settings": {
            "foreground": "#000"
          }
        }
      ]
    },
    "[*Dark*]": {
      "textMateRules": [
        {
          "scope": "ref.matchtext",
          "settings": {
            "foreground": "#fff"
          }
        }
      ]
    },
    "textMateRules": []
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "gitlens.ai.experimental.model": "openai:gpt-4o",
  "redhat.telemetry.enabled": true,
  "vscodeGoogleTranslate.preferredLanguage": "Chinese (Simplified)",
  "dotenv.enableAutocloaking": false,
  "i18n-ally.ignoredLocales": [],
  "svg.preview.mode": "svg",
  "terminal.integrated.fontFamily": "monospace",
  "editor.showFoldingControls": "always",
  "editor.hover.hidingDelay": 100,
  "editor.padding.top": 2,
  "editor.scrollbar.horizontalScrollbarSize": 6,
  "editor.scrollbar.ignoreHorizontalScrollbarInContentHeight": true,
  "editor.scrollbar.verticalScrollbarSize": 25,
  "editor.smoothScrolling": true,
  "editor.cursorBlinking": "smooth",
  "editor.bracketPairColorization.enabled": true,
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,LICENSE,.gitattributes,.gitignore,.gitpod.yml,CNAME,.npmrc,.browserslistrc"
  }
}

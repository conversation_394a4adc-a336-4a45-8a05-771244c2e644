<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的善豆钱包',
  },
}
</route>

<template>
  <view class="flex flex-col h-full pb-2 box-border">
    <div
      class="bgTop"
      :style="{
        paddingTop: menuButtonInfo.top + 'px',
        backgroundSize: 'cover',
        backgroundPosition: 'top',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'scroll',
      }"
    >
      <div
        class="flex items-center of-hidden box-border"
        :style="{
          height: menuButtonInfo.height + 'px',
          position: 'relative',
        }"
      >
        <div @click="goback()" class="flex items-center justify-center px42rpx">
          <wd-icon name="thin-arrow-left" size="28rpx" color="white"></wd-icon>
        </div>
        <div class="wallet flex-1 flex justify-center items-center text-white">我的善豆钱包</div>
      </div>
    </div>

    <div
      :style="{
        backgroundSize: 'cover',
        backgroundPositionY: `-${menuButtonInfo.top + menuButtonInfo.height}px`,
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'scroll',
      }"
      class="bgTop box-border w-full h-300rpx relative"
    >
      <view class="body-head pl42rpx text-white pt-50rpx pb-70rpx box-border">
        <view class="flex-1 flex flex-col gap-y-20rpx">
          <view class="name">当前善豆(数量)</view>
          <view class="text-80rpx fw-bold">{{ state.points }}</view>
        </view>
        <view
          v-if="isOpen"
          class="flex-1 text-30rpx flex flex-col justify-end items-end gap20rpx text-white"
          @click="gotoPay"
        >
          <image class="wallet_img" src="/static/svg/shandouRecharge.svg" mode="aspectFit"></image>
          <!-- <view class="gray6">充值</view> -->
        </view>
      </view>
      <view
        class="absolute left-[32rpx] right-[32rpx] bottom-[-50rpx]"
        style="width: calc(100% - 64rpx)"
      >
        <view class="h100rpx bg-white">
          <wd-calendar type="daterange" v-model="timePickerValue" @confirm="handleConfirm">
            <view class="flex items-center bg-none lh-90rpx">
              <view class="flex-1 flex justify-center items-center gap26rpx">
                <text>{{ dayjs(timePickerValue[0]).format('YYYY-MM-DD') }}</text>

                <image
                  class="w24rpx h24rpx"
                  src="https://file.shanqianqi.com/image/2025/06/14/5adb0c28f4594a26a41ab763417390d5.png"
                  mode="aspectFit"
                ></image>
              </view>
              <view class="flex-1 flex justify-center items-center l-h-100rpx gap26rpx">
                <text>{{ dayjs(timePickerValue[1]).format('YYYY-MM-DD') }}</text>
                <image
                  class="w24rpx h24rpx"
                  src="https://file.shanqianqi.com/image/2025/06/14/5adb0c28f4594a26a41ab763417390d5.png"
                  mode="aspectFit"
                ></image>
              </view>
            </view>
          </wd-calendar>
        </view>
      </view>
    </div>

    <div class="pt90rpx border-box">
      <!-- tab标签 -->
      <wd-tabs v-model="flowTab" @click="switchTab" custom-class="!bg-none">
        <block v-for="item in state.tabs" :key="item?.value" custom-class="!bg-none">
          <wd-tab :title="`${item.name}`" :name="item.value"></wd-tab>
        </block>
      </wd-tabs>
    </div>
    <!-- 中间内容 -->
    <view class="body flex-1 flex flex-col px-2 overflow-y-auto">
      <z-paging
        ref="paging"
        v-model="state.tableData"
        :default-page-size="20"
        :fixed="false"
        class="flex-1"
        @query="queryList"
      >
        <view class="px24rpx box-border">
          <view
            class="flex flex-col py20rpx gap20rpx"
            v-for="(item, index) in groupedData"
            :key="index"
          >
            <view class="flex justify-between items-center">
              <text class="text-28rpx fw-500 text-#333333">{{ item.title }}</text>
              <text class="text-24rpx fw-400 text-#999999">数量</text>
            </view>
            <div class="flex flex-col gap26rpx">
              <view
                v-for="(ele, ix) in item?.records"
                :key="ix"
                class="flex justify-between gap26rpx text-24rpx"
              >
                <text class="text-24rpx fw-400 text-#999999">{{ ele.description }}</text>
                <view class="text-#ff5704" v-if="Number(ele.value) > 0">+{{ ele.value }}</view>
                <view class="text-red" v-else>{{ ele.value }}</view>
              </view>
            </div>
          </view>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { fetchUserGoodBeanIndex, fetchUserBalancePlanIndex } from '@/service'
import { onMounted, ref } from 'vue'
import { getPlatform } from '@/utils'
const isOpen = ref(false)
// 响应式状态
const dataList = ref<Api.User.UserGoodBean[]>([])
const balance = ref<number>(0.0)
const topBarHeight = ref<number>(0)
const topBarTop = ref<number>(0)
// 获取一周的开始时间和结束时间
const startOfDay = dayjs().startOf('week').format('YYYY-MM-DD')
const endOfDay = dayjs().endOf('week').format('YYYY-MM-DD')
const flowTab = ref<number>(0)
const state = reactive({
  tabs: [
    {
      name: '全部',
      value: 0,
    },
    {
      name: '收入',
      value: 1,
    },
    {
      name: '支出',
      value: 2,
    },
  ],
  points: 0,
  tableData: [],
})

// 设置 timePickerValue 的默认值
const timePickerValue = ref<any[]>([startOfDay, endOfDay])
const menuButtonInfo = ref({
  height: 32,
  top: 50,
  width: 0,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

// 获取顶部栏高度（示例逻辑，实际根据平台调整）
onMounted(() => {
  // #ifdef MP-WEIXIN || APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  topBarHeight.value = systemInfo.statusBarHeight
  topBarTop.value = systemInfo.statusBarHeight
  // #endif
  fetchSetting()
  // fetchWalletData()
})
const value = ref<number>(0)
const formatValue = ref<string>('')

// 定义分组后的数据
const groupedData = ref<{ title: string; records: any[] }[]>([])
// 添加日期格式化函数
const formatDate = (timestamp: number | string) => {
  if (!timestamp) return ''
  return dayjs(timestamp).format('YYYY-MM-DD')
}
// 按日期分组数据的函数
const groupDataByDate = (records: any[]) => {
  // 使用对象存储不同日期的记录，避免重复
  const dateMap: { [date: string]: any[] } = {}

  // 遍历原始记录
  records.forEach((record) => {
    // 提取日期部分
    const date = dayjs(record.createTime).format('YYYY-MM-DD')

    // 如果对象中没有该日期的数组，就创建一个新数组
    if (!dateMap[date]) {
      dateMap[date] = []
    }

    // 将记录添加到对应日期的数组中
    dateMap[date].push(record)
  })

  // 将对象转换为所需的数组格式
  groupedData.value = Object.entries(dateMap).map(([date, records]) => ({
    title: date,
    records: records,
  }))

  // 按日期降序排序（最新日期在前）
  groupedData.value.sort((a, b) => dayjs(b.title).diff(dayjs(a.title)))
}
const paging = ref()
const allRecords = ref<any[]>([]) // 新增：用于累加所有分页数据
const flowType = computed(() => {
  switch (flowTab.value) {
    case 0:
      return '' // 当flowTab.value为0时返回空字符串
    // 当flowTab.value为1时返回'10'收入
    case 1:
      return 10
    // 当flowTab.value为2时返回'20'支出
    case 2:
      return 20
    default:
      return '' // 默认返回空字符串，可根据需要调整
  }
})
const queryList = async (pageNo: number, pageSize: number) => {
  uni.showLoading({ title: '加载中' })
  try {
    const { data } = await fetchUserGoodBeanIndex({
      pageIndex: pageNo,
      flowType: flowType.value,
      pageSize: pageSize,
      startDate: dayjs(timePickerValue.value[0]).startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
      endDate: dayjs(timePickerValue.value[1]).endOf('day').format('YYYY-MM-DDTHH:mm:ss'),
      appId: import.meta.env.VITE_APPID,
    })
    const records = data.list.records
    state.points = data.points

    if (pageNo === 1) {
      allRecords.value = [...records] // 第一页重置
    } else {
      allRecords.value = [...allRecords.value, ...records] // 累加
    }

    groupDataByDate(allRecords.value) // 用累加后的数据分组

    uni.hideLoading()
    paging.value.complete(records)
  } catch (err) {
    uni.hideLoading()
    paging.value.complete(false)
  }
}
const switchTab = (index: number) => {
  // const currentId = tabs.value[index]?.id
  // tab.value = index // 保持与 wd-tabs 同步
  // flowTab.value = currentId // 使用真正的 id 发起请求
  paging?.value?.reload()
}
const handleConfirm = (e: any) => {
  console.log('e', e)
  // 这里可以添加其他处理逻辑
  paging?.value?.reload() // 如果需要重新加载数据
}
const fetchSetting = async () => {
  let { data } = await fetchUserBalancePlanIndex(
    { appId: import.meta.env.VITE_APPID },
    { paySource: getPlatform() },
  )
  isOpen.value = data?.settings?.isOpen
}
// 获取钱包明细数据

const fetchWalletData = async () => {
  const { data } = await fetchUserGoodBeanIndex({
    appId: import.meta.env.VITE_APPID,
    // pageIndex: 1,
    // pageSize: 10,
  })
  console.log('🚀 ~ fetchWalletData ~ data:', data)
  const records = data.list.records
  dataList.value = records
  balance.value = data.points
}

// 返回上一页
const goback = () => {
  uni.navigateBack()
}

// 跳转充值页面
const gotoPay = () => {
  uni.navigateTo({ url: '/pages-sub/user/recharge/index' })
}

// 通用跳转方法（保持原逻辑）
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: none !important;
}
:deep(.wd-tabs__nav) {
  background: none !important;
}
.wallet {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.bgTop {
  background: url('https://file.shanqianqi.com/image/2025/06/20/bac7e1bbd69644e79820e62012d74d9c.png')
    no-repeat;

  background-size: contain;
}
.font-color-ccc {
  color: #cccccc;
}

.icon-jiantou::before {
  font-size: 24rpx;
}

.font-24 {
  font-size: 24rpx;
}

.font-28 {
  font-size: 28rpx;
}

.font-32 {
  font-size: 32rpx;
}

.font-72 {
  font-size: 72rpx;
}

.width-150 {
  width: 150rpx;
  text-align: center;
}

.wallet-content {
  margin-top: -93rpx;
}
.index-head {
  width: 710rpx;
  margin: 0 auto;
  height: 160rpx;
  background: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 25rpx;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.08);
}

.bg-image {
  width: 660rpx;
  height: 340rpx;
  background-image: url('../../../static/card.png');
  background-size: 100% 100%;
  margin: 0 auto;
  margin-top: 50rpx;
  display: flex;
  flex-direction: column;
}

.card-top {
  width: 750rpx;
  height: 256rpx;
  padding-left: 47rpx;
  padding-right: 22rpx;
  box-sizing: border-box;
}

.card-bottom {
  /* width: 660rpx; */
  height: 160rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.index-body {
  width: 706rpx;
  /* height: 1036rpx; */
  background: #ffffff;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 15rpx;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
}

.body-head {
  display: flex;
  justify-content: space-between;
  //flex-direction: column;
}
.body-head .name {
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-500;
  font-weight: 500;
}
.body-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 126rpx;
  border-bottom: 1rpx #f2f2f2 solid;
}

.body-item-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  margin-bottom: 10rpx;
}

.body-item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top_bg {
  width: 750rpx;
  height: 368rpx;
  background: linear-gradient(180deg, #ff774d 0%, #ff422e 100%);
}

.wallet_img {
  width: 209rpx;
  height: 76.8rpx;
  margin: 0;
}

.none_line {
  width: 1rpx;
  height: 80rpx;
  background-color: #d9d9d9;
}

.body-item-top-right {
  color: #ff5649;
  margin-bottom: -30rpx;
}
.underline {
  display: inline-block;
  line-height: 2;
  border-bottom: 1px solid #fff;
}
.text-btn {
  width: 189rpx;
  height: 62rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.15);
  border-radius: 30rpx;
  color: #ff5649;
}
.more-btn {
  width: 142rpx;
  height: 52rpx;
  background: #ffffff;
  opacity: 0.98;
  border-radius: 26rpx;
  font-size: 24rpx;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

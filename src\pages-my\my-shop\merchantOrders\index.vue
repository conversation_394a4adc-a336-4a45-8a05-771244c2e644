<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '商户订单',
  },
}
</route>

<template>
  <view class="overflow-hidden pb-3 box-border h-full">
    <z-paging
      ref="paging"
      v-model="state.listData"
      :default-page-size="10"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <div class="flex flex-col w-full">
          <!-- <wd-tabs
            @click="handleTabChange"
            slidable="always"
            v-model="state.stateActive"
            auto-line-width
          >
            <block v-for="item in state.ordrerTabs" :key="item.key">
              <wd-tab :title="`${item.name}`" :name="item.key"></wd-tab>
            </block>
          </wd-tabs> -->

          <wd-tabs @click="handleSourceTabChange" v-model="state.sourceActive" auto-line-width>
            <block v-for="(ele, index) in state.ordrerTypeTabs" :key="index">
              <wd-tab :title="`${ele.name}`" :name="ele.name"></wd-tab>
            </block>
          </wd-tabs>
          <wd-radio-group
            custom-class="flex-row bg-#F2F2F2 flex-row w-full overflow-x-auto whitespace-nowrap p20rpx box-border"
            v-model="state.stateActive"
            shape="button"
            @change="handleTabChange"
          >
            <wd-radio v-for="item in state.ordrerTabs" :key="item.key" :value="item.key">
              {{ item.name }}
            </wd-radio>
          </wd-radio-group>
        </div>
      </template>
      <!--列表-->

      <view class="order-list">
        <view class="p30rpx mt30rpx bg-#fff" v-for="(item, index) in state.listData" :key="index">
          <view class="order-head flex flex-col justify-between items-start">
            <view>
              <wd-tag
                color="white"
                bg-color="#f6220c"
                style="font-size: 22rpx"
                custom-class=" rounded-4rpx mr10rpx"
              >
                {{ item.orderSourceText }}
              </wd-tag>
              <text style="font-family: PingFang SC" class="text-26rpx fw-500 text-#333333">
                订单号：{{ item.orderNo }}
              </text>
            </view>
            <view
              v-if="
                !(
                  (item.orderStatus == 30 && item.storeType == 30) ||
                  (item.orderStatus == 30 && item.storeType == 40)
                )
              "
              class="state w-full flex justify-start items-center pt20rpx"
            >
              <text class="text-28rpx text-red">{{ item.orderStatusText }}</text>
            </view>
          </view>
          <!--多个商品显示-->

          <view
            class="product-list py20rpx flex justify-between flex-wrap relative"
            v-if="item.product.length > 1"
            @click="jumpPage(item.orderId)"
          >
            <scroll-view :scroll-x="true">
              <view class="list pr100rpx flex justify-start items-center">
                <view class="cover mr10rpx" v-for="(img, num) in item.product" :key="num">
                  <image class="w160rpx h160rpx" :src="img.productImage" mode="aspectFit"></image>
                </view>
              </view>
            </scroll-view>
            <view class="total-count pl20rpx">
              <view class="left-shadow"></view>
              <view class="flex justify-center items-end text-22rpx pl20rpx box-border">
                <text>¥</text>
                <text class="text-40rpx">{{ item.payPrice }}</text>
              </view>
              <view class="text-#666 text-28rpx">共{{ item.product.length }}件</view>
            </view>
          </view>
          <!--一个商品显示-->
          <view
            class="one-product flex justify-start items-center py20rpx box-border"
            v-else
            @click="jumpPage(item.orderId)"
          >
            <view class="cover" v-for="(img, num) in item.product" :key="num">
              <image class="w160rpx h160rpx" :src="img.productImage" mode="aspectFit"></image>
            </view>
            <view class="flex-1 text-28rpx text-#666 px30rpx">
              {{ item.product[0]?.productName }}
            </view>
            <view class="flex items-end flex-col">
              <view class="left-shadow"></view>
              <view class="flex justify-center items-end text-24rpx pl20rpx box-border">
                <text>¥</text>
                <text class="text-40rpx">{{ item.payPrice }}</text>
              </view>
              <view v-if="item.orderSource !== 100" class="text-#666 text-28rpx">
                共{{ item?.totalNum }}件
              </view>
            </view>
          </view>
          <view class="flex justify-end gap20rpx">
            <!-- 取消订单 -->
            <block
              v-if="item.payStatus == 20 && item.deliveryStatus == 10 && item.orderStatus == 10"
            >
              <button
                size="mini"
                @click="openClose(item.orderNo)"
                class="rounded-28rpx m0 text-24rpx bg-#F7F7FA text-#999999"
              >
                取消订单
              </button>
            </block>
            <!-- 订单去发货 -->
            <block
              v-if="
                item.payStatus == 20 &&
                item.deliveryType == 10 &&
                item.orderStatus == 10 &&
                item.deliveryStatus == 10
              "
            >
              <button
                size="mini"
                class="m0 rounded-28rpx text-24rpx bg-#FFECDF text-#FF7D26"
                @click="jumpPage(item.orderId)"
              >
                去发货
              </button>
            </block>

            <!-- 订单修改单包裹物流单号 -->
            <block
              v-if="
                item.payStatus == 20 &&
                item.deliveryType == 10 &&
                item.orderStatus == 10 &&
                item.deliveryStatus != 10
              "
            >
              <button
                size="mini"
                class="m0 rounded-28rpx text-24rpx bg-#FFECDF text-#FF7D26"
                @click="jumpPage(item.orderId)"
              >
                修改物流
              </button>
            </block>

            <!-- 查看物流 -->
            <button
              class="m0 rounded-28rpx text-24rpx bg-#FFECDF text-#FF7D26"
              v-if="
                (item.orderStatus == 10 || item.orderStatus == 30) &&
                (item.deliveryType == 40 || item.deliveryType == 10) &&
                item.deliveryStatus != 10
              "
              size="mini"
              @click="
                gotoPage(
                  `/pages-my/orederExpressDetail/orederExpressDetail?orderId=${item.orderId}&deliveryType=40`,
                )
              "
            >
              查看物流
            </button>
            <!-- <block v-if="item.payStatus == 20 && item.orderStatus == 21">
              <button
                class="border-btn m0 text-24rpx bg-white text-red"
                @click="goAudit(item.shopSupplierId)"
              >
                去审核
              </button>
            </block> -->
          </view>
        </view>
      </view>
      <!-- 取消订单弹窗 -->

      <wd-popup
        v-model="state.showCancel"
        position="center"
        safe-area-inset-bottom
        lockScroll
        custom-style=""
        @close="handleClose"
        custom-class="!rounded-10rpx !w-600rpx !h-auto"
      >
        <view class="w-full px50rpx pt68rpx box-border">
          <view class="text-32rpx fw-bold mb20rpx box-border">取消订单</view>
          <view class="flex items-center mb20rpx box-border text-24rpx">
            <view class="text-26rpx">订单号：</view>
            <view>{{ state.orderNo }}</view>
          </view>
          <view class="mb20rpx text-24rpx flex flex-col gap20rpx">
            <view class="w120rpx text-26rpx">备注：</view>
            <textarea
              v-model="state.cancelRemark"
              placeholder="请输入备注"
              style="border: 1rpx solid #eeeeee"
              class="textRemark p10rpx w-auto"
            />
          </view>
          <view class="flex justify-center items-center gap30rpx">
            <button class="m0 text-28rpx rounded-30rpx border-btn box-border" @click="handleClose">
              取消
            </button>
            <button class="m0 text-28rpx rounded-30rpx border-btn" @click="sendClose()">
              确定
            </button>
          </view>
        </view>
      </wd-popup>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import {
  fetchUserStoreOrderList,
  getOrderListBysupplierldList,
  cancelUserStoreOrder,
} from '@/service'
// 引入 onLoad 生命周期钩子
// 响应式状态
const state = reactive({
  //取消按钮
  showCancel: false,
  //订单号
  orderNo: '',
  //订单备注
  cancelRemark: '',
  shopSupplierId: '',
  // stateActive: 'all',
  // ordrerTabs: [
  //   {
  //     key: 'all',
  //     name: '全部订单',
  //   },
  //   {
  //     key: 'payment',
  //     name: '待付款',
  //   },
  //   {
  //     key: 'delivery',
  //     name: '待发货',
  //   },
  //   {
  //     key: 'received',
  //     name: '待收货',
  //   },
  //   {
  //     key: 'comment',
  //     name: '待评价',
  //   },
  //   // {
  //   //   id: 5,
  //   //   name: '退款/售后',
  //   // },
  // ],

  stateActive: 'all',
  ordrerTabs: [
    // {
    //   key: 'all',
    //   name: '全部',
    // },
    // {
    //   key: 'waitcheck',
    //   name: '待核销',
    // },
    // {
    //   key: 'havecheck',
    //   name: '待评价',
    // },

    {
      key: 'all',
      name: '全部订单',
    },
    {
      key: 'payment',
      name: '待付款',
    },
    {
      key: 'delivery',
      name: '待发货',
    },
    {
      key: 'received',
      name: '待收货/待使用',
    },
    {
      key: 'comment',
      name: '待评价',
    },
  ],
  sourceActive: '全部订单',
  ordrerTypeTabs: [
    {
      // 线上传空，线下传100
      key: 0,
      name: '全部订单',
    },
    {
      // 线上传空，线下传100
      key: 10,
      name: '商圈订单',
    },
    {
      key: 20,
      name: '商城订单',
    },
  ],
  listData: [],
})

//防抖处理
let signInTimeout: ReturnType<typeof setTimeout> | null = null
let tabTimer: ReturnType<typeof setTimeout> | null = null
// 页面加载时触发
onLoad((e) => {
  state.shopSupplierId = e.shopSupplierId
  if (typeof e.dataType !== 'undefined') {
    state.stateActive = e.dataType
    handleTabChangeByDataType()
  }
})

// 生命周期 - 初始化
onMounted(() => {})
const paging = ref()
onShow(() => {
  paging?.value?.reload()
})
const sourceTabsIndex = computed(() =>
  state.ordrerTypeTabs.findIndex((item) => item.name === state.sourceActive),
)
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    // const { data } = await fetchUserStoreOrderList({
    //   pageIndex: pageNo,
    //   type: state.stateActive,
    //   pageSize: pageSize,
    //   shopSupplierId: state.shopSupplierId,
    //   orderType: state.ordrerTypeTabs[sourceTabsIndex.value]?.key,
    //   // 来源线上传空，线下传100
    //   appId: import.meta.env.VITE_APPID,
    // })
    uni.showLoading({
      title: '加载中',
    })
    const { data } = await getOrderListBysupplierldList({
      pageIndex: pageNo,
      type: state.stateActive,
      pageSize: pageSize,
      shopSupplierId: state.shopSupplierId,
      orderType: state.ordrerTypeTabs[sourceTabsIndex.value]?.key,
      // 来源线上传空，线下传100
      appId: import.meta.env.VITE_APPID,
    })
    const records = data.records

    paging.value.complete(records)
    uni.hideLoading()
  } catch (err) {
    console.log('err', err)

    paging.value.complete(false)
    uni.hideLoading()
  }
}
// 跳转不同订单tab
const handleTabChangeByDataType = () => {
  paging.value?.reload()
}
// 标签切换处理
const handleTabChange = () => {
  if (tabTimer) clearTimeout(tabTimer)
  tabTimer = setTimeout(async () => {
    paging.value?.reload()
  }, 300)
}
// 订单来源tab
const handleSourceTabChange = () => {
  if (signInTimeout) clearTimeout(signInTimeout)
  signInTimeout = setTimeout(async () => {
    //线下
    const currentKey = state.ordrerTypeTabs[sourceTabsIndex.value]?.key
    if (currentKey === 0) {
      state.ordrerTabs = [
        {
          key: 'all',
          name: '全部订单',
        },
        {
          key: 'payment',
          name: '待付款',
        },
        {
          key: 'delivery',
          name: '待发货',
        },
        {
          key: 'received',
          name: '待收货/待使用',
        },
        {
          key: 'comment',
          name: '待评价',
        },
      ]
    } else if (currentKey === 10) {
      state.ordrerTabs = [
        {
          key: 'all',
          name: '全部',
        },
        {
          key: 'payment',
          name: '待付款',
        },
        {
          key: 'waitcheck',
          name: '待核销',
        },
        // {
        //   key: 'havecheck',
        //   name: '待评价',
        // },
      ]
    } else if (currentKey === 20) {
      state.ordrerTabs = [
        {
          key: 'all',
          name: '全部订单',
        },
        {
          key: 'payment',
          name: '待付款',
        },
        {
          key: 'delivery',
          name: '待发货',
        },
        {
          key: 'received',
          name: '待收货',
        },
        {
          key: 'comment',
          name: '待评价',
        },
      ]
    }
    state.stateActive = state.ordrerTabs[0].key
    paging.value?.reload()
  }, 300)
}
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
/*跳转页面*/
const jumpPage = (e) => {
  gotoPage('/pages-my/my-shop/merchantOrders/merchantOrdersDetail?orderId=' + e)
}
//去审核
const goAudit = (e) => {
  // gotoPage('/pages-my/my-shop/after_sale/index?shopSupplierId=' + e)
  // gotoPage('/pages-my/user/refund/refundDetail?source=supplier&shopSupplierId=' + e)
}
const handleClose = () => {
  state.showCancel = false
}
//取消订单
const openClose = (e) => {
  state.showCancel = true
  state.orderNo = e
}
//确认取消订单
const sendClose = () => {
  uni.showModal({
    title: '提示',
    content: '你确定要取消订单吗？',
    success: (res) => {
      if (!res.confirm) {
        return // 用户取消操作，退出函数
      }
      uni.showLoading({
        title: '正在处理',
      })
      // 发送 API 请求以撤回取消订单
      cancelUserStoreOrder({
        appId: import.meta.env.VITE_APPID,
        orderNo: state.orderNo,
        cancelRemark: state.cancelRemark,
      })
        .then((res) => {
          uni.hideLoading()
          uni.showToast({ title: res.msg || '操作成功', duration: 2000, icon: 'success' })
          paging.value?.reload() // 刷新订单列表
          state.showCancel = false
        })
        .catch((error) => {
          uni.hideLoading()
          state.showCancel = false
          uni.showToast({ title: error.data.msg || '操作失败', duration: 2000, icon: 'none' })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },
  })
}
</script>

<style lang="scss" scoped>
:deep(.flex-row) {
  &::-webkit-scrollbar {
    display: none; // 隐藏滚动条
  }

  // -ms-overflow-style: none; // IE 和 Edge
  // scrollbar-width: none; // Firefox
}
:deep(.wd-radio__label) {
  font-size: 22rpx !important;
  font-weight: 600;
  padding: 10rpx !important;
}
.total-count {
  background: rgba(255, 255, 255, 0.9);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  padding-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}
.border-btn {
  border: 2rpx solid #cccccc;
  border-radius: 30rpx;
}
.border-red {
  border: 2rpx solid red;
  border-radius: 30rpx;
}
.textRemark::placeholder {
  font-size: 24rpx;
}
</style>

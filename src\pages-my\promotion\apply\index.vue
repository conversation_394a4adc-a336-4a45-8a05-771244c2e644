<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full grid grid-cols-1 grid-cols-1 grid-rows-[auto_1fr_auto] gap-y-8px"
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
  >
    <div class="w-100% h-508rpx fixed top-0 left-0">
      <wd-img
        width="100%"
        height="508rpx"
        mode="aspectFit"
        src="https://file.shanqianqi.com/image/2025/06/12/b763e9696a3c447a9845b7a6bf1750aa.png"
      ></wd-img>
    </div>

    <SimpleNavBar :bordered="false" custom-class="!bg-transparent" title="申请提现" />
    <div class="grid grid-cols-1 pt64rpx box-border z-10 px-40rpx gap-y-10rpx grid-auto-rows-min">
      <div class="text-28rpx text-#ffffff">可提现金额（元）</div>
      <div class="flex items-end gap-x-10rpx text-#ffffff">
        <span class="text-52rpx font-bold">
          {{ applyInfo?.agentUser?.money ?? '' }}
          <span class="text-24rpx font-bold">
            ({{
              `${applyInfo?.words?.cashApply?.minMoney ?? ''}${applyInfo?.settlement?.minMoney ?? ''}元`
            }})
          </span>
        </span>
      </div>
      <div
        class="mt18rpx w-full border-rd-10rpx text-#333333 bg-[#ffffff] shadow-[0rpx_0rpx_8rpx_4rpx_#1b51ad12] p40rpx box-border flex flex-col gap-y-60rpx"
      >
        <div class="flex flex-col gap-y-60rpx">
          <span class="text-32rpx">提现金额</span>
          <div class="flex items-end pb12rpx box-border border-b border-b-solid border-b-#F2F2F2">
            <span class="text-48rpx mr16rpx">¥</span>
            <div class="flex-1">
              <wd-input
                v-model="inputValue"
                no-border
                @blur="handleBlur"
                placeholder="请输入金额"
                custom-input-class="!text-80rpx !h-100rpx"
                custom-class="!bg-transparent"
                type="digit"
              />
            </div>
            <wd-button
              type="text"
              @click="handleApplyAll"
              custom-class="!m-unset !p-unset !line-height-unset !text-#8445C7 !text-32rpx"
            >
              全部提现
            </wd-button>
          </div>
        </div>
        <div v-if="userFees" class="grid grid-cols-1 gap-y-20rpx">
          <span class="text-28rpx text-#666666">扣去{{ userFees?.fees }}%个人所得税</span>
          <span class="text-28rpx text-#333333">
            {{ userFees?.amountReceived }}
            <span class="text-#666666">（实际到账金额）</span>
          </span>
        </div>
        <div v-if="applyInfo?.agentUser?.bankName" class="flex flex-col gap-y-40rpx">
          <span class="text-32rpx text-#999999">提现方式</span>
          <div
            @click="goBindCard"
            class="flex items-center justify-between pb12rpx box-border gap-x-20rpx"
          >
            <div class="flex items-baseline gap-x-20rpx">
              <span>{{ applyInfo?.agentUser?.bankName }}</span>
              <span>({{ applyInfo?.agentUser?.bankCard?.slice(-4) }})</span>
            </div>
            <div class="i-carbon-chevron-right text-#333333 w-28rpx h-28rpx"></div>
          </div>
        </div>
        <div v-else class="flex flex-col gap-y-40rpx">
          <span class="text-32rpx text-#999999">信息设置</span>
          <div @click="goBindCard" class="flex items-center justify-between pb12rpx box-border">
            <span class="text-28rpx text-#333333">个人银行卡信息</span>
            <div class="i-carbon-chevron-right text-#333333 w-28rpx h-28rpx"></div>
          </div>
        </div>
      </div>
    </div>
    <template
      v-if="
        isWeixin() &&
        loginSettingStore?.loginSetting?.mpState == 1 &&
        applyInfo?.templateArr.length > 0
      "
    >
      //#ifdef H5
      <wx-open-subscribe
        :template="applyInfo?.templateArr"
        id="subscribe-btn"
        @success="handleSubmit"
        @error="handleSubmit"
      >
        <component :is="'script'" type="text/wxtag-template">
          <component :is="'style'">
            #subscribe-btn{ width: 100%; } .subscribe-btn{ border-radius:20px; color: #fff;
            background: linear-gradient(90deg,#ff6b6b 4%,#f6220c 100%); width: 100%; box-sizing:
            border-box; text-align: center; }
          </component>
        </component>
        <component :is="'script'" type="text/wxtag-template" style="display: block">
          <slot>
            <div style="width: 100%; display: flex">
              <div
                class="subscribe-btn"
                :style="{
                  height: btnAtrrpx.height + 'px',
                  lineHeight: btnAtrrpx.height + 'px',
                  paddingLeft: btnAtrrpx.paddingLeft + 'px',
                  paddingRigth: btnAtrrpx.paddingLeft + 'px',
                  fontSize: btnAtrrpx.fontSize + 'px',
                }"
              >
                提交申请
              </div>
            </div>
          </slot>
        </component>
      </wx-open-subscribe>
      //#endif
    </template>
    <div v-else class="px-40rpx w-full box-border pt-20rpx flex flex-col gap-y-10rpx items-center">
      <span class="text-24rpx text-#999999">
        提现扣除{{ showMoney?.fees }}%个人所得税（例：提现100实际到账{{
          showMoney?.amountReceived
        }}）
      </span>
      <wd-button @click="handleSubmit" custom-class="!h-96rpx !rd-48rpx !bg-#8445C7 !w-full" block>
        提交申请
      </wd-button>
    </div>

    <wd-popup v-model="showSign" custom-style="border-radius:32rpx;" @close="showSign = !showSign">
      <div class="flex flex-col items-center gap-y-10px p-10px">
        <wd-img
          width="500rpx"
          height="500rpx"
          enable-preview
          mode="aspectFill"
          :src="agent?.agent?.signUrlQrCode"
        ></wd-img>
        <span>请扫描二维码完成电子签约</span>
      </div>
    </wd-popup>
    <!-- <div
      class="w-full px-10px py15px box-border bg-#ffffff grid grid-cols-1 grid-auto-rows-min gap-y-14px"
    >
      <div class="flex items-baseline gap-x-10px text-32rpx font-500 text-#333333">
        <span>{{ applyInfo?.words?.cashApply?.money ?? '' }}</span>
        <span class="text-26rpx text-#999999">
          {{ `${applyInfo?.words?.cashApply?.minMoney}${applyInfo?.settlement?.minMoney}元` }}
        </span>
      </div>
      <div class="pt15px flex items-center">
        <span class="text-48rpx">￥</span>
        <wd-input
          v-model="inputValue"
          placeholderClass="!hidden"
          no-border
          custom-input-class="!text-48rpx"
          type="digit"
        />
      </div>
      <div class="text-26rpx text-#999999 flex items-center gap-x-5px">
        <span>{{ applyInfo?.words?.cashApply?.capital }}{{ applyInfo?.agentUser?.money }}元</span>
        <wd-button
          @click="inputValue = applyInfo?.agentUser?.money"
          type="text"
          custom-class="!m-unset !text-#0479FF"
        >
          全部提现
        </wd-button>
      </div>
    </div>
    <div
      class="w-full px-10px py15px box-border bg-#ffffff grid grid-cols-1 grid-auto-rows-min gap-y-14px"
    >
      <div class="flex items-baseline gap-x-10px text-32rpx font-500 text-#333333">
        <span>提现方式</span>
      </div>
      <div class="pt15px w-full">
        <wd-radio-group custom-class="!flex !flex-col !gap-y-20px" v-model="withdrawType">
          <wd-radio
            custom-class="!gap-x-20px"
            v-if="applyInfo?.settlement?.payType.includes(10)"
            :value="10"
          >
            <div class="text-26rpx text-#333333 flex items-start">微信</div>
            <div v-if="withdrawType === 10">
              <view class="mt20rpx">
                <wd-input
                  :disabled="Boolean(applyInfo?.agentUser?.realName)"
                  name="realName"
                  v-model="realName"
                  type="text"
                  placeholder-class="text-#999999"
                  placeholder="请输入姓名"
                />
              </view>
              <view
                v-if="!applyInfo?.agentUser?.realName"
                class="gap-x-5px flex items-center text-22rpx text-#999999"
              >
                <div class="i-carbon-information text-24rpx"></div>
                请输入收款人真实姓名进行提现操作！
              </view>
            </div>
          </wd-radio>
          <wd-radio
            v-if="applyInfo?.settlement?.payType.includes(20)"
            custom-class="!gap-x-20px"
            :value="20"
          >
            <div class="text-26rpx text-#333333 flex items-start">支付宝</div>
            <div v-if="withdrawType === 20">
              <view class="mt20rpx">
                <wd-input
                  v-model="form.alipayName"
                  type="text"
                  placeholder-class="text-#999999"
                  placeholder="请输入姓名"
                />
              </view>
              <view class="mt20rpx">
                <wd-input
                  v-model="form.alipayAccount"
                  type="text"
                  placeholder-class="text-#999999"
                  placeholder="请输入支付宝账号"
                />
              </view>
            </div>
          </wd-radio>
          <wd-radio
            custom-class="!gap-x-20px !items-unset"
            v-if="applyInfo?.settlement?.payType.includes(30)"
            :value="30"
          >
            <div class="text-26rpx text-#333333 flex items-start gap-x-10px">
              银行卡
              <span>
                {{ applyInfo?.agentUser?.bankName }}({{
                  applyInfo?.agentUser?.bankCard?.slice(-4)
                }})
              </span>
            </div>
            <div v-if="withdrawType === 30">
              <view class="mt20rpx">
                <wd-input
                  v-model="form.bankName"
                  type="text"
                  :readonly="Boolean(applyInfo?.agentUser?.identity)"
                  placeholder-class="text-#999999"
                  placeholder="请输入姓名"
                />
              </view>
              <view class="mt20rpx">
                <wd-input
                  type="text"
                  :readonly="Boolean(applyInfo?.agentUser?.bankName)"
                  v-model="form.bankAccount"
                  placeholder-class="text-#999999"
                  placeholder="请输入开户行名称/地址"
                />
              </view>
              <view class="mt20rpx">
                <wd-input
                  v-model="form.bankCard"
                  :readonly="Boolean(applyInfo?.agentUser?.bankCard)"
                  type="text"
                  placeholder-class="text-#999999"
                  placeholder="请输入银行卡号"
                />
              </view>
            </div>
          </wd-radio>
        </wd-radio-group>
      </div>
    </div>
    <template
      v-if="
        isWeixin() &&
        loginSettingStore?.loginSetting?.mpState == 1 &&
        applyInfo?.templateArr.length > 0
      "
    >
      //#ifdef H5
      <wx-open-subscribe
        :template="applyInfo?.templateArr"
        id="subscribe-btn"
        @success="handleSubmit"
        @error="handleSubmit"
      >
        <div v-is="'script'" type="text/wxtag-template" slot="style">
          <div v-is="'style'">
            #subscribe-btn{ width: 100%; } .subscribe-btn{ border-radius:20px; color: #fff;
            background: linear-gradient(90deg,#ff6b6b 4%,#f6220c 100%); width: 100%; box-sizing:
            border-box; text-align: center; }
          </div>
        </div>
        <div v-is="'script'" type="text/wxtag-template">
          <div
            class="subscribe-btn"
            :style="{
              height: btnAtrrpx.height + 'px',
              lineHeight: btnAtrrpx.height + 'px',
              paddingLeft: btnAtrrpx.paddingLeft + 'px',
              paddingRigth: btnAtrrpx.paddingLeft + 'px',
              fontSize: btnAtrrpx.fontSize + 'px',
            }"
          >
            {{ applyInfo?.words?.cashApply?.submit }}
          </div>
        </div>
      </wx-open-subscribe>
      //#endif
    </template>
    <div class="px-30rpx box-border">
      <wd-button @click="handleSubmit" custom-class="!h-88rpx" block>
        {{ applyInfo?.words?.cashApply?.submit }}
      </wd-button>
    </div> -->
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import {
  applyUserCash,
  getWxSignPackage,
  userApplySubmit,
  userCashFees,
  userAgent,
} from '@/service'
import { getPlatform } from '@/utils'
import { mpMessage, isWeixin, subMessage } from '@/utils'
import { useLoginSettingStore } from '@/store'
const loginSettingStore = useLoginSettingStore()

const { safeAreaInsets } = uni.getSystemInfoSync()

interface BtnAttrPx {
  height: number
  paddingLeft: number
  fontSize: number
}

const showSign = ref(false)

const { data: agent, run } = useRequest(() => userAgent())
watch(
  () => agent?.value?.agent,
  (agentInfo) => {
    if (agentInfo?.isSign === 0 && agentInfo?.signUrl) {
      showSign.value = true
    }
  },
  { deep: true },
)

onShow(() => {
  fetchData()
  run()
})

onMounted(() => {
  // #ifdef H5
  if (isWeixin()) {
    getTemplateId()
    getBtnInfo()
  }
  // #endif
})

const getTemplateId = async () => {
  // #ifdef H5
  const { data: signPackage } = await getWxSignPackage({
    url: window.location.href,
    paySource: getPlatform(),
  })

  mpMessage(signPackage?.signPackage)
  // #endif
}

const btnAtrrpx = ref<BtnAttrPx>()

const getBtnInfo = (): void => {
  const defalueVal = {
    height: 88,
    paddingLeft: 28,
    fontSize: 32,
  }
  btnAtrrpx.value = defalueVal

  uni.getSystemInfo({
    success: (res) => {
      const scale = res.screenWidth / 750
      const newObj: BtnAttrPx = {
        height: btnAtrrpx.value.height * scale,
        paddingLeft: btnAtrrpx.value.paddingLeft * scale,
        fontSize: btnAtrrpx.value.fontSize * scale,
      }
      btnAtrrpx.value = newObj
    },
    fail: () => {
      btnAtrrpx.value = defalueVal
    },
  })
}

const applyInfo = ref<Api.User.ApplyData>()

const fetchData = async () => {
  const { data } = await applyUserCash({ platform: getPlatform() })

  applyInfo.value = data

  realName.value = data?.agentUser?.realName

  form.value.bankAccount = data?.agentUser?.realName
  form.value.bankName = data?.agentUser?.bankName
  form.value.bankCard = data?.agentUser?.bankCard
}

const inputValue = ref<string>('')

const withdrawType = ref<number>(30)

// 支付方式相关

const realName = ref('')

const form = ref({
  alipayName: '',
  alipayAccount: '',
  bankName: '',
  bankAccount: '',
  bankCard: '',
})

const { data: userFees, run: fetchUserFee } = useRequest(() =>
  userCashFees({ money: inputValue.value }),
)

const handleBlur = () => {
  if (!inputValue.value) return
  fetchUserFee()
}

const { data: showMoney } = useRequest(() => userCashFees({ money: '100.00' }), {
  immediate: true,
})

const handleApplyAll = () => {
  if (applyInfo.value?.agentUser?.money === '0.00') {
    uni.showToast({
      title: '暂无可提现金额',
      icon: 'none',
    })
    return
  }
  inputValue.value = applyInfo.value?.agentUser?.money
  fetchUserFee()
}

const handleSubscribe = async () => {
  uni.showLoading({
    title: '正在提交',
    mask: true,
  })

  await userApplySubmit({
    ...form.value,
    payType: withdrawType.value,
    money: inputValue.value,
    fees: userFees.value?.fees,
    amountReceived: userFees.value?.amountReceived,
  })

  setTimeout(() => {
    uni.hideLoading()
  }, 500)
  uni.showToast({
    title: '申请成功',
    duration: 2000,
    icon: 'success',
  })
  setTimeout(() => {
    uni.navigateBack()
  }, 500)
}

const handleSubmit = async () => {
  if (!applyInfo?.value?.agentUser?.bankCard) {
    uni.showToast({ title: '请先绑定银行卡', icon: 'none' })
    return
  }
  if (!inputValue.value) {
    uni.showToast({ title: '请输入金额', icon: 'none' })
    return
  }
  await fetchUserFee()
  subMessage(applyInfo.value?.templateArr, handleSubscribe)
}

const goBindCard = () => {
  uni.navigateTo({ url: '/pages-my/promotion/apply-bind-card/index' })
}
</script>

<style lang="scss" scoped>
:deep(.wd-radio__label) {
  flex: 1 !important;
}
:deep(.wd-navbar__title) {
  color: #ffffff !important;
}
:deep(.wd-navbar__arrow) {
  color: #ffffff !important;
}
</style>

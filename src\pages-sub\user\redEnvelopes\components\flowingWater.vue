<template>
  <view class="flex-1 flex-col flex overflow-hidden box-border">
    <z-paging
      ref="paging"
      v-model="tableData"
      :default-page-size="10"
      :fixed="true"
      class="flex-1"
      @query="queryList"
    >
      <template #top>
        <div
          class="pl40rpx flex w-full justify-center items-center py36rpx text-#333333 px20rpx box-border text-36rpx fw-700"
        >
          <div class="flex-1 flex justify-center">明细记录</div>
          <div class="px20rpx box-border">
            <wd-icon name="close" size="36rpx" @click="handleCloseModal"></wd-icon>
          </div>
        </div>

        <div
          class="flex justify-center items-center pb30rpx box-border gap20rpx text-#666666 text-24rpx fw-500"
        >
          <div>
            红包总额:
            <span class="text-#FF7D26">{{ formatLargeNumber(totalAmount) }}</span>
          </div>
          <div>
            红包剩余:
            <span class="text-#FF7D26">{{ formatLargeNumber(remainingAmount) }}</span>
          </div>
        </div>
      </template>
      <view class="px30rpx">
        <view
          class="flex items-start justify-center flex-col gap-20rpx py15rpx"
          v-for="(item, index) in tableData"
          :key="item.statementId || index"
        >
          <view class="w-full flex justify-between items-center gap10rpx text-24rpx text-#999999">
            <view class="rounded-4rpx mr10rpx max-w-[40%] line-clamp-2 text-#FF7D26 bg-#FFECDF">
              {{ item.orderTitle }}
            </view>

            <view v-if="item?.remarks" class="text-24rpx">
              <span>{{ item?.remarks }}</span>
            </view>
          </view>
          <view class="w-full flex justify-between items-center">
            <view class="text-#999999 text-22rpx">
              <!-- <text v-if="Number(item.amountUsed) > 0" class="">使用日期:</text>
              <text v-else class="">收入日期:</text> -->
              <text class="">日期:</text>
              <text class="">{{ dayjs(item.createTime).format('YYYY.MM.DD HH:mm:ss') }}</text>
            </view>
            <view class="flex-1 flex justify-end text-#ff5704 text-24rpx">
              <text>{{ item.amountUsed }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { formatLargeNumber } from '@/utils'
import { fetchUserRedPacketLogList } from '@/service'
import { computed, onMounted, ref } from 'vue'
// import RechargePopup from './part/recharge.vue';

// 响应式状态
const tableData = ref<Api.User.UserRedPacketLogRecords[]>([])
const props = defineProps({
  redPacketId: {
    type: String as PropType<string>,
    default: '',
  },
  remainingAmount: {
    type: [Number, String] as PropType<number | string>,
  },
  totalAmount: {
    type: [Number, String] as PropType<number | string>,
  },
})
let state = reactive({
  totalAmount: 0,
  remainingAmount: 0,
})
const emit = defineEmits<{
  (e: 'close'): void
}>()

const handleCloseModal = () => {
  emit('close')
}
// 监听 redPacketId 变化并重新加载数据
watch(
  () => props.redPacketId,
  (newVal) => {
    if (newVal) {
      paging.value?.reload()
    }
  },
  {
    // immediate: true, // 页面初次加载时也执行一次
    deep: false,
  },
)
// 生命周期 - 挂载完成
onMounted(() => {})

// 生命周期 - 页面触底
onReachBottom(() => {})

const paging = ref()
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await fetchUserRedPacketLogList({
      pageIndex: pageNo,
      pageSize: pageSize,
      redPacketId: props.redPacketId,
      appId: import.meta.env.VITE_APPID,
    })

    paging.value.complete(data.records)
  } catch (err) {
    paging.value.complete(false)
  }
}
</script>

<style lang="scss" scoped></style>

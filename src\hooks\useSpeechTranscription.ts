import { ref, onUnmounted } from 'vue'

const getToken = require('../static/libs/token').getToken
const SpeechTranscription = require('../static/libs/st')

interface AppConfig {
  AKID: string
  AKKEY: string
  URL: string
  APPKEY: string
}

function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export function useSpeechTranscription(appConfig: AppConfig) {
  const stResult = ref('未开始识别')
  const stStart = ref(false)
  const st = ref<InstanceType<typeof SpeechTranscription> | null>(null)
  const recorderManager = uni.getRecorderManager()

  async function initST() {
    try {
      const token = await getToken(appConfig.AKID, appConfig.AKKEY)
      const stInstance = new SpeechTranscription({
        url: appConfig.URL,
        appkey: appConfig.APPKEY,
        token,
      })

      stInstance.on('started', (msg: string) => {
        console.log('Client recv started')
      })

      stInstance.on('changed', (msg: string) => {
        console.log('Client recv changed:', msg)
      })

      stInstance.on('completed', (msg: string) => {
        console.log('Client recv completed:', msg)
      })

      stInstance.on('begin', (msg: string) => {
        console.log('Client recv sentenceBegin:', msg)
      })

      stInstance.on('end', (msg: string) => {
        console.log('Client recv sentenceEnd:', msg)
        const result = JSON.parse(msg ?? '{}')?.payload?.result
        stResult.value = result
      })

      stInstance.on('failed', (msg: string) => {
        console.log('Client recv failed:', msg)
      })

      stInstance.on('closed', () => {
        console.log('Client recv closed')
      })

      st.value = stInstance

      recorderManager.onFrameRecorded((res) => {
        if (res.isLastFrame) {
          console.log('record done')
        }
        if (st.value && stStart.value) {
          console.log('send ' + res.frameBuffer.byteLength)
          st.value.sendAudio(res.frameBuffer)
        }
      })

      recorderManager.onStart(() => {
        console.log('start recording...')
      })

      recorderManager.onStop((res) => {
        console.log('🚀 ~ recorderManager.onStop ~ res:', res)
        console.log('stop recording...')
        if (res.tempFilePath) {
          // 删除临时文件
          uni.getFileSystemManager().unlink({
            filePath: res.tempFilePath,
            success() {
              console.log('临时录音文件删除成功')
            },
            fail(err) {
              console.error('删除失败', err)
            },
          })
        }
      })

      recorderManager.onError((err) => {
        console.log('recording failed:', err)
      })
    } catch (e) {
      console.error('Failed to init ST:', e)
    }
  }

  async function startST() {
    if (!st.value || stStart.value) {
      return
    }

    try {
      await st.value.start(st.value.defaultStartParams())
      stStart.value = true

      recorderManager.start({
        duration: 600000,
        numberOfChannels: 1,
        sampleRate: 16000,
        format: 'PCM',
        frameSize: 4,
      })
    } catch (e) {
      console.error('startST failed:', e)
    }
  }

  async function stopST() {
    recorderManager.stop()
    await sleep(500)
    if (stStart.value && st.value) {
      try {
        await st.value.close()
      } catch (e) {
        console.error('close ST failed:', e)
      } finally {
        stStart.value = false
      }
    }
  }

  function shutdownST() {
    stStart.value = false
    recorderManager.stop()
    if (st.value) {
      st.value.shutdown()
    }
  }

  onUnmounted(() => {
    shutdownST()
  })

  return {
    stResult,
    stStart,
    initST,
    startST,
    stopST,
  }
}

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '登录',
  },
}
</route>

<template>
  <view class="login-container">
    <view class="skip" @click="gotoPage('/pages/index/index')">跳过→</view>
    <!-- <view v-if="state.isLogin !== 0">
      <view class="login_topbpx">
        <view class="login_tit" v-if="state.isLogin === 2">快速登录</view>
        <view class="login_tit" v-if="state.isLogin === 1">账号登录</view>
        <view class="login_top" v-if="state.isLogin === 2">首次登录会自动注册</view>
      </view>
      <view class="group-bd">
        <view class="form-level d-s-c">
          <input
            class="login-input"
            :adjust-position="false"
            type="text"
            v-model="state.formData.mobile"
            placeholder="请填写手机号"
            :disabled="state.isSend"
          />
        </view>
        <view class="form-level flex items-center" v-if="state.isLogin === 1">
          <input
            class="login-input flex-1"
            :adjust-position="false"
            type="password"
            v-model="state.formData.password"
            placeholder="请输入密码"
          />
          <button class="get-code-btn text-#ff5704" @click="handleForgotPassword">
            忘记密码？
          </button>
        </view>
     
      </view>
    </view> -->

    <!-- <view v-if="state.isLogin === 0">
      <view class="login_topbpx">
        <view class="login_tit">找回密码</view>
        <view class="login_top">请确认您的账号已绑定此手机号码</view>
      </view>
      <view class="group-bd text-28rpx">
        <view class="form-level flex items-center h98rpx">
          <input
            type="text"
            v-model="state.resetForm.mobile"
            placeholder="请填写手机号"
            :disabled="state.isSend"
          />
        </view>
        <view class="form-level flex items-center">
          <view class="flex-1 flex items-center h98rpx">
            <input
              class="flex-1"
              :adjust-position="false"
              type="number"
              v-model="state.resetForm.code"
              placeholder="请填写验证码"
            />
            <button class="get-code-btn" @click="handleSendCode" :disabled="state.isSend">
              {{ state.sendBtnTxt }}
            </button>
          </view>
        </view>
        <view class="form-level flex items-center h98rpx">
          <input
            type="password"
            :adjust-position="false"
            v-model="state.resetForm.password"
            placeholder="请输入密码"
          />
        </view>
        <view class="form-level flex items-center h98rpx">
          <input
            type="password"
            :adjust-position="false"
            v-model="state.resetForm.repassword"
            placeholder="请再次输入密码"
          />
        </view>
      </view>
    </view> -->
    <view class="wechatapp">
      <image
        class="logo-image"
        :src="
          setting.loginLogo ||
          'https://file.shanqianqi.com/image/2025/06/06/5e92b4f0175641d5ac8c93adb2de8175.png'
        "
      ></image>
      <view class="setting-name">{{ setting.name }}</view>
    </view>
    <view class="auth-title fb">{{ setting.loginDesc }}</view>
    <view class="auth-subtitle">授权绑定手机号 为您提供更好的服务</view>

    <!-- <button
      v-if="state.isLogin === 2"
      class="rounded-30rpx text-white bg-#ff5704 sub-btn"
      open-type="getPhoneNumber"
      @getphonenumber="getUserInfo"
    >
      立即登录
    </button>
    <button
      v-if="state.isLogin === 1"
      class="rounded-30rpx text-white bg-#ff5704 fw-bold sub-btn"
      @click="handleLogin"
    >
      立即登录
    </button>
    <button
      v-if="state.isLogin === 0"
      class="rounded-30rpx text-white bg-#ff5704 sub-btn"
      @click="handleResetPassword"
    >
      确认
    </button> -->
    <div>
      <wd-button @click="handleWxLogin" block custom-class="!rd-30rpx mt-10px !h-90rpx">
        一键登录
      </wd-button>
      <view class="flex items-center mt30rpx text-24rpx mb30rpx justify-center">
        <wd-checkbox v-model="state.isAgree" @click="toggleAgree"></wd-checkbox>
        我已阅读并接受
        <text class="text-#ff5704" @click="openAgreement('service')">《用户协议》</text>
        和
        <text class="text-#ff5704" @click="openAgreement('privacy')">《隐私政策》</text>
      </view>
    </div>

    <!-- <view class="py-20rpx flex justify-center items-center text-#666666 text-30rpx">
      <view v-if="state.isLogin === 1" @click="switchToMobileLogin">手机登录</view>
      <view v-if="state.isLogin === 2" @click="switchToAccountLogin">账号登录</view>
      <view v-if="state.isLogin === 0" @click="switchToLogin">立即登录</view>
    </view> -->
  </view>
</template>

<script lang="ts" setup>
import { onMountedounted, reacref, watch } from 'vue'
import { getCodeType } from '@/service/api/user'

import { useUserStore } from '@/store'

const userStore = useUserStore()

defineOptions({
  name: 'Login',
})

// 类型定义
interface FormData {
  mobile: string
  password: string
  code: string
}

interface ResetForm {
  mobile: string
  code: string
  password: string
  repassword: string
}

const setting = ref({
  loginDesc: '',
  loginLogo: '',
  name: '',
})

// 响应式状态
const state = reactive({
  formData: {
    mobile: '',
    password: '',
    code: '',
  } as FormData,
  resetForm: {
    mobile: '',
    code: '',
    password: '',
    repassword: '',
  } as ResetForm,
  isSend: false,
  sendBtnTxt: '获取验证码',
  second: 60,
  isLogin: 1, // 0: 找回密码, 1: 账号登录, 2: 手机登录
  smsOpen: false,
  isAgree: false,
  theme: '',
})

const getCodeTypeFun = async () => {
  const res = await getCodeType({})
  setting.value = res.data
}

// 获取验证码开关状态
const fetchLoginSettings = () => {
  // 模拟请求，实际替换为uni.request
  setTimeout(() => {
    state.smsOpen = true // 假设接口返回true
  }, 500)
}

// 生命周期钩子
onMounted(() => {
  getCodeTypeFun()
  fetchLoginSettings()
})

const redirect = ref('')
onLoad((option) => {
  if (option.redirect) {
    redirect.value = decodeURIComponent(option.redirect)
  }
})

// 发送验证码
const handleSendCode = () => {
  const mobile = state.isLogin === 0 ? state.resetForm.mobile : state.formData.mobile
  if (!/^1[3-9]\d{9}$/.test(mobile)) {
    uni.showToast({ title: '手机号格式错误', icon: 'none' })
    return
  }

  state.isSend = true
  state.sendBtnTxt = `${state.second}秒`
  state.second--

  const timer = setInterval(() => {
    if (state.second <= 0) {
      clearInterval(timer)
      state.isSend = false
      state.sendBtnTxt = '获取验证码'
      state.second = 60
      return
    }
    state.sendBtnTxt = `${state.second}秒`
    state.second--
  }, 1000)

  // 模拟发送验证码请求
  setTimeout(() => {
    uni.showToast({ title: '验证码发送成功', icon: 'none' })
  }, 1000)
}

//登录
const getUserInfo = () => {
  console.log('获取用户信息')
}
// 处理登录
const handleLogin = () => {
  if (!state.isAgree) {
    uni.showToast({ title: '请阅读并同意用户协议及隐私政策', icon: 'none' })
    return
  }

  if (state.isLogin === 2) {
    if (state.smsOpen && !state.formData.code) {
      uni.showToast({ title: '验证码不能为空', icon: 'none' })
      return
    }
    // 手机登录逻辑
    console.log('手机登录:', state.formData)
  } else {
    if (!state.formData.password) {
      uni.showToast({ title: '密码不能为空', icon: 'none' })
      return
    }
    // 账号登录逻辑
    console.log('账号登录:', state.formData)
  }

  uni.showLoading({ title: '正在登录' })
  setTimeout(() => {
    uni.hideLoading()
    uni.navigateBack()
  }, 1500)
}

const handleWxLogin = () => {
  if (!state.isAgree) {
    uni.showToast({ title: '请阅读并同意用户协议及隐私政策', icon: 'none' })
    return
  }
  userStore.wxLogin(true, redirect.value)
}

// 处理密码重置
const handleResetPassword = () => {
  if (!/^1[3-9]\d{9}$/.test(state.resetForm.mobile)) {
    uni.showToast({ title: '手机号格式错误', icon: 'none' })
    return
  }
  if (!state.resetForm.code) {
    uni.showToast({ title: '验证码不能为空', icon: 'none' })
    return
  }
  if (state.resetForm.password.length < 6) {
    uni.showToast({ title: '密码至少6位', icon: 'none' })
    return
  }
  if (state.resetForm.password !== state.resetForm.repassword) {
    uni.showToast({ title: '两次密码不一致', icon: 'none' })
    return
  }

  uni.showLoading({ title: '正在重置密码' })
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({ title: '密码重置成功', icon: 'none' })
    state.isLogin = 1
  }, 1500)
}

// 切换到手机登录
const switchToMobileLogin = () => {
  state.isLogin = 2
}

// 切换到账号登录
const switchToAccountLogin = () => {
  state.isLogin = 1
}

// 切换到登录页面
const switchToLogin = () => {
  state.isLogin = 1
}

// 处理忘记密码
const handleForgotPassword = () => {
  state.isLogin = 0
}

// 打开协议页面
const openAgreement = (type: 'service' | 'privacy') => {
  uni.navigateTo({
    url: `/pages-sub/webview/ue?type=${type}`,
  })
}

// 切换协议勾选状态
const toggleAgree = () => {
  //   state.isAgree = !state.isAgree
}

// 跳转页面
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
</script>

<style lang="scss" scoped>
// @import "./login.scss"; // 保持原样式引入方式
.wechatapp {
  padding: 120rpx 0 48rpx;
  margin-bottom: 72rpx;
  text-align: center;

  .logo-image {
    margin: 0 auto;
    margin-bottom: 20rpx;
    width: 145rpx;
    height: 145rpx;
    border-radius: 50%;
  }
  .setting-name {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30rpx;
    color: #333333;
  }
}
.auth-title {
  color: #333333;
  font-size: 30rpx;
  margin-top: 170rpx;
  margin-bottom: 85rpx;
  text-align: center;
  font-weight: bold;
}
.auth-subtitle {
  color: #777;
  margin-bottom: 20rpx;
  font-size: 22rpx;
  text-align: center;
}
.login-container {
  padding: 30rpx;
  background: #fff;
  height: calc(100vh - 30rpx);

  .skip {
    text-align: right;
    padding: 20rpx;
    font-size: 28rpx;
    color: #666;
  }

  .login_topbpx {
    margin-bottom: 40rpx;

    .login_tit {
      font-size: 40rpx;
      font-weight: bold;
      color: rgb(153, 153, 153) #333;
    }

    .login_top {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #999;
    }
  }

  .form-level {
    margin-bottom: 30rpx;
    border-bottom: 1rpx solid #e5e5e5;

    .login-input {
      height: 80rpx;
      font-size: 28rpx;
    }

    .get-code-btn {
      background: none;
      font-size: 24rpx;
      margin-left: 20rpx;
    }
  }

  .sub-btn {
    margin-top: 60rpx;
    height: 90rpx;
    line-height: 90rpx;
    font-size: 30rpx;
  }

  .agreement {
    font-size: 28rpx;
    margin-right: 10rpx;
    color: #666;

    &.active {
      color: #18a0fb;
    }
  }
}
</style>

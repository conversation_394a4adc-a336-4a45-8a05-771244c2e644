<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '发布内容',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#ffffff pl-25rpx pr-25rpx pt-30rpx h-100%">
    <wd-navbar
      left-arrow
      :safeAreaInsetTop="true"
      :placeholder="true"
      fixed
      @click-left="handleBack"
    >
      <template #title>
        <view class="">
          <span class="ml-20rpx">发布内容</span>
        </view>
      </template>
    </wd-navbar>

    <view class="form-container">
      <!-- 标题输入 -->
      <view class="form-item">
        <wd-input type="text" v-model="form.title" placeholder="请输入标题（必填）" clearable />
      </view>

      <!-- 内容输入 -->
      <view class="form-item">
        <wd-textarea
          v-model="form.content"
          placeholder="请输入内容（必填）"
          :maxlength="500"
          show-word-limit
        />
      </view>

      <!-- 图片上传 -->
      <view class="form-item">
        <view class="upload-title">添加图片（最多9张）</view>
        <wd-upload
          accept="image"
          multiple
          :max-count="9"
          :limit="9"
          :upload-method="uploadMethod"
          :file-list="fileList"
          :action="uploadBaseUrl"
          name="iFile"
          @change="handleUploadChange"
          @fail="handleUploadFail"
        ></wd-upload>
      </view>

      <!-- 提交按钮 -->
      <view class="add-btn theme-btn bg-#FF7D26 text-white" @click="handleSubmit">确认发布</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { discourseAddDiscourse } from '@/service'
import { createUploadHandler, getEnvBaseUploadUrl } from '@/utils'

// 表单数据
const form = reactive({
  title: '',
  content: '',
  imageList: [] as Array<{
    url: string
    name?: string
    size?: number
    type?: string
  }>,
})

// 上传相关配置
const uploadMethod = createUploadHandler('image')
const uploadBaseUrl = getEnvBaseUploadUrl()

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`
// 图片上传相关
const fileList = ref<Array<{ url: string }>>([])

// 图片上传变化
const handleUploadChange = ({ fileList: newFileList }) => {
  fileList.value = newFileList
  form.imageList = newFileList.map((file) => file?.response?.filePath)
}

// 图片上传失败处理
const handleUploadFail = (error: any) => {
  uni.showToast({
    title: '图片上传失败: ' + (error.message || '未知错误'),
    icon: 'none',
    duration: 1500,
  })
}

// 表单验证
const validateForm = () => {
  let isValid = true

  if (!form.title.trim()) {
    isValid = false
    uni.showToast({
      title: '请输入标题',
      icon: 'none',
    })
  }

  if (!form.content.trim()) {
    isValid = false
    uni.showToast({
      title: '请输入内容',
      icon: 'none',
    })
  }

  return isValid
}

// 提交表单处理
const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    uni.showLoading({
      title: '发布中...',
      mask: true,
    })
    const postData = {
      title: form.title,
      content: form.content,
      imageList: form.imageList, // 转换为JSON字符串
      mainCategoryId: 1,
      type: 'content',
    }

    console.log('提交的数据:', postData) // 调试用

    const res = await discourseAddDiscourse(postData)

    uni.hideLoading()
    uni.showToast({
      title: '发布成功',
      icon: 'success',
      duration: 1500,
    })

    // 发布成功后返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '发布失败，请重试',
      icon: 'none',
      duration: 1500,
    })
    console.error('发布失败:', error)
  }
}

// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1,
  })
}
</script>

<style lang="scss" scoped>
.form-container {
  padding-bottom: 150rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.upload-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.add-btn {
  position: fixed;
  bottom: 50rpx;
  left: 5%;
  width: 90%;
  height: 96rpx;
  border-radius: 48rpx;
  line-height: 96rpx;
  text-align: center;
  z-index: 100;
}

::v-deep .wd-textarea {
  padding: 20rpx 0 !important;
  min-height: 200rpx;
}
</style>

<template>
  <view class="szjp">
    <view v-if="show">
      <view class="mask" @tap="Close"></view>
      <view class="box" :class="AlertObj[AlertClass][AlertClassUp]">
        <view class="bottom">
          <!-- <view class="bg">
            <image src="./static/bg.png" mode="widthFix" />
          </view> -->
          <view class="tle">
            <view class="tle_lft">
              <image src="./static/pwd.png" mode="widthFix" />
              <view class="tle_1">{{ is_rpwd ? '请设置6位支付密码' : txt }}</view>
              <view class="tle_2">(长度{{ pwdlength }}位)</view>
            </view>
            <view class="tle_rgt" @tap.stop="is_vis = !is_vis">
              <block v-if="is_vis">
                <image src="./static/yj2.png" mode="widthFix" />
              </block>
              <block v-else>
                <image src="./static/yj1.png" mode="widthFix" />
              </block>
            </view>
          </view>
          <view style="margin-left: 105rpx" v-if="is_rpwd && smsOpen">
            <view class="certification_head_body_item">
              <view class="icon iconfont icon-phone"></view>
              <text type="text">{{ user.mobile }}</text>
              <button style="margin-left: 50rpx" type="default" @click="getCode(user.mobile)">
                {{ send_btn_txt }}
              </button>
            </view>
            <view class="certification_head_body_item">
              <view class="icon iconfont icon-yanzhengma"></view>
              <input type="text" name="code" v-model="code" placeholder="请输入验证码" />
            </view>
          </view>
          <view class="pwd_box" @click.stop>
            <view
              class="pwd-text"
              :class="{ active: idx == k }"
              v-for="(v, k) in pwdlength"
              :key="k"
            >
              {{ star_lis[k] || '' }}
            </view>
          </view>
          <!-- @tap.stop="handleResetChange" -->
          <view class="pwd_info">
            <!-- <view class="tle_2">{{ is_rpwd ? '请输入设置的新密码' : '忘记密码？' }}</view> -->
          </view>
          <view class="solt" @click.stop>
            <view class="s_lft">
              <button
                class="s_li"
                type="primary"
                plain="true"
                v-for="(v, k) in 9"
                :key="k"
                @tap="click(k + 1)"
              >
                {{ k + 1 }}
              </button>
              <button class="s_li s_o" type="primary" plain="true" @tap="click(0)">0</button>
              <button class="s_li s_sq" type="primary" plain="true" @tap="Close">
                <image src="./static/sq.png" mode="widthFix" />
              </button>
            </view>
            <view class="s_rgt">
              <view class="s_cx">
                <button class="s_li s_x" type="primary" plain="true" @tap="del">
                  <image src="./static/ht.png" mode="widthFix" />
                </button>
              </view>
              <view class="s_qd">
                <button class="s_li s_s !bg-#FF7D26" type="primary" plain="true" @tap="submit">
                  确定
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/*引入插件*/
export default {
  props: {
    txt: {
      type: String,
      default: '支付密码',
    },
    pwdlength: {
      type: [Number, String],
      default: 1,
    },
    is_rpwd: {
      type: Boolean,
      default: false,
    },
    smsOpen: {
      type: [Boolean],
      default: false,
    },
  },
  data() {
    return {
      Repeat: true,
      show: false,
      AlertObj: [['a-bounceinB', 'a-bounceoutB']],
      AlertClass: 0,
      AlertClassUp: 0,
      res_pwd: [],
      fa_par: '',
      is_vis: false, //密码是否可见
      // is_rpwd: false, //是否修改密码
      user: {},
      /*当前秒数*/
      second: 60,
      /*是否已发验证码*/
      is_send: false,
      send_btn_txt: '获取验证码',
      code: null,
    }
  },
  //   计算属性
  computed: {
    idx() {
      return this.res_pwd.length
    },
    star_lis() {
      if (this.is_vis) {
        return this.res_pwd
      } else {
        let arr = []
        this.res_pwd.forEach((v) => {
          arr.push('*')
        })
        return arr
      }
    },
  },

  mounted() {},
  methods: {
    click(e) {
      if (this.res_pwd.length <= this.pwdlength - 1) {
        this.res_pwd.push(e)
        // #ifdef APP-PLUS
        plus.device.vibrate(50) //震动
        // #endif
      } else {
        this.toast(`最大长度${this.pwdlength}位`)
      }
    },
    del() {
      this.res_pwd.pop()
      // #ifdef APP-PLUS
      plus.device.vibrate(50) //震动
      // #endif
    },
    submit() {
      //   console.log('确定密码为', this.res_pwd.join(''))
      // #ifdef APP-PLUS
      plus.device.vibrate(50) //震动
      // #endif
      if (this.res_pwd.length == this.pwdlength) {
        let res = this.res_pwd.join('')
        if (this.is_rpwd) {
          this.$emit('pwd_reset', res, this.fa_par) //调用父元素方法
          this.Close()
        } else {
          this.$emit('pwd_e', res, this.fa_par) //调用父元素方法
          this.Close()
        }
      } else {
        this.toast(`长度未满足`)
      }
    },
    handleResetChange() {
      this.$emit('reset-change')
    },

    // 打开
    Open(par) {
      if (par) {
        console.log('父元素传参', par)
        this.fa_par = par //父元素传参
      }
      if (!this.Repeat) {
        return false
      }
      this.res_pwd = []
      this.Repeat = false
      this.AlertClassUp = 0
      this.show = true
      setTimeout(() => {
        this.Repeat = true
      }, 300)
    },
    // 关闭
    Close() {
      if (!this.Repeat) {
        return false
      }
      clearTimeout(this.SetTime)
      this.Repeat = false
      this.AlertClassUp = 1
      setTimeout(() => {
        this.show = false
        this.Repeat = true
      }, 300)
    },
    toast(val) {
      uni.showToast({
        title: val,
        icon: 'error',
      })
    },
    //获取验证码
    getCode(mobile) {
      let self = this
      this.$tn.message.loading('正在获取验证码')
      this.$tn.message.closeLoading()
      let params = {
        phonenumber: mobile,
      }
      captchaSms(params).then((res) => {
        if (res.code !== 200) {
          setTimeout(() => {
            uni.showToast({
              title: res.msg,
              mask: false,
              icon: 'none',
            })
          }, 500)
        } else {
          this.$tn.message.toast('验证码已经发送')
          self.is_send = true
          self.changeMsg()
        }
      })
    },
    changeMsg() {
      if (this.second > 0) {
        this.send_btn_txt = this.second + '秒'
        this.second--
        setTimeout(this.changeMsg, 1000)
      } else {
        this.send_btn_txt = '获取验证码'
        this.second = 60
        this.is_send = false
      }
    },
  },
}
</script>
<style lang="scss" scoped>
/*引入css文件*/
@import './static/szjp.scss';

.certification_head_body_item {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx !important;
}

.certification_head_body_item view {
  margin-right: 10rpx;
}

.certification_head_body_item button {
  width: 172rpx;
  height: 56rpx;
  line-height: 56rpx;
  border: 1rpx #e2231a solid;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: #e2231a;
  padding: 0 20rpx;
}
</style>

import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate' // 数据持久化

const store = createPinia()
store.use(
  createPersistedState({
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
  }),
)

export default store

// 模块统一导出
export * from './user'
export * from './cart'
export * from './confirm-order'
export * from './location'
export * from './login-setting'
export * from './search'
export * from './theme'
export * from './user-setting'
export * from './message'
export * from './forum'

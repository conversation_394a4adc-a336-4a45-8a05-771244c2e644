<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的评价',
  },
}
</route>

<template>
  <view class="flex bg-#f2f2f2 overflow-hidden pb-2 box-border h-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="20"
      :refresher-enabled="false"
      class="flex-1"
      :fixed="false"
      @query="queryList"
    >
      <view class="list">
        <view class="listItem" v-for="(item, index) in dataList" :key="index">
          <view class="timeBox">
            <view class="time">
              <span class="data">{{ item.data }}</span>
              <span class="line"></span>
              <span class="mouth">
                {{ item.mouth }}
                <span class="unit">月</span>
              </span>
              <span class="line"></span>
              <span class="year">
                {{ item.year }}
                <span class="unit">年</span>
              </span>
            </view>
            <view class="delTxt" @click="del(item, index)">删除</view>
          </view>

          <view class="con">
            <view>{{ item.content }}</view>
            <wd-img
              class="img"
              mode="aspectFit"
              v-for="(v, idx) in item.image"
              :width="100"
              :height="100"
              :src="v"
              :preview-src="v"
              :enable-preview="true"
              :key="idx"
            ></wd-img>
          </view>
          <view class="prodcut">
            <image :src="item.productImage" model="aspectFit"></image>
            <view class="r" @click="gotoProduct(item.productId, item.storeId)">
              <view class="title">{{ item.productName }}</view>
              <view class="spec">{{ item.productAttr }}</view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { FetchUserCommentToOrderData, deleteProductComment } from '@/service'
import { ref } from 'vue'

const dataList = ref([])
const paging = ref()
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await FetchUserCommentToOrderData({
      pageIndex: pageNo,
      pageSize: pageSize,
      appId: import.meta.env.VITE_APPID,
    })

    if (data.records && data.records.length > 0) {
      data.records.forEach((v) => {
        v.year = v.createTime.substr(0, 4)
        v.mouth = v.createTime.substr(5, 2)
        v.data = v.createTime.substr(8, 2)
      })
    }
    dataList.value = data.records
    paging.value.complete(data.records)
  } catch (err) {
    paging.value.complete(false)
  }
}
const gotoProduct = (productId, storeId) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${productId}&storeId=${storeId}` })
}
const del = async (item, index) => {
  try {
    let res = await deleteProductComment({
      appId: import.meta.env.VITE_APPID,
      commentId: item.commentId,
    })
    queryList(1, 10)
    uni.showToast({
      title: res.msg || '删除成功',
    })
  } catch (err) {
    uni.showToast({
      title: '删除失败',
    })
  }
}
</script>

<style lang="scss" scoped>
//
page {
  background-color: #ebebeb;
}
.list {
  padding: 20rpx;
}
.listItem {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  .timeBox {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .delTxt {
    color: #fd6a03;
  }
  .time {
    .unit {
      color: rgba(0, 0, 0, 0.9);
      font-weight: 400;
    }
    span {
      font-size: 26rpx;
      color: #000000;
      font-weight: 800;
    }
    .data {
      font-size: 38rpx;
    }
    .line {
      width: 4rpx;
      height: 22rpx;
      background-color: #fd6a03;
      display: inline-block;
      margin: 0 6rpx;
      transform: rotate(18deg);
    }
  }
  .con {
    letter-spacing: 1rpx;
    line-height: 42rpx;
    margin: 60rpx 0 30rpx 0;
    font-size: 24rpx;
    .img {
      width: 200rpx;
      height: 200rpx;
      display: inline-block;
      margin-right: 20rpx;
    }
  }
  .prodcut {
    display: flex;
    align-items: center;
    background: rgba(235, 235, 235, 0.8);
    font-size: 26rpx;
    image {
      width: 90rpx;
      height: 90rpx;
      margin-right: 6rpx;
      border-radius: 6rpx;
    }
    .spec {
      color: #959595;
      font-size: 24rpx;
      margin-top: 6rpx;
    }
  }
}
</style>

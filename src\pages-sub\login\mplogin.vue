<template>
  <view>
    <kfPopupDengl ref="refpopup3" style="width: 100%;"/>
  </view>
</template>

<script>
import kfPopupDengl from '@/components/kf-popup-dengl/kf-popup-dengl.vue'
import {gotoPage} from "@/utils";
import {bindMobile1, getSchemeUrl} from "@/service";
import {useUserStore} from "@/store";

const userStore = useUserStore()
export default {
  components: {
    kfPopupDengl,
  },
  data() {
    return {
      bainPhone: false,
      form: {
        phone: '',
        password: '',
        rpassword: ''
      },
    };
  },
  onLoad(options) {
    let self = this;
    userStore.setToken(options.token);
    userStore.fetchUserCenterConfig()
    // 获取登录前页面
    let url = '/' + uni.getStorageSync('currentPage');
    let pageOptions = uni.getStorageSync('currentPageOptions');
    if (Object.keys(pageOptions).length > 0) {
      url += '?';
      for (let i in pageOptions) {
        url += i + '=' + pageOptions[i] + '&';
      }
      url = url.substring(0, url.length - 1);
    }
    // 执行回调函数
    gotoPage(url, 'redirect');

    if (options.type == 'gzh') {
      this.getSchemeUrls()
    }

  },
  methods: {

    async getSchemeUrls() {
      let params = {
        // path: "/pages/index/index",
        // query: "?uid=1",
      }
      const {data} = await getSchemeUrl(params)
      console.log('data', data)
      if (data) {
        let schemeUrl = data
        window.location.href = schemeUrl;
      }
    },

    async bainMobile() {
      let self = this;
      if (this.form.phone !== '') {
        // 绑定手机号
        try {
          const {data} = await bindMobile1({
            phone: this.form.phone,
            password: this.form.password,
            rpassword: this.form.rpassword,
            type: 'gzh',
          })
          console.log("data", data)
          if (data == '绑定成功') {
            self.$refs.refpopup3.close()
            this.bainPhone = false;
            userStore.clearToken(options.token);
            // uni.removeStorageSync('token');
            // uni.removeStorageSync('userId');
            // uni.removeStorageSync('mobile');
            // 获取登录前页面
            let url = '/' + uni.getStorageSync('currentPage');
            let pageOptions = uni.getStorageSync('currentPageOptions');
            if (Object.keys(pageOptions).length > 0) {
              url += '?';
              for (let i in pageOptions) {
                url += i + '=' + pageOptions[i] + '&';
              }
              url = url.substring(0, url.length - 1);
            }
            // 执行回调函数
            gotoPage(url, 'reLaunch');
          }
        } catch (error) {
          console.log(error);
        } finally {
          uni.hideLoading();
        }
      }
    },
  }
}
</script>

<style>

</style>

<template>
  <view>
    <!-- 	<tn-modal v-model="bainPhone" :custom="true" :maskCloseable="false">
        <tn-form>
          <tn-form-item label="手机号" prop="phone" :labelWidth="130">
            <tn-input v-model="form.phone" placeholder="请输入手机号" type="text" :border="true" :showRightIcon="true"
              rightIcon="phone" />
          </tn-form-item>
          <tn-form-item label="密码" prop="password" :labelWidth="130">
            <tn-input v-model="form.password" placeholder="请输入密码" type="password" :border="true"
              :showRightIcon="true" />
          </tn-form-item>
          <tn-form-item label="确认密码" prop="rpassword" :labelWidth="130">
            <tn-input v-model="form.rpassword" placeholder="请输入确认密码" type="password" :border="true"
              :showRightIcon="true" />
          </tn-form-item>
        </tn-form>
        <tn-button size="lg" width="100%" shape="round" :plain="true" backgroundColor="#01BEFF" fontColor="#080808"
          @click="bainMobile()">绑定手机号</tn-button>
      </tn-modal> -->
    <kfPopupDengl ref="refpopup3" style="width: 100%;"/>
  </view>
</template>

<script>
import kfPopupDengl from '@/components/kf-popup-dengl/kf-popup-dengl.vue'
import {gotoPage} from "@/utils";
import {bindMobile1} from "@/service";
import {useUserStore} from "@/store";

const userStore = useUserStore()
export default {
  components: {
    kfPopupDengl,
  },
  data() {
    return {
      bainPhone: false,
      form: {
        phone: '',
        password: '',
        rpassword: ''
      },
      userList: [],
    };
  },
  onLoad(options) {
    let self = this;
    userStore.setToken(options.token);
    userStore.fetchUserCenterConfig()
    // 获取登录前页面
    let url = '/' + uni.getStorageSync('currentPage');
    let pageOptions = uni.getStorageSync('currentPageOptions');
    if (Object.keys(pageOptions).length > 0) {
      url += '?';
      for (let i in pageOptions) {
        url += i + '=' + pageOptions[i] + '&';
      }
      url = url.substring(0, url.length - 1);
    }
    // 执行回调函数
    gotoPage(url, 'redirect');
  },
  methods: {
    async bainMobile() {
      let self = this;
      if (this.form.phone !== '') {
        // 绑定手机号
        try {
          const {data} = await bindMobile1({
            phone: this.form.phone,
            password: this.form.password,
            rpassword: this.form.rpassword,
            type: 'zfb',
          })
          console.log("data", data)
          if (data == '绑定成功') {
            self.$refs.refpopup3.close()
            this.bainPhone = false;
            userStore.clearToken(options.token);
            // uni.removeStorageSync('token');
            // uni.removeStorageSync('userId');
            // uni.removeStorageSync('mobile');
            // 获取登录前页面
            let url = '/' + uni.getStorageSync('currentPage');
            let pageOptions = uni.getStorageSync('currentPageOptions');
            if (Object.keys(pageOptions).length > 0) {
              url += '?';
              for (let i in pageOptions) {
                url += i + '=' + pageOptions[i] + '&';
              }
              url = url.substring(0, url.length - 1);
            }
            // 执行回调函数
            gotoPage(url, 'reLaunch');
          }
        } catch (error) {
          console.log(error);
        } finally {
          uni.hideLoading();
        }
      }
    },
  }
}
</script>

<style>

</style>

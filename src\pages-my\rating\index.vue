<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#FF7D26 size-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      :show-loading-more-no-more-view="false"
      :default-page-size="10"
      fixed
      @query="queryList"
    >
      <template #top>
        <SimpleNavBar custom-class="!bg-#FF7D26 white-title" :bordered="false" title="排行榜" />
        <wd-tabs custom-class="!bg-[unset]" @change="handleTabChange" auto-line-width>
          <block v-for="item in tabs" :key="item.key">
            <wd-tab :title="`${item.label}`" :name="item.key"></wd-tab>
          </block>
        </wd-tabs>
      </template>
      <div class="grid grid-cols-1 gap-y-10px p20px box-border">
        <div class="grid grid-cols-[auto_1fr_auto] text-26rpx text-#ffffff p10px box-border">
          <div class="w100rpx">排名</div>
          <div class="pl-20px box-border">昵称</div>
          <div>拉新人数</div>
        </div>
        <div
          v-for="(item, index) in dataList"
          :key="item?.userId"
          :class="[
            'flex items-center text-26rpx text-#ffffff rd-10px p10px box-border',
            index === 0
              ? 'bg-#FFD700'
              : index === 1
                ? 'bg-#C0C0C0'
                : index === 2
                  ? 'bg-#CD7F32'
                  : 'bg-blue',
          ]"
        >
          <div class="w100rpx text-center">
            <div v-if="index === 0" class="i-hugeicons-crown text-40rpx" />
            <div v-else-if="index === 1" class="i-hugeicons-crown text-40rpx" />
            <div v-else-if="index === 2" class="i-hugeicons-crown text-40rpx" />
            <div v-else>{{ index + 1 }}</div>
          </div>

          <div class="pl-20px box-border flex items-center gap-x-10px flex-1">
            <wd-img width="80rpx" height="80rpx" radius="50%" :src="item?.avatarUrl"></wd-img>
            <span>{{ item?.nickName }}</span>
          </div>
          <div class="text-36rpx">{{ item?.inviteCount }}</div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { fetchTodayInviteRanking } from '@/service'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
dayjs.extend(isoWeek)

const tabs = ref([
  { label: '日榜', key: 'day' },
  { label: '周榜', key: 'week' },
  { label: '月榜', key: 'month' },
])

const paging = ref()
const dataList = ref<Api.User.InviteItem[]>([])
const currentRankType = ref<'day' | 'week' | 'month'>('day')

const getDateRangeByType = (type: 'day' | 'week' | 'month') => {
  const now = dayjs()
  if (type === 'day') {
    return {
      startDate: now.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      endDate: now.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
    }
  } else if (type === 'week') {
    const start = now.startOf('week').add(1, 'day')
    const end = now.endOf('week').add(1, 'day')
    return {
      startDate: start.format('YYYY-MM-DD HH:mm:ss'),
      endDate: end.format('YYYY-MM-DD HH:mm:ss'),
    }
  } else {
    return {
      startDate: now.startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      endDate: now.endOf('month').format('YYYY-MM-DD HH:mm:ss'),
    }
  }
}

const queryList = async (_: number, pageSize: number) => {
  try {
    const { startDate, endDate } = getDateRangeByType(currentRankType.value)

    const { data } = await fetchTodayInviteRanking({
      limit: pageSize,
      startDate,
      endDate,
    })

    dataList.value = data || []
    paging.value.complete(data)
  } catch (err) {
    paging.value.complete(false)
  }
}

const handleTabChange = ({ name }) => {
  currentRankType.value = name
  nextTick(() => {
    paging.value?.reload()
  })
}
</script>

<style lang="scss" scoped>
:deep(.white-title .wd-navbar__title) {
  color: #ffffff;
}
:deep(.white-title .wd-navbar__arrow) {
  color: #ffffff;
}

:deep(.wd-tabs__nav) {
  background: unset !important;
}
:deep(.wd-tabs) {
  background: unset !important;
}
:deep(.wd-tabs__line) {
  background: #ffffff !important;
}
:deep(.wd-tabs__nav-item) {
  color: #ffffff !important;
}
</style>

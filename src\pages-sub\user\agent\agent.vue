<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的推广',
  },
}
</route>
<template>
  <!-- navigationStyle: 'custom', -->
  <view class="h-full">
    <!--头部图片-->
    <view v-if="isAgent" class="banner">
      <image :src="top_background" mode="aspectFill"></image>
    </view>
    <view v-if="!isAgent" class="banner">
      <image :src="apply" mode="aspectFill"></image>
    </view>
    <!--不是分销商-->
    <template v-if="!isAgent">
      <view class="flex flex-col justify-center items-center gap30rpx py50rpx text-#999999 agent-w">
        <text style="font-size: 30rpx; margin-bottom: 50rpx">{{ info_words.index.notAgent }}</text>
        <!-- <button class="rounded-30rpx text-white bg-#ff5704 fw-bold sub-btn w-full" >
          
        </button> -->
        <wd-button
          type="primary"
          custom-class="!mx-26rpx w-full"
          size="large"
          @click="applyagent"
          block
        >
          {{ info_words.index.applyNow }}
        </wd-button>
      </view>
    </template>
    <!--是分销商-->
    <template v-if="isAgent">
      <view class="info-top flex items-center">
        <view class="info-ava">
          <image class="info-avatar" :src="user.avatarUrl" mode="aspectFill"></image>
          <view class="info-grade" v-if="agent.gradeId > 0">{{ agent.realName }}</view>
        </view>
        <view class="flex-1 gap40rpx">
          <view class="text-30rpx">{{ user.nickName }}</view>
          <view class="text-22rpx">
            {{ info_words.index.referee || '' }}:{{
              agent.refereeNickname ? agent.refereeNickname : '平台'
            }}
          </view>
        </view>
      </view>
      <!--金额信息-->
      <view class="agent-wrap">
        <view class="flex mb-20rpx">
          <view
            @click="gotoPage('/pages-my/promotion/distribution/index')"
            class="flex-1 flex flex-col items-center gap20rpx"
          >
            <text class="text-36rpx fb">{{ agent.money }}</text>
            <view class="text-24rpx">{{ info_words.index.money }}</view>
          </view>
          <view class="flex-1 flex flex-col items-center gap20rpx">
            <text class="text-36rpx">{{ agent?.freezeMoney }}</text>
            <view class="text-24rpx">{{ info_words.index.freezeMoney }}</view>
          </view>
          <view class="flex-1 flex flex-col items-center gap20rpx">
            <text class="text-36rpx">{{ agent?.totalMoney }}</text>
            <view class="text-24rpx">{{ info_words.index.totalMoney }}</view>
          </view>
        </view>
        <view class="">
          <button
            type="primary"
            class="flex-1 rounded-30rpx text-white bg-#ff5704"
            @click="gotoCash"
          >
            {{ info_words.index.cash }}
          </button>
        </view>
      </view>
      <!--图标入口-->
      <view class="agent-wrap bg-white flex items-center">
        <view
          class="flex-1 flex flex-col items-center gap20rpx"
          @click="gotoPage('/pages-my/promotion/withdrawal/index')"
        >
          <!-- <image
            class="agent_index_img"
            src="../../../static/icon/icon-zijinmingxi.png"
            mode=""
          ></image> -->

          <text class="text-26rpx">{{ info_words.cashList.title }}</text>
        </view>
        <view
          class="flex-1 flex flex-col items-center gap20rpx"
          @click="gotoPage('/pages-my/promotion/distribution/index')"
        >
          <!-- <image
            class="agent_index_img"
            src="../../../static/icon/icon-fenxiaodingdan.png"
            mode=""
          ></image> -->
          <text class="text-26rpx">{{ info_words.order.title }}</text>
        </view>
        <view
          class="flex-1 flex flex-col items-center gap20rpx"
          @click="gotoPage('/pages-my/promotion/my-team/index')"
        >
          <!-- <image
            class="agent_index_img"
            src="../../../static/icon/icon-tuandui.png"
            mode=""
          ></image> -->

          <text class="text-26rpx">{{ info_words.team.title }}</text>
        </view>
        <view
          class="flex-1 flex flex-col items-center gap20rpx"
          @click="gotoPage('/pages-sub/user/agent/qrcode')"
        >
          <!-- <image
            class="agent_index_img"
            src="../../../static/icon/icon-erweima.png"
            mode=""
          ></image> -->

          <text class="text-26rpx">{{ info_words.qrcode.title }}</text>
        </view>
      </view>
    </template>
  </view>
</template>

<script lang="ts" setup>
import { userAgent } from '@/service/api/user'
import { useUserStore } from '@/store'
import { ref } from 'vue'

const userStore = useUserStore()
const isAgent = ref(false)
const apply = ref('')
const top_background = ref('')

const agent = ref({
  freezeMoney: '0',
  money: '0',
  totalMoney: '0',
  refereeNickname: '',
  gradeId: 0,
  realName: '',
})
let info_words = reactive({
  index: {
    applyNow: '立即加入',
    cash: '去提现',
    freezeMoney: '待提现佣金',
    money: '可提现佣金',
    notAgent: '您还不是分销商，请先提交申请',
    referee: '推荐人',
    title: '分销中心',
    totalMoney: '已提现金额',
  },
  order: {
    all: '全部',
    settled: '已结算',
    title: '分销订单',
    unsettled: '未结算',
  },
  cashApply: {
    capital: '可提现佣金',
    minMoney: '最低提现佣金',
    money: '提现金额',
    moneyPlaceholder: '请输入要提取的金额',
    submit: '提交申请',
    title: '申请提现',
  },
  cashList: {
    all: '全部',
    apply10: '审核中',
    apply20: '审核通过',
    apply30: '驳回',
    apply40: '已打款',
    title: '提现明细',
  },
  team: {
    first: '一级团队',
    second: '二级团队',
    third: '三级团队',
    title: '我的团队',
    totalTeam: '团队总人数',
  },
  qrcode: {
    title: '推广二维码',
  },
})
const user = computed(() => userStore.userInfo)
const handleApply = async () => {
  const { data } = await userAgent()
  info_words = data.words
  isAgent.value = data.isAgent
  apply.value = data.apply
  agent.value = data.agent
  top_background.value = data.background
  console.log(data)
}

onShow(() => {
  handleApply()
})

const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
/*申请分销商*/
const applyagent = () => {
  gotoPage('/pages-sub/user/agent/apply')
}

/*去商城逛逛*/
const gotoShop = () => {
  gotoPage('/pages/index/index')
}

/*去提现*/
const gotoCash = () => {
  gotoPage('/pages-my/promotion/apply/index')
}
</script>

<style lang="scss" scoped>
//
.banner {
  width: 750rpx;
  height: 348rpx;
  /* padding-bottom: 60rpx; */
  background-repeat: no-repeat;
  background-size: 100%;
}

.banner image {
  width: 100%;
}
page {
  background-color: #f9f9f9;
}

.index-agent .banner {
  position: absolute;
  width: 750rpx;
  height: 348rpx;
  z-index: 0;
  /* padding-bottom: 60rpx; */
  background-repeat: no-repeat;
  background-size: 100%;
}

.index-agent .banner image {
  width: 100%;
}

.no-agent-image {
  width: 532rpx;
  height: 340rpx;
  padding-top: 20rpx;
  margin: 0 auto;
  display: block;
}

.agent-wrap {
  background: #ffffff;
  background-size: 100% 100%;
  padding: 31rpx 25rpx 36rpx 25rpx;
  box-shadow: 0px 8rpx 3rpx 0px rgba(6, 0, 1, 0.03);
  border-radius: 20rpx;
}

.index-agent .agent-wrap .iconfont {
  font-size: 60rpx;
}

.index-agent .btn-gcred {
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  background: #ff5649;
  border-color: #ff5649;
}

.reg180 {
  padding-right: 20rpx;
  text-align: right;
  transform: rotateY(180deg);
  position: absolute;
  bottom: 0;
}

.icon-jiantou {
  color: #ffffff;
  font-size: 30rpx;
}

.head_top {
  position: absolute;
  width: 100%;
  height: 30px;
  line-height: 30px;
  color: #ffffff;
  font-size: 32rpx;
  z-index: 2;
}

.top_dash {
  padding-bottom: 9px;
}

.agent_index_img {
  width: 78rpx;
  height: 78rpx;
}

.info-top {
  padding: 74rpx 0 63rpx 44rpx;
}

.info-ava {
  width: 108rpx;
  height: 108rpx;
  border-radius: 50%;
  position: relative;
  margin-right: 30rpx;
}

.info-avatar {
  width: 108rpx;
  height: 108rpx;
  border-radius: 50%;
}

.info-grade {
  min-width: 114rpx;
  height: 30rpx;
  line-height: 30rpx;
  padding: 0 22rpx;
  box-sizing: border-box;
  font-size: 18rpx;
  color: #ffffff;
  background: #ffc519;
  box-shadow: 0px 3rpx 7rpx 0px rgba(0, 0, 0, 0.15);
  border-radius: 15rpx;
  text-align: center;
  white-space: nowrap;
  // position: absolute;
  // left: 0;
  // right: 0;
  bottom: -16rpx;
  z-index: 2;
}
.agent-w {
  width: calc(100% - 80rpx);
  margin-top: 50rpx;
  padding: 40rpx;
}
</style>

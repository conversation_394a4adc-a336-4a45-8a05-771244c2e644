<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full flex flex-col">
    <SimpleNavBar title="签到" />
    <div
      :style="{ paddingBottom: `${safeAreaInsets.bottom || 15}px` }"
      class="flex-1 bg-[linear-gradient(45deg,_#EFEAFA_0%,_#F4D6E5_13%,_#E6F2FE_100%)] w-full grid grid-cols-1 grid-auto-rows-min gap-y-10px px24rpx py42rpx box-border"
    >
      <!-- 善豆信息 -->
      <div class="flex items-center gap-x-30px relative">
        <div class="flex flex-col gap-y-10px text-#333333">
          <span class="text-26rpx">我的善豆</span>
          <span class="text-62rpx font-bold">{{ signDetail?.userPoint ?? 0 }}</span>
        </div>
        <div class="flex flex-col gap-y-10px text-#333333">
          <span class="text-26rpx">已累计签到</span>
          <span class="text-62rpx font-bold">
            {{ signDetail?.days ?? 0 }}
            <span class="text-26rpx font-normal">天</span>
          </span>
        </div>
        <div class="sign-top absolute top-10rpx right-24rpx"></div>
      </div>
      <!-- 签到打卡 -->
      <div
        class="w-full px-24rpx py-36rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-30rpx bg-#ffffff rd-25rpx z-1"
      >
        <div class="flex items-center justify-between">
          <span class="text-32rpx text-#333333 font-bold">签到打卡</span>
          <!-- <div class="flex items-center text-24rpx text-#999999 gap-x-3px">
            日历
            <div class="i-carbon-chevron-right"></div>
          </div> -->
        </div>

        <div class="grid grid-cols-7 gap-x-10px grid-auto-rows-min">
          <div
            v-for="item in signDetail?.week"
            :key="item?.date"
            class="flex flex-col items-center gap-y-14rpx"
          >
            <div
              class="w-full h108rpx flex flex-col items-center justify-center gap-y-5px rd-15rpx"
              :class="`${item?.isSign ? 'bg-#F3F4F9' : 'bg-#FDF6E3'}`"
            >
              <span class="text-22rpx text-#333333">+{{ item?.rank }}</span>
              <wd-img
                v-if="item.isSign"
                width="46rpx"
                height="46rpx"
                src="https://file.shanqianqi.com/image/2025/06/24/de68f4dcb2de42bab6b9d32a30b5b9b7.png"
                mode="aspectFit"
              ></wd-img>
              <wd-img
                v-else
                width="46rpx"
                height="46rpx"
                src="https://file.shanqianqi.com/image/2025/06/24/07c81566b3544903b79db58f744be818.png"
                mode="aspectFit"
              ></wd-img>
            </div>
            <span class="text-22rpx text-#333333">{{ item?.date }}</span>
          </div>
        </div>

        <div class="text-28rpx text-#333333">已连续签到{{ signDetail?.continuousDays }}天</div>

        <wd-button
          :disabled="Boolean(signDetail?.isSign)"
          @click="handleSignIn"
          :custom-class="`!w-392rpx !text-30rpx !font-bold !h-82rpx !m-auto  ${signDetail?.isSign ? '!bg-#EEEFF3 !text-#A2AABA' : ''}`"
        >
          {{ signDetail?.isSign ? '已签到' : '立即签到' }}
        </wd-button>
      </div>
      <!-- 我的签到 -->
      <div
        class="w-full px-24rpx py-36rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-30rpx bg-#ffffff rd-25rpx z-1"
      >
        <div class="flex items-center justify-between">
          <span class="text-32rpx text-#333333 font-bold">我的签到</span>
          <!-- <div class="flex items-center text-24rpx text-#999999 gap-x-3px">
            活动规则
            <div class="i-carbon-chevron-right"></div>
          </div> -->
        </div>

        <div class="grid grid-cols-3 gap-x-20rpx gap-y-20rpx">
          <div
            class="w-full h142rpx px30rpx py38rpx box-border rd-25rpx relative flex flex-col justify-between text-#333333 bg-#FFF7E2 of-hidden gap-y-5px"
          >
            <span class="text-28rpx">善豆:{{ signDetail?.points }}</span>
            <span class="text-24rpx text-#999999">累计获得善豆</span>
            <div
              class="bg-[linear-gradient(0deg,_#FA9A46_0%,_rgba(255,255,255,0)_100%)] absolute right--18rpx bottom--31rpx z1 w128rpx h128rpx opacity-15 rd-1/2"
            ></div>
          </div>
          <!-- <div
            class="w-full h142rpx px30rpx py38rpx box-border rd-25rpx relative flex flex-col justify-between text-#333333 bg-#FDEDEE of-hidden gap-y-5px"
          >
            <span class="text-28rpx">优惠券:{{ signDetail?.couponNum }}</span>
            <span class="text-24rpx text-#999999">累计优惠券</span>
            <div
              class="bg-[linear-gradient(0deg,_#FDB9CA_0%,_rgba(255,255,255,0)_100%)] absolute right--18rpx bottom--31rpx z1 w128rpx h128rpx opacity-15 rd-1/2"
            ></div>
          </div> -->
          <div
            class="w-full h142rpx px30rpx py38rpx box-border rd-25rpx relative flex flex-col justify-between text-#333333 bg-#F5F8FE of-hidden gap-y-5px"
          >
            <span class="text-28rpx">次数:{{ signDetail?.continuousDays }}</span>
            <span class="text-24rpx text-#999999">连续签到次数</span>
            <div
              class="bg-[linear-gradient(0deg,_#E6C4EC_0%,_rgba(255,255,255,0)_100%)] absolute right--18rpx bottom--31rpx z1 w128rpx h128rpx opacity-15 rd-1/2"
            ></div>
          </div>
          <div
            class="w-full h142rpx px30rpx py38rpx box-border rd-25rpx relative flex flex-col justify-between text-#333333 bg-#F2FAFE of-hidden gap-y-5px"
          >
            <span class="text-28rpx">次数:{{ signDetail?.days }}</span>
            <span class="text-24rpx text-#999999">累计签到次数</span>
            <div
              class="bg-[linear-gradient(0deg,_#2989CC_0%,_rgba(255,255,255,0)_100%)] absolute right--18rpx bottom--31rpx z1 w128rpx h128rpx opacity-15 rd-1/2"
            ></div>
          </div>
        </div>
      </div>
      <!-- 签到记录 -->
      <div
        class="w-full px-24rpx py-36rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-30rpx bg-#ffffff rd-25rpx z-1"
      >
        <div class="flex items-center justify-between">
          <span class="text-32rpx text-#333333 font-bold">签到记录</span>
          <!-- <div class="flex items-center text-24rpx text-#999999 gap-x-3px">
            更多
            <div class="i-carbon-chevron-right"></div>
          </div> -->
        </div>

        <div class="grid grid-cols-1 gap-y-15px grid-auto-rows-min">
          <div
            v-for="item in signDetail?.signList"
            :key="item?.createTime"
            class="flex items-start justify-between"
          >
            <div class="flex flex-col gap-y-10px">
              <span class="text-28rpx text-#333333">签到奖励</span>
              <div class="flex items-center gap-x-3px">
                <wd-img
                  width="29rpx"
                  height="29rpx"
                  mode="aspectFit"
                  src="https://file.shanqianqi.com/image/2025/06/24/07c81566b3544903b79db58f744be818.png"
                ></wd-img>
                <span class="text-24rpx">{{ item?.points }}善豆</span>
              </div>
            </div>
            <span class="text-24rpx text-#999999">{{ item?.createTime }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 签到成功 -->
    <wd-overlay :show="showSuccess" @click="showSuccess = false">
      <view class="size-full flex items-center justify-center">
        <view
          class="w572rpx bg-#ffffff rd-35rpx shadow-light px-30rpx pt100rpx pb30rpx z-2 relative"
        >
          <div class="flex items-center flex-col justify-center gap-y-30rpx">
            <span class="text-36rpx text-#FC674C font-bold">恭喜您，签到成功获得</span>
            <div class="flex items-center gap-x-3px">
              <wd-img
                width="29rpx"
                height="29rpx"
                mode="aspectFit"
                src="https://file.shanqianqi.com/image/2025/06/24/07c81566b3544903b79db58f744be818.png"
              ></wd-img>
              <span class="text-24rpx">{{ signSuccessData?.points }}善豆</span>
            </div>
            <wd-button @click="showSuccess = false" custom-class="!w-256rpx !h66rpx">
              知道了
            </wd-button>
          </div>
          <div class="w-full absolute top--232rpx left-0">
            <wd-img
              width="100%"
              height="294rpx"
              mode="aspectFit"
              src="https://file.shanqianqi.com/image/2025/06/24/aeef03e164b142c6b38945836f0d159d.png"
            ></wd-img>
          </div>
        </view>
      </view>
    </wd-overlay>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { onSignIn, signIn } from '@/service'
import { useUserStore } from '@/store'

const userStore = useUserStore()

const { safeAreaInsets } = uni.getSystemInfoSync()

onMounted(() => {
  fetchData()
})

const signDetail = ref<Api.User.SignData>()

const fetchData = async () => {
  try {
    const { data } = await signIn({ token: userStore?.token, appId: import.meta.env.VITE_APPID })

    signDetail.value = data
  } catch (error) {}
}

const signSuccessData = ref<{ coupon: unknown[]; couponNum: number; points: number }>()

const showSuccess = ref(false)

let signInTimeout: ReturnType<typeof setTimeout> | null = null

const handleSignIn = () => {
  if (signInTimeout) clearTimeout(signInTimeout)

  signInTimeout = setTimeout(async () => {
    try {
      const { data } = await onSignIn({
        token: userStore?.token,
        appId: import.meta.env.VITE_APPID,
      })
      signSuccessData.value = data
      showSuccess.value = true
      fetchData()
    } catch (error) {}
  }, 300)
}
</script>

<style lang="scss" scoped>
//
</style>

after_sale
<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '商户售后',
  },
}
</route>

<template>
  <view class="flex overflow-hidden pb-3 box-border h-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="10"
      :fixed="false"
      class="flex-1"
      @query="queryList"
    >
      <template #top>
        <div class="tabs">
          <view :class="tab === -1 ? 'activeTab tab' : 'tab'" @click="switchTab(-1)">
            <text>全部</text>
          </view>
          <view :class="tab === 0 ? 'activeTab tab' : 'tab'" @click="switchTab(0)">
            <text>售后</text>
          </view>
        </div>
      </template>
      <!-- 列表 -->
      <view class="content">
        <view
          class="w-full bg-white p30rpx mb20rpx box-border"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <view class="flex justify-between text-26rpx">
            <text>{{ item.createTime }}</text>
            <text class="text-red">{{ item.stateText }}</text>
          </view>
          <view class="pt20rpx flex justify-start items-center gap31rpx">
            <view class="w160rpx h160rpx">
              <image :src="item.orderProduct.productImage" mode="aspectFit"></image>
            </view>
            <view class="flex-1 pr20rpx">
              <view class="text-26rpx">{{ item.orderProduct.productName }}</view>
              <view class="pt10rpx">
                <text class="text-24rpx">{{ item.orderProduct.productAttr }}</text>
              </view>
            </view>
          </view>
          <view class="pt20rpx flex justify-end items-center text-24rpx">
            <view>
              商品金额：
              <text class="text-red">¥{{ item.orderProduct.linePrice }}</text>
            </view>
          </view>
          <view class="pt10rpx flex justify-end items-center text-24rpx">
            <view>
              订单实付金额：
              <text class="text-red">¥{{ item.orderProduct.totalPayPrice || 0 }}</text>
            </view>
          </view>

          <view
            style="border-top: 1rpx solid #eeeeee"
            class="mt20rpx pt20rpx flex justify-end items-center gap20rpx"
          >
            <button
              class="m0 text-28rpx"
              @click="gotoRefundDetail(item.orderRefundId, item.orderId)"
            >
              查看详情
            </button>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { fetchUserStoreRefundOrderList, refundPlateApply } from '@/service'
import { computed, onMounted, ref } from 'vue'

// 响应式状态
const tab = ref<number>(-1)
let dataList = ref([])
let shopSupplierId = ref('')

// 生命周期 - 挂载完成
onMounted(() => {})
onLoad((e) => {
  shopSupplierId.value = e.shopSupplierId
})

// 生命周期 - 页面触底
onReachBottom(() => {})

const paging = ref()
const queryList = async (pageNo: number, pageSize: number) => {
  uni.showLoading({ title: '加载中' })
  try {
    let { data } = await fetchUserStoreRefundOrderList({
      pageIndex: pageNo,
      pageSize: pageSize,
      shopSupplierId: shopSupplierId.value,
      state: tab.value,
      appId: import.meta.env.VITE_APPID,
      type: 0,
    })

    uni.hideLoading()

    paging.value.complete(data.records)
  } catch (err) {
    uni.hideLoading()
    paging.value.complete(false)
  }
}
const switchTab = (index: number) => {
  console.log('tab1', tab.value)

  tab.value = index
  paging.value?.reload()
}

// 售后详情
const gotoRefundDetail = (orderRefundId: any, orderId: any) => {
  uni.navigateTo({
    url: `/pages-my/my-shop/after_sale/after_sale_detail?order_refund_id=${orderRefundId}&orderId=${orderId} `,
  })
}
</script>

<style lang="scss" scoped>
.tabs {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
}
.tab {
  box-sizing: border-box;
  padding: 0 34rpx;
  display: flex;
  height: 76rpx;
  justify-content: center;
  align-items: center;
  text {
    font-size: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 10rpx;
  }
}
.activeTab {
  color: #333;
  text {
    display: flex;
    border-bottom: 2px solid #ff7d26;
    font-weight: bold;
  }
}
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.loading-img {
  display: flex;
  margin-bottom: 10rpx;
}

.load1,
.load2,
.load3 {
  display: flex;
  margin: 0 5rpx;
  animation: loading 1.2s infinite ease-in-out;
}

.load1 view,
.load2 view,
.load3 view {
  width: 8rpx;
  height: 30rpx;
  margin: 0 2rpx;
  border-radius: 4rpx;
}

.load1 {
  animation-delay: -0.24s;
}
.load2 {
  animation-delay: -0.12s;
}

@keyframes loading {
  0%,
  100% {
    height: 30rpx;
  }
  50% {
    height: 15rpx;
  }
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}
</style>

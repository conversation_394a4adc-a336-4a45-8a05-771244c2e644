<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的钱包',
    navigationBarBackgroundColor: '#ff774d',
    navigationBarTextStyle: 'white',
  },
}
</route>

<template>
  <view class="index">
    <view class="top_bg">
      <!-- <view
      class="top_bg"
      :style="'height:' + (368 + topBarHeight * 2 + topBarTop * 2) + 'rpx;'"
    > -->
      <!-- #ifdef MP-WEIXIN || APP-PLUS -->
      <!-- <view class="ww100" :style="'height:' + topBarTop + 'px;'"></view>
      <view
        class="tc head_top"
        :style="topBarHeight === 0 ? '' : 'height:' + topBarHeight + 'px;'"
      >
        <view class="reg180" @click="goback">
          <text class="icon iconfont icon-jiantou"></text>
        </view>
        <view class="fb">我的钱包</view>
      </view> -->
      <!-- #endif -->
      <view class="card-top flex items-center">
        <view class="flex-1">
          <view class="text-28rpx text-white fb white">账户余额(元)</view>
          <view class="text-white gap-20rpx">
            <text class="text-38rpx">￥</text>
            <text class="text-72rpx">{{ balance }}</text>
          </view>
        </view>
        <view v-if="cashOpen">
          <view class="text-btn mb10" @click="gotoPage('/pages/user/cash/apply')">去提现</view>
          <view class="white underline f28" @click="gotoPage('/pages/user/cash/list')">
            提现明细
          </view>
        </view>
      </view>
    </view>

    <view class="wallet-content">
      <view class="index-head" v-if="balanceOpen">
        <view class="card-bottom">
          <view class="width-150 font-30 pr flex-1" @click="gotoPay">
            <view>
              <image
                class="wallet_img"
                src="https://file.shanqianqi.com/image/2025/06/24/32aaf92b38b74f51b57001d9017d1c6d.png"
                mode="aspectFit"
              ></image>
            </view>
            <view class="f26 gray6">充值</view>
          </view>
          <view class="none_line"></view>
          <view class="width-150 font-30 pr flex-1" @click="gotoList('rechange')">
            <view>
              <image
                class="wallet_img"
                src="https://file.shanqianqi.com/image/2025/06/24/1d03cafd97034f0895bf857de644d227.png"
                mode="aspectFit"
              ></image>
            </view>
            <view class="f26 gray6">充值记录</view>
          </view>
        </view>
      </view>

      <view class="index-body">
        <view class="body-head">
          <view class="name">钱包明细</view>
          <view class="more-btn" @click="gotoList('all')">
            更多明细
            <text class="i-carbon-chevron-right" style="color: #666; font-size: 22rpx"></text>
          </view>
        </view>
        <view style="padding: 0 27rpx">
          <view class="body-item" v-for="(item, index) in dataList" :key="item.id || index">
            <view class="body-item-top">
              <view class="body-item-top-left f32">{{ item.sceneText }}</view>
              <view class="body-item-top-right f36 !text-red" v-if="Number(item.money) > 0">
                +{{ item.money }}
              </view>
              <view class="body-item-top-right f36 !text-#333333" v-else>{{ item.money }}</view>
            </view>
            <view class="body-item-bottom">
              <view class="body-item-bottom-left font-24 gray9">
                {{ item.createTime }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { fetchUserBalanceLogIndex } from '@/service'
import { useUserStore } from '@/store'
import { onMounted, ref } from 'vue'

const userStore = useUserStore()
// 响应式状态
const dataList = ref<WalletItem[]>([])
const balance = ref<string>('0.00')
const balanceOpen = ref<boolean>(false)
const cashOpen = ref<boolean>(false)
const topBarHeight = ref<number>(0)
const topBarTop = ref<number>(0)

// 获取顶部栏高度（示例逻辑，实际根据平台调整）
onMounted(() => {
  // #ifdef MP-WEIXIN || APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  topBarHeight.value = systemInfo.statusBarHeight
  topBarTop.value = systemInfo.statusBarHeight
  // #endif
  fetchWalletData()
})

// 获取钱包明细数据
const fetchWalletData = async () => {
  const { data } = await fetchUserBalanceLogIndex({
    appId: import.meta.env.VITE_APPID,
    // pageIndex: 1,
    // pageSize: 10,
  })
  dataList.value = data.list
  balance.value = data.balance
  balanceOpen.value = data.balanceOpen
  cashOpen.value = data.cashOpen
}

// 跳转明细列表
const gotoList = (type: 'all' | 'rechange') => {
  uni.navigateTo({
    url: `/pages-sub/user/myBalance/index?type=${type}`,
  })
}

// 返回上一页
const goback = () => {
  uni.navigateBack()
}

// 跳转充值页面
const gotoPay = () => {
  uni.navigateTo({ url: '/pages-sub/user/recharge/index' })
}

// 通用跳转方法（保持原逻辑）
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}

// 类型定义（实际项目建议单独文件）
interface WalletItem {
  id?: string
  sceneText: string
  money: string | number
  createTime: string
}
</script>

<style lang="scss" scoped>
.font-color-ccc {
  color: #cccccc;
}

.icon-jiantou::before {
  font-size: 24rpx;
}

.font-24 {
  font-size: 24rpx;
}

.font-28 {
  font-size: 28rpx;
}

.font-32 {
  font-size: 32rpx;
}

.font-72 {
  font-size: 72rpx;
}

.width-150 {
  width: 150rpx;
  text-align: center;
}

.index {
  width: 750rpx;
}
.wallet-content {
  margin-top: -93rpx;
}
.index-head {
  width: 710rpx;
  margin: 0 auto;
  height: 160rpx;
  background: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 25rpx;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.08);
}

.bg-image {
  width: 660rpx;
  height: 340rpx;
  background-image: url('../../../static/card.png');
  background-size: 100% 100%;
  margin: 0 auto;
  margin-top: 50rpx;
  display: flex;
  flex-direction: column;
}

.card-top {
  width: 750rpx;
  height: 256rpx;
  padding-left: 47rpx;
  padding-right: 22rpx;
  box-sizing: border-box;
}

.card-bottom {
  /* width: 660rpx; */
  height: 160rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.index-body {
  width: 706rpx;
  /* height: 1036rpx; */
  background: #ffffff;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 15rpx;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
}

.body-head {
  height: 110rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px #f2f2f2 solid;
  padding: 0 27rpx;
}
.body-head .name {
  font-size: 30rpx;
  font-weight: bold;
}
.body-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 126rpx;
  border-bottom: 1rpx #f2f2f2 solid;
}

.body-item-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  margin-bottom: 10rpx;
}

.body-item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reg180 {
  padding-right: 20rpx;
  text-align: right;
  transform: rotateY(180deg);
  position: absolute;
  bottom: 0;
}

.icon-jiantou {
  color: #ffffff;
  font-size: 30rpx;
}

.head_top {
  position: relative;
  height: 30px;
  line-height: 30px;
  color: #ffffff;
  font-size: 32rpx;
}

.bg_topimg {
  position: absolute;
  top: 0;
  width: 100%;
  height: 400rpx;
  z-index: -1;
}

.top_bg {
  width: 750rpx;
  height: 368rpx;
  background: linear-gradient(180deg, #ff774d 0%, #ff422e 100%);
}

.wallet_img {
  width: 72rpx;
  height: 72rpx;
  margin: 0 auto;
}

.index-head .card-bottom .pr .icon-jiantou {
  position: absolute;
  right: 95rpx;
  color: #999999;
  font-size: 26rpx;
  top: 24rpx;
}

.none_line {
  width: 1rpx;
  height: 80rpx;
  background-color: #d9d9d9;
}

.body-item-top-right {
  color: #ff5649;
  margin-bottom: -30rpx;
}
.underline {
  display: inline-block;
  line-height: 2;
  border-bottom: 1px solid #fff;
}
.text-btn {
  width: 189rpx;
  height: 62rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.15);
  border-radius: 30rpx;
  color: #ff5649;
}
.more-btn {
  width: 142rpx;
  height: 52rpx;
  background: #ffffff;
  opacity: 0.98;
  border-radius: 26rpx;
  font-size: 24rpx;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

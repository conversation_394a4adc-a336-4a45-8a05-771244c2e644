export function useFabOffset() {
  const { windowHeight, windowWidth } = uni.getSystemInfoSync()

  let menuButtonInfo_capsule = { width: 0, height: 0, top: 0, right: 0 }

  try {
    // #ifdef MP-WEIXIN
    menuButtonInfo_capsule = uni.getMenuButtonBoundingClientRect()
    // #endif
  } catch (e) {
    console.log('获取胶囊信息失败:', e)
  }

  const cartIconSize = uni.upx2px(100)
  const badgeSize = 20
  const initialX = windowWidth - cartIconSize - badgeSize - 10
  const cartOffset = ref<[number, number]>([initialX, windowHeight / 1.5])

  let isAdjusting = false

  watch(
    cartOffset,
    (newOffset) => {
      if (isAdjusting) return
      const [x, y] = newOffset
      let adjustedX = x
      let adjustedY = y
      let needsAdjustment = false

      const minX = badgeSize
      const maxX = windowWidth - cartIconSize - badgeSize

      if (x < minX) {
        adjustedX = minX
        needsAdjustment = true
      }

      if (x > maxX) {
        adjustedX = maxX
        needsAdjustment = true
      }

      // #ifdef MP-WEIXIN
      if (menuButtonInfo_capsule.width > 0) {
        const safeMargin = 50
        const capsuleLeft = windowWidth - menuButtonInfo_capsule.width - safeMargin
        const capsuleTop = 0
        const capsuleBottom =
          menuButtonInfo_capsule.top + menuButtonInfo_capsule.height + safeMargin

        if (y >= capsuleTop && y <= capsuleBottom && x >= capsuleLeft) {
          const leftSafeX = capsuleLeft - cartIconSize - 10
          const bottomSafeY = capsuleBottom + 20

          if (leftSafeX >= badgeSize) {
            adjustedX = leftSafeX
            adjustedY = Math.max(y, capsuleTop + 20)
          } else {
            adjustedX = Math.min(x, capsuleLeft - 20)
            adjustedY = bottomSafeY
          }

          needsAdjustment = true
        }

        if (
          y >= menuButtonInfo_capsule.top - 20 &&
          y <= menuButtonInfo_capsule.top + menuButtonInfo_capsule.height + 20
        ) {
          if (x >= capsuleLeft) {
            adjustedX = capsuleLeft - cartIconSize - 10
            needsAdjustment = true
          }
        }
      }
      // #endif

      if (needsAdjustment) {
        isAdjusting = true
        nextTick(() => {
          cartOffset.value = [adjustedX, adjustedY]
          setTimeout(() => {
            isAdjusting = false
          }, 100)
        })
      }
    },
    { deep: true },
  )

  return {
    cartOffset,
  }
}

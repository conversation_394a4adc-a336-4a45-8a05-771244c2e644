import { userCenterConfig, userPhoneLogin, userSmsLogin, userWxLogin } from '@/service/api/user'
import { defineStore } from 'pinia'
import { useUserSettingStore } from './user-setting'
import { bindMobile } from '@/service/api/user'

const initState: Api.User.UserInfo = null

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<Api.User.UserInfo>(initState)

    let orderCount = ref(null)

    const token = ref('')

    const sessionKey = ref('')

    const setToken = (val: string) => {
      token.value = val
    }
    const clearToken = () => {
      token.value = ''
    }

    const setSessionKey = (val: string) => {
      sessionKey.value = val
    }
    const clearSessionKey = () => {
      sessionKey.value = ''
    }

    const isLogined = computed(() => !!token.value)

    const appId = ref(0)

    const setAppid = (val: number) => {
      appId.value = val
    }

    // 登录
    const wxLogin = async (back: boolean = true, url: string = '/pages/index/index') => {
      try {
        const { code } = await uni.login()

        const { data } = await userWxLogin({
          appId: import.meta.env.VITE_APPID,
          code,
          refereeId: uni.getStorageSync('refereeId'),
          invitationId: uni.getStorageSync('invitationId'),
        })
        token.value = data.token
        await fetchUserCenterConfig()
        back && uni.navigateBack()
      } catch (error) {}
    }

    // 手机登录
    const phoneLogin = async (param) => {
      try {
        const { data } = await userPhoneLogin(param)
        console.log('🚀 ~ phoneLogin ~ data:', data)
        token.value = data.token
        console.log('🚀 ~ phoneLogin ~ token:', token)
        await fetchUserCenterConfig()
        uni.navigateBack()
      } catch (error) {
        console.error(error)
      }
    }

    // 手机登录
    const smsLogin = async (param) => {
      try {
        const { data } = await userSmsLogin(param)
        console.log(data)
        token.value = data.token
        await fetchUserCenterConfig()
        uni.navigateBack()
      } catch (error) {
        console.error(error)
      }
    }

    //手机号
    const userMobile = ref('')

    const setUserMobile = (val: string) => {
      userMobile.value = val
    }

    const clearUserMobile = () => {
      userMobile.value = ''
    }
    // 获取用户信息
    const fetchUserCenterConfig = async () => {
      try {
        const { data } = await userCenterConfig({ appId: import.meta.env.VITE_APPID })
        userInfo.value = data?.user
        if (data?.user?.mobile) setUserMobile(data?.user?.mobile)
        orderCount.value = data?.orderCount
      } catch (error) {
        console.error(error)
      }
    }

    const clearUserInfo = () => {
      userInfo.value = initState
      token.value = ''
      orderCount.value = null
    }

    const logOut = () => {
      const userSettingStore = useUserSettingStore()
      userSettingStore.clearUserSettingInfo()
      clearUserInfo()
      uni.reLaunch({ url: '/pages/index/index' })
    }

    const fetchUserMobile = async (e: {
      cloudID: string
      code: string
      encryptedData: string
      errMsg: string
      iv: string
    }) => {
      await wxLogin(false)
      const { code } = await uni.login()
      await bindMobile({
        userId: userInfo.value?.userId,
        code: code,
        encryptedData: e.encryptedData,
        iv: e.iv,
      })

      await fetchUserCenterConfig()

      // uni.showToast({
      //   title: '绑定成功',
      // })
    }

    const envVersion = ref('')

    const setEnvVersion = (val: string) => {
      envVersion.value = val
    }

    const clearEnvVersion = () => {
      envVersion.value = ''
    }

    return {
      token,
      userInfo,
      orderCount,
      wxLogin,
      phoneLogin,
      smsLogin,
      logOut,
      fetchUserCenterConfig,
      clearUserInfo,
      fetchUserMobile,
      isLogined,
      clearToken,
      setToken,
      appId,
      setAppid,
      sessionKey,
      setSessionKey,
      clearSessionKey,
      envVersion,
      setEnvVersion,
      clearEnvVersion,
      userMobile,
      setUserMobile,
      clearUserMobile,
    }
  },
  {
    persist: true,
  },
)

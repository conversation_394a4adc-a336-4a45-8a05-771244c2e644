<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden pb-2 box-border h-full flex flex-col">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="10"
      :fixed="false"
      class="flex-1"
      @query="queryList"
    >
      <template #top>
        <div class="flex flex-col">
          <HomeSubNavBar
            @update-input-value="handleSearchShopName"
            @update-location="handleLocationChange"
            :voice-search="voiceSearch"
            placeholder="搜索门店"
            left-title="商圈"
          />
          <div class="bg-#FF7D26 px26rpx box-border h-80rpx of-hidden">
            <wd-tabs @change="handleTabChange" custom-class="!h-full" v-model="tab" auto-line-width>
              <block v-for="item in tabs" :key="item.key">
                <wd-tab :title="`${item.label}`" :name="`${item.label}`"></wd-tab>
              </block>
            </wd-tabs>
          </div>
        </div>
      </template>
      <div class="grid grid-cols-1 gap-y-20rpx">
        <div class="px-20rpx py-20rpx grid grid-cols-1 gap-y-20rpx grid-auto-rows-min">
          <!-- 通知 -->
          <!-- <wd-notice-bar
            text="这是一条消息提示信息，这是一条消息提示信息，这是一条消息提示信息"
            prefix="warn-bold"
          /> -->

          <!-- <VoiceInput /> -->

          <!-- 活动 -->
          <div class="w-full rd-8px grid grid-cols-5 box-border">
            <div
              v-for="item in categoryList"
              :key="item?.categoryId"
              @click="() => handleCategoryChange(item?.categoryId)"
              class="w-full aspect-square flex flex-col items-center gap-y-14rpx justify-center"
            >
              <div
                :class="`${activeCategory === item?.categoryId ? 'bg-#FF7D26' : 'bg-transparent'}`"
                class="p-20rpx flex items-center justify-center rd-1/2 transition-all-300"
              >
                <wd-img
                  :src="item?.picPath ?? ''"
                  width="96rpx"
                  height="96rpx"
                  radius="50%"
                  mode="aspectFit"
                ></wd-img>
              </div>

              <span
                :class="`${activeCategory === item?.categoryId ? 'text-#FF7D26' : ''}`"
                class="text-24rpx text-#333333 inline-block transition-all-300"
              >
                {{ item?.name }}
              </span>
            </div>
          </div>

          <!-- 轮播 -->
          <wd-swiper :list="swiperList" autoplay height="90" v-model:current="current"></wd-swiper>

          <!-- 热销 -->
          <div class="rd-8px w-full flex flex-col box-border gap-y-10px">
            <!-- title -->
            <!-- <div class="flex w-full items-center justify-between">
              <div class="flex items-end flex-1 of-hidden gap-x-6px relative h-44rpx">
                <span class="text-32rpx text-#333333 font-bold z-1 flex items-end">
                  善美优选
                  <span class="text-20rpx text-#333333 font-normal">/shanmeiyouxuan</span>
                </span>
                <div
                  class="bg-gradient-to-r from-#FF7D26 to-#FFFFFF w-full h-14rpx absolute bottom-0 left-0 z-0 rd-30rpx"
                ></div>
              </div>
              <div class="flex-1"></div>
            </div> -->
            <!-- 商品 -->
            <div class="grid grid-cols-1 gap-y-10px">
              <div
                v-for="store in dataList"
                @click="
                  () => goSupplierIndex(store?.shopSupplierId, store?.storeId, store?.storeName)
                "
                :key="store?.storeId"
                class="bg-#ffffff shadow-light rd-20rpx p20rpx box-border w-full flex flex-col gap-y-20rpx"
              >
                <div class="flex items-start justify-between">
                  <div class="flex items-center gap-x-20rpx">
                    <wd-img
                      v-if="store?.logoFilePath"
                      width="100rpx"
                      height="100rpx"
                      :src="store?.logoFilePath"
                      mode="aspectFit"
                    ></wd-img>
                    <div v-else class="i-carbon-store text-40rpx text-#FF7D26"></div>
                    <div class="flex flex-col gap-y-10rpx">
                      <span class="text-32rpx text-#333333 font-600 line-clamp-1">
                        {{ store?.supplierName }} ({{ store?.storeName }})
                      </span>
                      <div class="flex items-center gap-x-10px">
                        <wd-rate :modelValue="getScore(store.serverScore, 1)" readonly />
                        <span class="text-24rpx text-#666666">
                          {{ store?.serverScore ?? '' }} 分
                        </span>
                        <!-- <span class="text-24rpx text-#666666">
                          {{ store?.favCount ?? '' }} 人关注
                        </span> -->
                      </div>
                    </div>
                  </div>
                </div>
                <scroll-view :show-scrollbar="false" scroll-x enable-flex class="flex items-center">
                  <div
                    v-for="item in store?.productList"
                    :key="item.productId"
                    @click="() => goGoodsDetail(item.productId)"
                    class="flex flex-col gap-y-10rpx mr-20px"
                  >
                    <wd-img
                      width="200rpx"
                      height="200rpx"
                      :src="item?.productImage"
                      mode="aspectFit"
                      custom-class="!rd-20rpx"
                    ></wd-img>
                    <div class="flex items-baseline gap-x-12rpx text-#FF7D26">
                      <div class="flex items-baseline">
                        <span class="text-20rpx font-500">¥</span>
                        <span class="text-40rpx font-bold">
                          {{ item?.productPrice?.split('.')?.[0] }}
                        </span>
                        <span class="text-24rpx font-bold">
                          .{{ item?.productPrice?.split('.')?.[1] }}
                        </span>
                      </div>
                      <span class="text-20rpx text-#666666 line-through">
                        ¥{{ item?.linePrice }}
                      </span>
                    </div>
                  </div>
                </scroll-view>
              </div>
            </div>
            <!-- <div class="grid grid-cols-2 gap-10px">
              <div
                v-for="item in dataList"
                :key="item.productId"
                @click="() => goGoodsDetail(item.productId)"
                class="flex flex-col bg-#ffffff shadow-[0rpx_4rpx_8rpx_0rpx_rgba(0,0,0,0.02)] rd-20rpx of-hidden box-border"
              >
                <wd-img
                  :src="item?.productImage"
                  width="100%"
                  height="460rpx"
                  mode="aspectFill"
                ></wd-img>
                <div class="w-full p-20rpx box-border grid grid-cols-1">
                  <div class="text-24rpx text-#333333 line-clamp-1 font-600">
                    {{ item?.productName }}
                  </div>
                  <div class="text-20rpx text-#FF7D26 font-500 mt4px mb12rpx">
                    超长续航蓝牙耳机热卖榜
                  </div>
                  <div class="flex items-baseline gap-x-10px text-#FF7D26">
                    <div class="flex items-baseline">
                      <span class="text-20rpx font-500">¥</span>
                      <span class="text-40rpx font-bold">
                        {{ item?.productPrice?.split('.')?.[0] }}
                      </span>
                      <span class="text-24rpx font-bold">
                        .{{ item?.productPrice?.split('.')?.[1] }}
                      </span>
                    </div>
                    <span class="text-20rpx text-#666666">已售2000+</span>
                  </div>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </z-paging>
    <TabBar @update-voice-res="handleVoiceSearch" />
  </view>
</template>

<script lang="ts" setup>
import HomeSubNavBar from '@/components/HomeSubNavBar/index.vue'
import TabBar from '@/components/TabBar/index.vue'
import { fetchAllCityStoreList, fetchShopCategory } from '@/service'
import { useLocationStore } from '@/store/location'
import { useUserStore } from '@/store/user'
import { getScore } from '@/utils'
import { removePunctuation } from '@/utils'

const locationStore = useLocationStore()

const userStore = useUserStore()

defineOptions({
  name: 'District',
})

const tabs = ref([
  { label: '综合', key: 'all' },
  { label: '位置', key: 'distance' },
  { label: '销量', key: 'sales' },
  { label: '价格', key: 'price' },
])
const tab = ref('综合')

const tabIndex = computed(() => tabs.value.findIndex((item) => item.label === tab.value))

const name = ref('')

const categoryList = ref<Api.Home.ShopCategoryItem[]>()

const activeCategory = ref<number>()

const handleCategoryChange = (categoryId: number) => {
  activeCategory.value = categoryId
  paging.value.reload()
}

const paging = ref()
const dataList = ref<Api.Store.StoreListItem[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data: categoryData } = await fetchShopCategory({
      appId: import.meta.env.VITE_APPID,
      token: userStore.token,
    })
    categoryList.value = categoryData?.list ?? []

    const { data } = await fetchAllCityStoreList({
      pageIndex: pageNo,
      sortType: tabs.value[tabIndex.value]?.key,
      pageSize: pageSize,
      appId: import.meta.env.VITE_APPID,
      categoryId: activeCategory.value,
      storeName: name.value,
      lat: locationStore?.locationInfo?.location?.lat,
      lon: locationStore?.locationInfo?.location?.lon,
      ver: '1',
    })

    paging.value.complete(data?.records)
  } catch (err) {
    paging.value.complete(false)
  }
}

const current = ref<number>(0)

const swiperList = ref([])

const handleTabChange = () => {
  paging.value.reload()
}

const handleLocationChange = () => paging?.value?.reload()

const voiceSearch = ref('')

const handleVoiceSearch = (text: string) => {
  voiceSearch.value = removePunctuation(text)
  name.value = removePunctuation(text)
  paging.value?.reload()
}

const handleSearchShopName = (keyword: string) => {
  name.value = keyword
  paging.value?.reload()
}

const goGoodsDetail = (id: number) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}` })
}

const goSupplierIndex = (id: number, storeId: number, storeName: string) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${id}&storeId=${storeId}&storeName=${storeName}`,
  })
}
</script>

<style lang="scss" scoped>
:deep(.wd-tabs__nav-item) {
  background: #ff7d26 !important;
  color: #ffffff !important;
}
:deep(.wd-tabs__line) {
  background: #ffffff !important;
}
</style>

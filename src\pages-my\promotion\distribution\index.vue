<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full grid grid-rows-[1fr] grid-cols-1"
    :style="{
      paddingBottom: `${bottom || 15}px`,
    }"
  >
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="20"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <SimpleNavBar title="收益明细" />
        <wd-tabs @change="handleTabChange" auto-line-width>
          <block v-for="item in showsTab" :key="item.key">
            <wd-tab :title="`${item.label}`" :name="item.key"></wd-tab>
          </block>
        </wd-tabs>
      </template>
      <div class="grid grid-cols-1">
        <div
          v-for="item in dataList"
          :key="item.id"
          class="bg-#ffffff px30rpx py20rpx flex flex-col gap-y-12px box-border border-b border-b-solid border-b-#eeeeee"
        >
          <!-- <div class="flex items-center justify-between text-24rpx">
            <text>订单号{{ item?.orderMaster?.orderNo ?? '' }}</text>
          </div> -->
          <div class="flex items-center gap-x-10px text-24rpx">
            <!-- <wd-img
              width="80rpx"
              height="80rpx"
              radius="50%"
              :src="item?.avatarUrl"
              mode="aspectFill"
            ></wd-img> -->
            <div class="flex-1 text-24rpx flex flex-col gap-y-10px">
              <!-- <view class="flex items-center justify-between">
                <text class="text-#333333">{{ item?.nickName }}</text>
                <text class="text-#f6220c" v-if="item?.firstUserId == userStore?.userInfo?.userId">
                  +￥{{ item?.firstMoney }}
                </text>
                <text class="text-#f6220c" v-if="item?.secondUserId == userStore?.userInfo?.userId">
                  +￥{{ item?.secondMoney }}
                </text>
                <text class="text-#f6220c" v-if="item?.thirdUserId == userStore?.userInfo?.userId">
                  +￥{{ item?.thirdMoney }}
                </text>
              </view> -->
              <view class="flex items-center justify-between">
                <div class="flex items-center flex-1 justify-between">
                  <div class="flex flex-col gap12rpx">
                    <span class="text-#333333 text-28rpx">{{ item?.description ?? '' }}</span>
                    <text class="text-#999999">{{ item?.createTime }}</text>
                  </div>
                  <div class="flex flex-col #333333 text-24rpx gap12rpx">
                    <span v-if="item?.flowType !== 100 && item?.flowType !== 110" class="">
                      {{ item?.flowType === 60 ? `(${item?.nickName})` : `(${item?.storeName})` }}
                    </span>
                    <text class="">￥{{ item?.money }}</text>
                  </div>
                </div>
              </view>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { userOrderList } from '@/service'

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

// 资金流动类型 (10佣金收入 20提现支出 50商务收益 60锁客收益 70省代收益 80市代收益 90区代收益 100高管分红 110晋级奖励)
const identity = ref({
  isCity: false,
  isExecutive: false,
  isProvince: false,
  isRegion: false,
})
const tabs = computed(() => [
  { label: '全部收益', key: '', isShow: true },
  { label: '商务收益', key: 50, isShow: true },
  { label: '锁客收益', key: 60, isShow: true },
  { label: '省代收益', key: 70, isShow: identity.value.isProvince },
  { label: '市代收益', key: 80, isShow: identity.value.isCity },
  { label: '区代收益', key: 90, isShow: identity.value.isRegion },
  { label: '高管分红', key: 100, isShow: identity.value.isExecutive },
  { label: '晋级奖励', key: 110, isShow: true },
])

const showsTab = computed(() => tabs.value.filter((item) => item.isShow))

const status = ref('')

const paging = ref()
const dataList = ref<Api.User.orderCapitalItem[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await userOrderList({
      pageIndex: pageNo,
      pageSize,
      flowType: status.value,
    })
    identity.value.isProvince = data?.identity?.isProvince
    identity.value.isCity = data?.identity?.isCity
    identity.value.isRegion = data?.identity?.isRegion
    identity.value.isExecutive = data?.identity?.isExecutive
    paging.value.complete(data?.page?.records ?? [])
  } catch (err) {
    paging.value.complete(false)
  }
}

const handleTabChange = ({ name }) => {
  status.value = name
  nextTick(() => {
    paging.value?.reload()
  })
}
</script>

<style lang="scss" scoped>
//
</style>

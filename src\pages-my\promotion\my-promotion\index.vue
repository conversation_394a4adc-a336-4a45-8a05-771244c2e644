<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full bg-[rgba(255,255,255,1)]">
    <z-paging ref="paging" v-model="dataList" :default-page-size="20" fixed @query="queryList">
      <template #top>
        <SimpleNavBar title="我的推广" />
        <wd-tabs @change="handleTabChange" auto-line-width>
          <block v-for="item in tabs" :key="item.key">
            <wd-tab :title="`${item.label}`" :name="item.key"></wd-tab>
          </block>
        </wd-tabs>
        <div
          v-if="type === 1"
          class="flex items-center justify-center h80rpx px30rpx box-border text-28rpx text-#666666 gap-x-40rpx"
        >
          <div class="flex items-center text-28rpx font-500">
            总流水：
            <span class="text-#FF7D26">{{ totalNum }}</span>
          </div>
        </div>
        <div
          v-else
          class="flex items-center flex-col px30rpx box-border text-28rpx font-500 text-#666666 gap-y-20rpx"
        >
          <div class="flex justify-center gap-x-40rpx items-center">
            <div class="flex items-center">
              总流水：
              <span class="text-#FF7D26">{{ totalShop }}</span>
            </div>
            <div class="flex items-center">
              总业绩：
              <span class="text-#FF7D26">{{ totalShopSys }}</span>
            </div>
          </div>

          <div v-if="Number(totalShopSys) && Number(totalShop)" class="flex items-center">
            平均抽点：
            <span class="text-#FF7D26">
              {{ (Number(totalShopSys) / Number(totalShop)) * 100 }}%
            </span>
          </div>
        </div>
        <div class="flex items-center justify-center">
          <wd-drop-menu>
            <wd-drop-menu-item @open="handleRateOpen" title="前十排名">
              <div
                v-if="type === 1"
                class="grid grid-cols-1 gap-y-24rpx pl-50rpx pr-26rpx py-22rpx box-border bg-#ffffff"
              >
                <div class="grid text-26rpx font-500 text-#333333 grid-cols-2">
                  <div class="w-full">会员名</div>
                  <div class="w-full">当月流水</div>
                  <!-- <div class="w-full">当月业绩</div> -->
                </div>
                <div
                  v-for="item in rateUserList"
                  :key="item.createTime"
                  class="grid text-24rpx text-#333333 grid-cols-2"
                >
                  <div class="w-full text-#333333">
                    {{ item?.nickName }}
                  </div>
                  <div class="w-full text-#333333">
                    {{ item?.payMoney }}
                  </div>
                  <!-- <div class="w-full text-#999999">10476.48元</div> -->
                </div>
              </div>
              <div
                v-if="type === 2"
                class="grid grid-cols-1 gap-y-24rpx pl-50rpx pr-26rpx py-22rpx box-border bg-#ffffff"
              >
                <div class="grid text-26rpx font-500 text-#333333 grid-cols-4">
                  <div class="w-full">商户名</div>
                  <div class="w-full">当月流水</div>
                  <div class="w-full">当月业绩</div>
                  <div class="w-full">抽点</div>
                </div>
                <div
                  v-for="item in rateShopList"
                  :key="item.createTime"
                  class="grid text-24rpx text-#333333 grid-cols-4"
                >
                  <div class="w-full text-#333333">
                    {{ item?.name }}
                  </div>
                  <div class="w-full text-#333333">
                    {{ item?.salesMoney }}
                  </div>
                  <div class="w-full text-#333333">{{ item?.sysMoney }}</div>
                  <div class="w-full text-#333333">{{ item?.supplierCommissionRate }}%</div>
                </div>
              </div>
            </wd-drop-menu-item>
          </wd-drop-menu>
        </div>
      </template>
      <div class="grid grid-cols-1">
        <div
          v-for="item in dataList"
          :key="
            type === 1
              ? (item as Api.User.MyteamUserItem)?.userId
              : (item as Api.User.MyteamShopItem)?.shopSupplierId
          "
          class="bg-#ffffff px30rpx py20rpx flex items-center gap-y-10px box-border"
        >
          <div class="flex items-start justify-between gap-x-10px w-full">
            <wd-img
              width="80rpx"
              height="80rpx"
              radius="50%"
              :src="item?.avatarUrl"
              mode="aspectFill"
            ></wd-img>

            <div
              v-if="type === 1"
              class="flex-1 flex items-center justify-between border-b border-b-solid border-b-#eeeeee pb40rpx box-border"
            >
              <div class="flex flex-col gap-y-12rpx">
                <span class="text-28rpx text-#333333">
                  {{ (item as Api.User.MyteamUserItem)?.nickName }}
                  <span class="text-26rpx text-#333333">
                    {{
                      (item as Api.User.MyteamUserItem)?.payMoney
                        ? `(总流水：${(item as Api.User.MyteamUserItem)?.payMoney})`
                        : ''
                    }}
                  </span>
                </span>
                <span class="text-26rpx text-#999999">
                  {{ item?.createTime ? `${item?.createTime}受邀` : '' }}
                </span>
              </div>
              <wd-button
                v-if="!(item as Api.User.MyteamUserItem)?.isSupplier"
                @click="handleShop((item as Api.User.MyteamUserItem)?.userId)"
                size="small"
              >
                商户进件
              </wd-button>
            </div>
            <div
              v-if="type === 2"
              class="flex-1 flex items-start justify-between border-b border-b-solid border-b-#eeeeee pb40rpx box-border"
            >
              <div class="flex flex-col gap-y-12rpx">
                <span class="text-28rpx text-#333333">
                  {{ (item as Api.User.MyteamShopItem)?.name }}
                </span>
                <span class="text-26rpx text-#999999">
                  {{ item?.createTime ? `${item?.createTime}受邀` : '' }}
                </span>
              </div>
              <div class="flex flex-col gap-y-12rpx">
                <span class="text-26rpx text-#999999">
                  总流水：{{ (item as Api.User.MyteamShopItem)?.salesMoney }}
                </span>
                <span class="text-26rpx text-#999999">
                  总业绩：{{ (item as Api.User.MyteamShopItem)?.sysMoney }}
                </span>
                <span class="text-26rpx text-#999999">
                  抽点：{{ (item as Api.User.MyteamShopItem)?.supplierCommissionRate }}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { userTeamList, userTeamSupplierLists } from '@/service'
import dayjs from 'dayjs'

const startDate = dayjs().startOf('month').format('YYYY-MM-DDTHH:mm:ss')
const endDate = dayjs().endOf('month').format('YYYY-MM-DDTHH:mm:ss')

const totalNum = ref('')
const totalShop = ref('')
const totalShopSys = ref('')
const paging = ref()
const dataList = ref<Api.User.MyteamUserItem[] | Api.User.MyteamShopItem[]>([])
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    if (type.value === 1) {
      const { data } = await userTeamList({
        pageIndex: pageNo,
        pageSize,
        level: 1,
        lastMonth: false,
        startDate,
        endDate,
      })
      totalNum.value = data?.totalPayMoney ?? ''
      paging.value.complete(data?.list?.records ?? [])
    } else {
      const { data } = await userTeamSupplierLists({
        pageIndex: pageNo,
        pageSize,
        level: 1,
        lastMonth: false,
        startDate,
        endDate,
      })
      totalShop.value = data?.performance?.totalMoney
      totalShopSys.value = data?.performance?.totalSysMoney
      paging.value.complete(data?.list?.records ?? [])
    }
  } catch (err) {
    paging.value.complete(false)
  }
}

const type = ref(1)

const tabs = ref([
  { label: '会员', key: 1 },
  { label: '商户', key: 2 },
])

const handleTabChange = ({ name }) => {
  type.value = name
  paging.value.reload()
}

const rateUserList = ref<Api.User.MyteamUserItem[]>([])
const rateShopList = ref<Api.User.MyteamShopItem[]>([])

watch(
  () => type.value,
  () => {
    handleRateOpen()
  },
)

const handleRateOpen = async () => {
  if (type.value === 1) {
    const { data } = await userTeamList({
      pageIndex: 1,
      pageSize: 10,
      level: 1,
      lastMonth: false,
      startDate,
      endDate,
    })
    rateUserList.value = data?.list?.records ?? []
  } else {
    const { data } = await userTeamSupplierLists({
      pageIndex: 1,
      pageSize: 10,
      level: 1,
      lastMonth: false,
      startDate,
      endDate,
    })
    rateShopList.value = data?.list?.records ?? []
  }
}

const handleShop = (userId: number) => {
  uni.navigateTo({ url: `/pages-my/my-shop/shop-apply/index?userId=${userId}` })
}
</script>

<style lang="scss" scoped>
//
</style>

import { http, httpPostForm } from '@/utils/http'

export const fetchChatList = () => {
  return http.post<Api.Message.ChatListData>('/api/front/plus/chat/chat/index')
}

export const fetchChatRecord = (data: {
  pageIndex: number
  pageSize: number
  serviceUserId: number
}) => {
  return http.post<Api.Message.ChatRecordData>('/api/front/plus/chat/chat/record', data)
}

export const fetchChatInfo = (params: {
  shopSupplierId: number | string
  serviceUserId: number
}) => {
  return http.get<{ avatarUrl: string; logo: string; url: string }>(
    '/api/front/plus/chat/chat/getInfo',
    params,
  )
}

export const fetchMpService = (params: { shopSupplierId: number | string }) => {
  return http.get<any>('/api/front/index/mpService', params)
}

export const fetchSupplierInfo = (data: { shopSupplierId: number | string }) => {
  return http.post<any>('/api/front/supplier/index/wordIndex', data)
}

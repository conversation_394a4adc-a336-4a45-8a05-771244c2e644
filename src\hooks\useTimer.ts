import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)

interface UseTimerOptions {
  countdown?: boolean // 是否倒计时（默认 false）
  initialSeconds?: number // 初始秒数
  autoStart?: boolean // 是否自动开始
  onFinished?: () => void // 倒计时完成回调
}

export function useTimer(options: UseTimerOptions = {}) {
  const { countdown = false, initialSeconds = 0, autoStart = false, onFinished } = options

  const seconds = ref(initialSeconds)
  let timer: number | null = null

  const formattedTime = computed(() => {
    const dur = dayjs.duration(seconds.value, 'seconds')
    return dur.format('HH:mm:ss')
  })

  const isRunning = ref(false)

  function start() {
    if (timer !== null || isRunning.value) return
    timer = setInterval(() => {
      if (countdown) {
        if (seconds.value > 0) {
          seconds.value--
        } else {
          stop()
          onFinished?.()
        }
      } else {
        seconds.value++
      }
    }, 1000)
    isRunning.value = true
  }

  function pause() {
    if (timer !== null) {
      clearInterval(timer)
      timer = null
    }
    isRunning.value = false
  }

  function reset() {
    pause()
    seconds.value = initialSeconds
  }

  function stop() {
    pause()
    if (countdown) {
      seconds.value = 0
    }
  }

  onUnmounted(() => pause())

  if (autoStart) start()

  return {
    seconds,
    formattedTime,
    isRunning,
    start,
    pause,
    reset,
    stop,
  }
}

<template>
  <div :style="{ paddingTop: menuButtonInfo.top + 'px' }" class="">
    <div
      :style="{ height: menuButtonInfo.height + 'px' }"
      class="flex items-center flex-1 of-hidden pl-26rpx box-border gap-x-10px"
    ></div>
  </div>
</template>

<script lang="ts" setup>
const menuButtonInfo = ref({
  width: 0,
  height: 32,
  top: 20,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif
</script>

<style lang="scss" scoped>
//
</style>

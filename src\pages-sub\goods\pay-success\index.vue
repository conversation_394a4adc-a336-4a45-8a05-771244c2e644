<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full grid grid-cols-1 grid-auto-rows-min gap-y-30rpx">
    <wd-navbar title="支付成功" placeholder fixed safeAreaInsetTop>
      <template #capsule>
        <div class="i-carbon-home text-#999999 text-18px" @click="goHome"></div>
      </template>
    </wd-navbar>
    <div class="flex items-center flex-col justify-center p60rpx box-border gap-y-20rpx">
      <div class="i-hugeicons-checkmark-circle-01 bg-#04be01 text-#ffffff text-80rpx p30rpx"></div>
      <span class="text-30rpx">支付成功</span>
    </div>
    <div class="text-36rpx flex items-center justify-center">
      ￥
      <span class="text-60rpx font-bold">{{ payDetail?.payPrice }}</span>
    </div>
    <div class="p-20rpx bg-#ffffff flex items-center justify-between">
      <span class="text-26rpx text-#999999">积分赠送</span>
      <span class="text-26rpx text-#333333">{{ payDetail?.pointsBonus }}</span>
    </div>
    <div class="flex items-center gap-x-10px px-10px box-border mt10px">
      <wd-button
        @click="goHome"
        plain
        custom-class="!m-unset !min-w-unset !flex-1 !rd-5px !h-80rpx"
      >
        返回首页
      </wd-button>
      <wd-button @click="goOrderList" custom-class="!m-unset !min-w-unset !flex-1 !rd-5px !h-80rpx">
        我的订单
      </wd-button>
    </div>
  </view>
</template>

<script lang="ts" setup>
import { paySuccessDetail } from '@/service'
import { useUserStore } from '@/store'

const userStore = useUserStore()

defineOptions({
  name: 'PaySuccess',
})

const payDetail = ref()
onLoad(async (option) => {
  if (option.orderId) {
    const { data } = await paySuccessDetail({
      appId: import.meta.env.VITE_APPID,
      orderId: option?.orderId,
      token: userStore.token,
    })
    payDetail.value = data
  }
})

const goHome = () => {
  uni.reLaunch({ url: '/pages/index/index' })
}

const goOrderList = () => {
  uni.reLaunch({ url: '/pages-sub/myOrder/index?dataType=all' })
}
</script>

<style lang="scss" scoped>
//
</style>

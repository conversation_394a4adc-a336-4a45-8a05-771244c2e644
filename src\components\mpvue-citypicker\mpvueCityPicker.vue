<template>
  <div class="mpvue-picker">
    <div :class="{ pickerMask: showPicker }" @click="maskClick" @touchmove.stop></div>
    <div class="mpvue-picker-content" :class="{ 'mpvue-picker-view-show': showPicker }">
      <div class="mpvue-picker__hd" @touchmove.stop>
        <div class="mpvue-picker__action" @click="pickerCancel">取消</div>
        <div class="mpvue-picker__action" :style="{ color: themeColor }" @click="pickerConfirm">
          确定
        </div>
      </div>
      <picker-view
        indicator-style="height: 40px"
        class="mpvue-picker-view"
        :value="pickerValue"
        @change="pickerChange"
      >
        <picker-view-column>
          <div class="picker-item" v-for="(item, index) in provinceDataList" :key="index">
            {{ item.label }}
          </div>
        </picker-view-column>
        <picker-view-column>
          <div class="picker-item" v-for="(item, index) in cityDataList" :key="index">
            {{ item.label }}
          </div>
        </picker-view-column>
        <picker-view-column>
          <div class="picker-item" v-for="(item, index) in areaDataList" :key="index">
            {{ item.label }}
          </div>
        </picker-view-column>
      </picker-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch, nextTick } from 'vue'

// 定义组件 props 类型
interface PickerProps {
  pickerValueDefault?: number[]
  themeColor?: string
  province?: Array<{ label: string; value?: number }>
  city?: Array<Array<{ label: string; value?: number }>>
  area?: Array<Array<Array<{ label: string; value?: number }>>>
}

// 定义 emits 事件类型
interface PickerEmits {
  (e: 'onCancel'): void
  (e: 'onConfirm', pickObj: PickerResult): void
  (e: 'onChange', pickObj: PickerResult): void
}

// 定义返回结果类型
interface PickerResult {
  label: string
  value: number[]
  cityCode: number[]
}

// 接收 props
const props = withDefaults(defineProps<PickerProps>(), {
  pickerValueDefault: () => [0, 0, 0],
  themeColor: '#007AFF',
})

// 定义 emits
const emit = defineEmits<PickerEmits>()

// 响应式数据
const showPicker = ref(false)
const pickerValue = ref(props.pickerValueDefault)
const provinceDataList = ref<Array<{ label: string; value?: number }>>([])
const cityDataList = ref<Array<{ label: string; value?: number }>>([])
const areaDataList = ref<Array<{ label: string; value?: number }>>([])
const provinceData = ref(props.province || [])
const cityData = ref(props.city || [])
const areaData = ref(props.area || [])

// 初始化处理
const init = () => {
  provinceData.value = props.province || []
  cityData.value = props.city || []
  areaData.value = props.area || []
  handlePickerValueDefault()
  updateDataList()
}

// 处理默认值兼容
const handlePickerValueDefault = () => {
  const [p, c, a] = props.pickerValueDefault
  const finalP = Math.min(p, provinceData.value.length - 1)
  const finalC = Math.min(c, cityData.value[finalP]?.length - 1 || 0)
  const finalA = Math.min(a, areaData.value[finalP]?.[finalC]?.length - 1 || 0)

  pickerValue.value = [finalP, finalC, finalA]

}

// 更新数据列表
const updateDataList = () => {
  const [p, c, a] = pickerValue.value
  provinceDataList.value = provinceData.value
  cityDataList.value = cityData.value[p] || []
  areaDataList.value = areaData.value[p]?.[c] || []


  // 强制触发 picker-view 的视图更新
  nextTick(() => {
    // 临时改变值再改回来，强制触发视图更新
    const temp = [...pickerValue.value]
    pickerValue.value = [0, 0, 0]
    nextTick(() => {
      pickerValue.value = temp
      console.log('picker-view 强制更新完成，最终值:', pickerValue.value)
    })
  })
}

// 显示选择器
const show = () => {
  showPicker.value = true
}

defineExpose({ show })

// 遮罩层点击
const maskClick = () => {
  pickerCancel()
}

// 取消选择
const pickerCancel = () => {
  showPicker.value = false
  emit('onCancel')
}

// 确定选择
const pickerConfirm = () => {
  showPicker.value = false
  const pickObj = getPickerResult()
  emit('onConfirm', pickObj)
}

// 选择变化处理
const pickerChange = (e: any) => {
  const [newP, newC, newA] = e.detail.value
  const newValue = [...pickerValue.value]

  if (newValue[0] !== newP) {
    newValue[0] = newP
    newValue[1] = 0
    newValue[2] = 0
    cityDataList.value = cityData.value[newP] || []
    areaDataList.value = areaData.value[newP]?.[0] || []
  } else if (newValue[1] !== newC) {
    newValue[1] = newC
    newValue[2] = 0
    areaDataList.value = areaData.value[newP]?.[newC] || []
  } else {
    newValue[2] = newA
  }

  pickerValue.value = newValue
  const pickObj = getPickerResult()
  emit('onChange', pickObj)
}

// 获取选择结果
const getPickerResult = (): PickerResult => {
  const [p, c, a] = pickerValue.value
  const label = `${provinceDataList.value[p].label},${cityDataList.value[c].label},${areaDataList.value[a].label}`
  const cityCode = [
    provinceDataList.value[p]?.value || 0,
    cityDataList.value[c]?.value || 0,
    areaDataList.value[a]?.value || 0,
  ]
  return {
    label,
    value: pickerValue.value,
    cityCode,
  }
}

// 监听 props 变化
watch(
  [() => props.province, () => props.city, () => props.area],
  () => {
    init()
  },
  { deep: true },
)

// 监听 pickerValueDefault 变化
watch(
  () => props.pickerValueDefault,
  (newVal) => {
    console.log('=== pickerValueDefault 变化 ===')
    console.log('新值:', newVal)
    console.log('旧值:', pickerValue.value)

    if (newVal && Array.isArray(newVal)) {
      handlePickerValueDefault()
      updateDataList()

      // 强制触发 picker-view 重新渲染
      nextTick(() => {
        console.log('强制触发 picker-view 重新渲染')
        const currentValue = [...pickerValue.value]
        pickerValue.value = [-1, -1, -1] // 设置一个无效值
        nextTick(() => {
          pickerValue.value = currentValue // 恢复正确值
          console.log('picker-view 重新渲染完成，当前值:', pickerValue.value)
        })
      })
    }

    console.log('更新后的 pickerValue:', pickerValue.value)
    console.log('==============================')
  },
  { deep: true },
)

// 组件挂载时初始化
onMounted(() => {
  init()
})
</script>

<style lang="scss" scoped>
/* 样式保持与原代码一致，无需修改 */
.pickerMask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.mpvue-picker-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 1000;
  transition: all 0.3s ease;
  transform: translateY(100%);
}

.mpvue-picker-view-show {
  transform: translateY(0);
}

.mpvue-picker__hd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

.mpvue-picker__action {
  font-size: 16px;
  color: #333;
}

.mpvue-picker-view {
  height: 216px;
}

.picker-item {
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  color: #666;
}
</style>

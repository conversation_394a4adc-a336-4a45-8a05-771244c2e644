{"id": "lime-fab", "displayName": "lime-fab 浮动气泡-悬浮按钮", "version": "0.1.3", "description": "lime-fab 悬浮在页面边缘并可拖拽可点击的汽泡型悬浮按钮，自主吸附，兼容uniapp/uniappx，vue2需要配置vue/composition-api", "keywords": ["浮动气泡", "吸附边缘", "拖拽", "悬浮按钮", "悬浮拖拽按钮"], "repository": "", "engines": {"HBuilderX": "^4.26"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["lime-shared", "lime-style"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-uvue": "y", "app-nvue": "y", "app-harmony": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}
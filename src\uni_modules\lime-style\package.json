{"id": "lime-style", "displayName": "lime-style", "version": "0.1.4", "description": "lime-style", "keywords": ["lime-style"], "repository": "", "engines": {"HBuilderX": "^3.1.0", "uni-app": "^4.44", "uni-app-x": "^4.61"}, "dcloudext": {"type": "sdk-js", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": {"extVersion": "", "minVersion": "22"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "√", "jd": "√", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "√", "chrome": "√"}, "app": {"android": {"extVersion": "", "minVersion": "22"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√"}}}}}}
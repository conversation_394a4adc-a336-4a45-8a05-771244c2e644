<!-- <route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '开票详情',
  },
}
</route> -->

<template>
  <view class="min-h-screen bg-#f5f5f5 flex flex-col">
    <!-- 发票背景图区域 -->
    <view class="flex-1 px-30rpx pt-40rpx pb-40rpx flex justify-center">
      <view
        class="invoice-bg w-700rpx h-620rpx relative rd-20rpx overflow-hidden"
        :style="{
          backgroundImage: `url('https://file.shanqianqi.com/image/2025/06/30/6a6970f238614865aef84415b6095970.png')`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <!-- 发票信息内容 -->
        <view class="absolute inset-0 px-60rpx py-80rpx flex flex-col justify-center space-y-35rpx">
          <!-- 发票抬头 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-500 w-140rpx flex-shrink-0">发票抬头：</view>
            <view class="text-28rpx text-#000000 font-600 flex-1">
              {{ invoiceDetail.title }}
            </view>
          </view>

          <!-- 税号 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-500 w-140rpx flex-shrink-0">税号：</view>
            <view class="text-28rpx text-#000000 font-500 flex-1">
              {{ invoiceDetail.taxNumber }}
            </view>
          </view>

          <!-- 发票金额 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-500 w-140rpx flex-shrink-0">发票金额：</view>
            <view class="text-32rpx text-#ff4444 font-700 flex-1">¥{{ invoiceDetail.amount }}</view>
          </view>

          <!-- 发票内容 -->
          <view class="flex items-start gap50rpx">
            <view class="text-28rpx text-#333333 font-500 w-140rpx flex-shrink-0 leading-relaxed">
              发票内容：
            </view>
            <view class="text-26rpx text-#000000 font-500 flex-1 leading-relaxed">
              {{ invoiceDetail.content }}
            </view>
          </view>

          <!-- 申请日期 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-500 w-140rpx flex-shrink-0">申请日期：</view>
            <view class="text-28rpx text-#000000 font-500 flex-1">
              {{ invoiceDetail.applyDate }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部区域 -->
    <view class="px-30rpx pb-40rpx pt-20rpx bg-#F6F6F6">
      <!-- 发票中心标题 -->
      <view class="text-center mb-14rpx" @click="handleInvoiceCenter">
        <view class="invoice-center-title">发票中心</view>
      </view>

      <!-- 修改申请按钮 -->
      <wd-button
        @click="handleModifyApply"
        custom-class="!bg-#ff7d26 !h-88rpx !text-32rpx !font-600"
        block
        round
      >
        修改申请
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface InvoiceDetail {
  title: string
  taxNumber: string
  amount: string
  content: string
  applyDate: string
}

// 模拟发票详情数据
const invoiceDetail = ref<InvoiceDetail>({
  title: '北京科技有限公司',
  taxNumber: '91110000123456789X',
  amount: '12,999.00',
  content: '明细',
  applyDate: '2024年01月15日',
})
const handleInvoiceCenter = () => {
  uni.navigateTo({
    url: '/pages-sub/orderInvoice/invoiceCenter',
  })
}
// 修改申请
const handleModifyApply = () => {
  uni.showToast({
    title: '跳转到修改页面',
    icon: 'none',
  })
  // 这里可以跳转到修改申请页面
  // uni.navigateTo({
  //   url: '/pages-sub/orderInvoice/modifyInvoice'
  // })
}

// 页面加载时获取发票详情
onMounted(() => {
  // 这里可以根据路由参数获取具体的发票详情
  // const pages = getCurrentPages()
  // const currentPage = pages[pages.length - 1]
  // const options = currentPage.options
  // fetchInvoiceDetail(options.id)
})
</script>

<style lang="scss" scoped>
.invoice-bg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

// 确保文字在背景图上清晰可见
.invoice-bg view {
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

// 发票中心标题样式
.invoice-center-title {
  color: #ff2736;
  font-family: 'PingFang SC';
  font-weight: 500;
  font-size: 32rpx;
  text-decoration: underline;
  text-decoration-color: #ff2736;
  text-underline-offset: 8rpx;
}
</style>

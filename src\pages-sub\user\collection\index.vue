<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden pb-2 box-border h-full">
    <z-paging
      ref="paging"
      v-model="dataList"
      auto-show-system-loading
      :default-page-size="20"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <SimpleNavBar title="收藏/关注" />
        <wd-tabs @change="handleTabChange" v-model="tab" auto-line-width>
          <block v-for="item in tabs" :key="item.key">
            <wd-tab :title="`${item.label}`" :name="item.label"></wd-tab>
          </block>
        </wd-tabs>
      </template>
      <view class="grid grid-cols-1 gap-10px p20rpx box-border">
        <view
          v-for="item in dataList"
          :key="item.productId"
          class="flex bg-#ffffff shadow-[0rpx_4rpx_8rpx_0rpx_rgba(0,0,0,0.02)] rd-20rpx of-hidden box-border"
        >
          <template v-if="tabIndex === 0">
            <div class="flex w-full" @click="() => goGoodsDetail(item.productId, item?.storeId)">
              <wd-img
                :src="item?.productImage"
                width="220rpx"
                height="220rpx"
                mode="aspectFit"
              ></wd-img>

              <div class="right p-20rpx box-border flex flex-col flex-1 justify-between">
                <div class="text-32rpx text-#333333 line-clamp-1 font-600">
                  {{ item?.productName }}
                </div>
                <div class="flex items-baseline gap-x-10px text-#FF7D26">
                  <div class="flex items-baseline">
                    <span class="text-20rpx font-500">¥</span>
                    <span class="text-40rpx font-bold">
                      {{ item?.productPrice?.split('.')?.[0] }}
                    </span>
                    <span class="text-24rpx font-bold">
                      .{{ item?.productPrice?.split('.')?.[1] }}
                    </span>
                  </div>
                  <span class="text-20rpx text-#666666 line-through">¥{{ item?.linePrice }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="text-24rpx text-#585858">
                    累计销量:{{ formatLargeNumber(item?.productSales) }}+
                  </div>
                  <div @click.stop="() => hanldeCancel(item?.productId)">
                    <wd-button custom-class="!m-unset" size="small">取消收藏</wd-button>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="tabIndex === 1">
            <div
              @click="() => goSupplierIndex(item?.shopSupplierId, item?.storeId, item?.storeName)"
              class="bg-#ffffff shadow-light rd-20rpx p20rpx box-border w-full flex flex-col gap-y-20rpx"
            >
              <div class="flex items-start justify-between">
                <div class="flex items-center gap-x-20rpx">
                  <wd-img
                    v-if="item?.logoPath"
                    width="100rpx"
                    height="100rpx"
                    :src="item?.logoPath"
                    mode="aspectFit"
                  ></wd-img>
                  <div v-else class="i-carbon-store text-40rpx text-#FF7D26"></div>
                  <div class="flex flex-col gap-y-10rpx">
                    <span class="text-32rpx text-#333333 font-600 line-clamp-1">
                      {{ item?.name }}
                      {{ item?.storeName ? `(${item?.storeName})` : '' }}
                    </span>
                    <div class="flex items-center gap-x-10px">
                      <wd-rate :modelValue="getScore(item.serverScore, 1)" readonly />
                      <span class="text-24rpx text-#666666">{{ item?.serverScore ?? '' }} 分</span>
                      <!-- <span class="text-24rpx text-#666666">
                          {{ store?.favCount ?? '' }} 人关注
                        </span> -->
                    </div>
                  </div>
                </div>
                <div
                  @click.stop="() => hanldeCancelSupplier(`${item?.shopSupplierId}`, item.storeId)"
                >
                  <wd-button custom-class="!m-unset" size="small">取消收藏</wd-button>
                </div>
              </div>
              <!-- <scroll-view :show-scrollbar="false" scroll-x enable-flex class="flex items-center">
                <div
                  v-for="ele in item?.productList"
                  :key="ele.productId"
                  @click.stop="() => goGoodsDetail(ele.productId)"
                  class="flex flex-col gap-y-10rpx mr-20px"
                >
                  <wd-img
                    width="200rpx"
                    height="200rpx"
                    :src="ele?.productImage"
                    mode="aspectFit"
                    custom-class="!rd-20rpx"
                  ></wd-img>
                  <div class="flex items-baseline gap-x-12rpx text-#FF7D26">
                    <div class="flex items-baseline">
                      <span class="text-20rpx font-500">¥</span>
                      <span class="text-40rpx font-bold">
                        {{ ele?.productPrice?.split('.')?.[0] }}
                      </span>
                      <span class="text-24rpx font-bold">
                        .{{ ele?.productPrice?.split('.')?.[1] }}
                      </span>
                    </div>
                    <span class="text-20rpx text-#666666 line-through">¥{{ ele?.linePrice }}</span>
                  </div>
                </div>
              </scroll-view> -->
            </div>
          </template>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { userFavoriteList, userCancelFav, userFavAdd } from '@/service'
import { getScore, formatLargeNumber } from '@/utils'
import { useUserStore } from '@/store'

const userStore = useUserStore()
onLoad(() => {})
const paging = ref()
const dataList = ref<Api.User.CollectionItem[] & Api.Supplier.SupplierItem[]>([])

const tabs = ref([
  { label: '商品', key: 20 },
  { label: '商户(门店)', key: 10 },
])

const tab = ref('商品')

const tabIndex = computed(() => tabs.value.findIndex((item) => item.label === tab.value))

const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await userFavoriteList({
      pageIndex: pageNo,
      pageSize: pageSize,
      type: tabs.value[tabIndex.value]?.key,
      appId: import.meta.env.VITE_APPID,
    })
    paging.value.complete(data?.records)
  } catch (err) {
    paging.value.complete(false)
  }
}

onShow(() => {
  paging.value && paging.value?.reload()
})

const hanldeCancel = async (productId: number) => {
  try {
    await userCancelFav({ appId: import.meta.env.VITE_APPID, token: userStore.token, productId })
    paging.value?.reload()
  } catch (error) {
    console.log('🚀 ~ hanldeCancel ~ error:', error)
  }
}

const hanldeCancelSupplier = async (pid: string, storeId: any) => {
  try {
    await userFavAdd({ appId: import.meta.env.VITE_APPID, pid, storeId, type: 10 })
    paging.value?.reload()
  } catch (error) {
    console.log('🚀 ~ hanldeCancel ~ error:', error)
  }
}

const handleTabChange = () => {
  paging.value.reload()
}

const goGoodsDetail = (id: number, storeId: any) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}&storeId=${storeId}` })
}

const goSupplierIndex = (id: number, storeId: number, storeName: string) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${id}&storeId=${storeId}&storeName=${storeName}`,
  })
}
</script>

<style lang="scss" scoped></style>

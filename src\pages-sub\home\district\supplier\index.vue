<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full box-border">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="10"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <div class="grid grid-cols-1 bg-#F2F2F2" :style="{ paddingTop: menuButtonInfo.top + 'px' }">
          <!-- 搜索 -->
          <div class="grid grid-cols-1 text-#333333 box-border">
            <div
              class="flex items-center gap-x-10rpx pb26rpx"
              :style="{
                height: menuButtonInfo.height + 'px',
                marginRight: menuButtonInfo.width + 16 + 'px', // 16为额外预留
              }"
            >
              <div
                @click="hanleGoHome"
                class="w-60rpx h-60rpx pl-24rpx bg-none flex items-center justify-center"
              >
                <!-- <div class="i-carbon-home text-28rpx text-#333333"></div> -->
                <wd-icon name="thin-arrow-left" size="28rpx" color="#333333"></wd-icon>
              </div>
              <div
                class="flex items-center h-full of-hidden bg-white flex-1 box-border pl-20rpx gap-x-10rpx rd-30rpx"
              >
                <wd-icon name="search" size="28rpx" color="#999999"></wd-icon>
                <wd-input
                  inputmode="search"
                  v-model="searchVal"
                  @confirm="handleSearch"
                  @clear="handleSearch"
                  no-border
                  placeholder="搜索商品"
                  clearable
                  confirm-type="search"
                  placeholderClass="text-#999999"
                  custom-class="!flex-1 !text-#999999 !h-full !flex !items-center !text-24rpx"
                ></wd-input>
                <wd-button
                  @click="handleSearch"
                  custom-class="!w-108rpx !p-unset !min-w-unset !px30rpx !py14rpx !box-border !rd-30rpx !h-58rpx"
                >
                  搜索
                </wd-button>
              </div>
            </div>
            <!-- 商铺信息 -->
            <div
              @click="
                () => goSupplierDetail(shopInfo?.detail?.shopSupplierId, shopInfo?.store?.storeId)
              "
              class="flex items-center gap-x-10px bg-white px30rpx py26rpx box-border rounded-lt-30rpx rounded-rt-30rpx"
            >
              <wd-img
                v-if="shopInfo?.store?.logoFilePath"
                width="102rpx"
                height="102rpx"
                :src="shopInfo?.store?.logoFilePath"
                radius="10"
                mode="aspectFit"
              ></wd-img>
              <div
                v-else
                class="w102rpx h102rpx flex items-center justify-center rd-20rpx bg-#ffffff"
              >
                <div class="i-carbon-store text-40rpx text-#FF7D26"></div>
              </div>
              <div class="flex-1 flex flex-col gap-y-5px">
                <div class="flex items-center gap-x-5px">
                  <span class="text-30rpx font-bold">
                    {{ shopInfo?.detail?.name }} {{ storeName ? '(' + storeName + ')' : '' }}
                  </span>
                  <div class="i-carbon-chevron-right text-14px text-white font-bold"></div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex flex-col gap12rpx">
                    <div class="flex items-center gap-x-5px text-#ffffff text-24rpx">
                      <wd-rate
                        :modelValue="
                          shopInfo?.detail?.serverScore
                            ? getScore(shopInfo?.detail?.serverScore, 1)
                            : getScore(5, 1)
                        "
                        :active-color="[
                          'linear-gradient(180deg, rgba(255,238,0,1) 0%,rgba(250,176,21,1) 100%)',
                          'linear-gradient(315deg, rgba(245,34,34,1) 0%,rgba(255,117,102,1) 100%)',
                        ]"
                        readonly
                      />
                      <span>{{ shopInfo?.detail?.serverScore ?? '' }}分</span>
                    </div>
                    <span class="text-#333333 text-24rpx">
                      主营品牌:{{ shopInfo?.detail?.categoryName }}
                    </span>
                  </div>
                  <div @click.stop="hanldeAddFav(shopInfo.store.storeId)">
                    <wd-button custom-style="margin:0;" custom-class="!min-w-160rpx !m-unset">
                      {{ shopInfo?.detail?.isfollow ? '已关注' : '+关注' }}
                    </wd-button>
                  </div>
                </div>
              </div>
            </div>
            <!-- 粉丝 -->
            <div class="flex items-center justify-end bg-white px30rpx pb10px">
              <!-- <div class="flex items-center gap-x-15px px15px box-border">
                <div class="flex flex-col items-center text-#333333 text-24rpx gap-y-5px">
                  <span>{{ shopInfo?.detail?.productSales ?? 0 }}</span>
                  <span>销量</span>
                </div>
                <div class="flex flex-col items-center text-#333333 text-24rpx gap-y-5px">
                  <span>{{ shopInfo?.detail?.favCount ?? 0 }}</span>
                  <span>粉丝</span>
                </div>
              </div> -->
              <!-- <wd-button
                @click="hanldeAddFav(shopInfo.store.storeId)"
                custom-class="!min-w-160rpx !m-unset"
              >
                {{ shopInfo?.detail?.isfollow ? '已关注' : '+关注' }}
              </wd-button> -->
            </div>
          </div>
          <!-- tabs -->
          <!-- <div class="bg-#ffffff">
            <wd-tabs @change="handleTabChange" v-model="tab" auto-line-width>
              <block v-for="item in tabs" :key="item.key">
                <wd-tab :title="`${item.label}`" :name="`${item.label}`"></wd-tab>
              </block>
            </wd-tabs>
          </div> -->
          <view v-if="isStore" class="rounded-lb-30rpx rounded-rb-30rpx of-hidden">
            <view class="bg-#ffffff py20rpx px30rpx text-24rpx text-#333333 fw-400">
              <span class="mr10rpx">营业时间</span>
              <span>{{ shopInfo?.store?.shopHours }}</span>
            </view>
            <view
              @click="navigateToShop()"
              class="bg-#ffffff pb20rpx px30rpx text-24rpx text-#333333 fw-400 flex items-center justify-between"
            >
              <view>
                <view class="bg-#ffffff mb-10rpx">
                  <span>{{ shopInfo?.store?.area }}</span>
                  <span>{{ shopInfo?.store?.address }}</span>
                </view>
                <view class="flex items-center">
                  <view class="flex bg-#ffffff text-#999999 text-20rpx">
                    <div class="flex items-end">
                      <span>距您{{ timeData.distanceInKm }}公里｜</span>
                    </div>
                    <span>驾车{{ timeData.driveTime }}｜</span>
                    <span>骑行{{ timeData.bikeTime }}</span>
                  </view>
                </view>
              </view>

              <view class="flex items-end gap44rpx bg-#ffffff text-#999999 text-20rpx">
                <div @click.stop="navigateToShop()" class="flex flex-col gap2rpx">
                  <image
                    src="https://file.shanqianqi.com/image/2025/06/21/0b4378797e884a0983e389ff0917314f.png"
                    class="w-46rpx h-46rpx"
                  ></image>
                  <div>定位</div>
                </div>
                <div
                  @click.stop="callPhone(shopInfo?.detail?.linkPhone)"
                  class="flex flex-col gap2rpx"
                >
                  <image
                    src="https://file.shanqianqi.com/image/2025/06/21/774c0c7206a24438a68dc7e5961b4b56.png"
                    class="w-46rpx h-46rpx"
                  ></image>
                  <div>电话</div>
                </div>
              </view>
            </view>
          </view>
        </div>
      </template>
      <div class="">
        <view style="font-family: PingFang SC" class="text-30rpx fw-600 pl-24rpx pt20rpx">
          商品
        </view>
        <div
          class="grid grid-cols-1 mt10px gap-y-20px p10px box-border"
          :class="`${dataList.length > 0 ? 'bg-#ffffff' : ''}`"
        >
          <div
            @click="() => goGoodsDetail(item)"
            v-for="item in dataList"
            :key="item.productId"
            class="flex items-center gap-x-10px"
          >
            <div class="w-160rpx h-160rpx rd-25rpx of-hidden relative">
              <wd-img
                :src="item?.productImage"
                width="160rpx"
                height="160rpx"
                mode="aspectFit"
              ></wd-img>
              <div
                v-if="item?.productStock <= 0"
                class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center"
              >
                <span>售罄</span>
              </div>
            </div>

            <div class="flex-1 flex flex-col justify-between h-full">
              <div class="flex flex-col w-full gap-y-16rpx">
                <span class="text-28rpx fw-550 text-#333333 line-clamp-1">
                  {{ item?.productName }}
                </span>
                <!-- <span class="text-24rpx text-#999999">累计成交：{{ item?.productSales }}笔</span> -->
              </div>
              <div>
                <div class="text-#999999 text-20rpx fw-400 line-through">
                  ¥ {{ item?.linePrice }}
                </div>
                <view class="flex items-end justify-between gap-x-16rpx">
                  <div class="flex items-end gap-x-12rpx">
                    <div class="flex items-baseline text-24rpx text-#ff4c01">
                      ¥
                      <!-- <span class="text-32rpx font-bold">{{ item?.productPrice }}</span> -->
                      <div class="text-#FF7D26">
                        <text class="text-40rpx font-bold">
                          {{ item?.productPrice?.split('.')?.[0] }}
                        </text>
                        <text class="text-24rpx font-bold">
                          .{{ item?.productPrice?.split('.')?.[1] }}
                        </text>

                        <span
                          v-if="item?.extraRedPocketPrice && item?.extraRedPocketPrice !== '0.00'"
                          class="text-#FF7D26 text-20rpx fw-400"
                        >
                          + {{ item?.extraRedPocketPrice }}红包
                        </span>

                        <span class="text-#FF7D26 text-20rpx fw-400">/ 件</span>
                      </div>
                    </div>
                  </div>
                  <view class="">
                    <wd-button custom-class="!min-w-160rpx">去购买</wd-button>
                  </view>
                </view>
              </div>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { callPhone } from '@/utils'
import {
  fetchRecGoods,
  fetchSuppliersNewIndex,
  fetchSupplierslsHaveStore,
  userFavAdd,
} from '@/service'
import { useUserStore } from '@/store'
import { getScore, getVisitcode } from '@/utils'
import { useLocationStore } from '@/store'
defineOptions({
  name: 'Supplier',
})

const userStore = useUserStore()
const locationStore = useLocationStore()
const menuButtonInfo = ref({
  width: 0,
  height: 32,
  top: 20,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const isStore = ref(false)
const shopId = ref('')
const storeId = ref('')
const storeName = ref('')
let timeData = ref({
  walkTime: '0分钟',
  distanceInKm: 0,
  driveTime: '0分钟',
  bikeTime: '0分钟',
})
const searchVal = ref('')
let fromRoute = ref(null)
onLoad((option) => {
  // getTimeData(1.3)
  if (option) {
    shopId.value = option?.shopId
    storeId.value = option?.storeId
    storeName.value = option?.storeName
    if (option.from) {
      fromRoute.value = option.from
    }
  }
})
onShow(() => {
  paging?.value?.reload()
})

const tabs = ref([
  { label: '综合', key: 'all' },
  { label: '销量', key: 'sales' },
  { label: '价格', key: 'price' },
])
const tab = ref('综合')

const tabIndex = computed(() => tabs.value.findIndex((item) => item.label === tab.value))

const paging = ref()

const dataList = ref<Api.Home.GoodItem[]>([])

// const shopInfo = ref<Api.Supplier.SupplierHome>()
const shopInfo = ref<any>()
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    let { data: IsStoreData } = await fetchSupplierslsHaveStore({
      supplierId: shopId.value,
      appId: import.meta.env.VITE_APPID,
    })
    //为true有门店
    isStore.value = IsStoreData

    const { data } = await fetchSuppliersNewIndex({
      longitude: locationStore.locationInfo?.location?.lon,
      latitude: locationStore.locationInfo?.location?.lat,
      appId: import.meta.env.VITE_APPID,
      shopSupplierId: shopId.value,
      userId: userStore?.userInfo?.userId,
      storeId: storeId.value,
      visitcode: getVisitcode(),
    })
    //数据结构变化,有门店跟无门店数据响应结构不同
    if (IsStoreData) {
      console.log('Number(data?.store?.distance)', Number(data?.store?.distance))
      timeData.value = getTimeData(Number(data?.store?.distance))
      console.log('timeData.value', timeData.value)
      shopInfo.value = data
      storeName.value = data?.store?.storeName
    } else {
      shopInfo.value = {
        detail: data.supplier,
      }
    }
    console.log(' shopInfo.value.detail ', shopInfo.value)
    const { data: goodData } = await fetchRecGoods(
      {
        appId: import.meta.env.VITE_APPID,
        pageIndex: pageNo,
        pageSize,
        shopSupplierId: shopId.value,
        sortPrice: 1,
        search: searchVal.value,
        // sortType: tabs.value[tabIndex.value]?.key,
        type: 'sell',
      },
      { appId: import.meta.env.VITE_APPID },
    )
    paging.value.complete(goodData?.records)
  } catch (err) {
    console.log('err', err)

    paging.value.complete(false)
  }
}

const handleSearch = () => {
  paging.value.reload()
}

const handleTabChange = () => {
  paging.value.reload()
}

// 导航到店铺的函数
const navigateToShop = () => {
  if (!shopInfo.value.store.latitude || !shopInfo.value.store.longitude) {
    uni.showToast({ title: '未获取到店铺经纬度', duration: 2000, icon: 'none' })
    return
  }
  uni.openLocation({
    latitude: Number(shopInfo.value.store.latitude),
    longitude: Number(shopInfo.value.store.longitude),
    name: shopInfo.value.store.address, // 店铺地址作为名称
    address: shopInfo.value.store.address, // 店铺详细地址
    success: (res) => {
      console.log('打开地图成功', res)
    },
    fail: (err) => {
      console.error('打开地图失败', err)
      uni.showToast({ title: '打开地图失败', duration: 2000, icon: 'none' })
    },
  })
}
/**
 * 根据距离换算出行走、骑行和开车的时间，并自动格式化时间单位（更贴近高德等地图平台估算）
 * @param distance - 距离（单位：米 m）
 * @returns 包含步行、骑行、驾车时间的字符串，以及保留一位小数的公里数
 */
const getTimeData = (
  distance: number, // 注意：现在是公里单位，例如 6.23
): { distanceInKm: number; walkTime: string; bikeTime: string; driveTime: string } => {
  if (typeof distance !== 'number' || distance <= 0) {
    return {
      walkTime: '0分钟',
      bikeTime: '0分钟',
      driveTime: '0分钟',
      distanceInKm: 0,
    }
  }

  // 保留两位小数的公里数（显示用）
  const distanceInKm = Number(distance.toFixed(2))

  // 将公里转回为米用于时间计算
  const distanceInMeters = distance * 1000

  // 定义平均速度（单位：米/分钟）
  const walkSpeedMPerMin = 80 // 步行 ≈ 4.8 km/h
  const bikeSpeedMPerMin = 210 // 骑行 ≈ 12.6 km/h
  const cityDriveSpeedMPerMin = 750 // 市内驾车 ≈ 45 km/h

  // 计算所需时间（分钟）
  const walkMinutes = distanceInMeters / walkSpeedMPerMin
  const bikeMinutes = distanceInMeters / bikeSpeedMPerMin
  const driveMinutes = distanceInMeters / cityDriveSpeedMPerMin

  // 格式化时间为合适的单位
  const formatTime = (minutes: number): string => {
    if (minutes < 60) {
      return `${Math.round(minutes)}分钟`
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = Math.round(minutes % 60)
      return remainingMinutes === 0 ? `${hours}小时` : `${hours}小时${remainingMinutes}分钟`
    } else {
      const days = Math.floor(minutes / 1440)
      const remainingHours = Math.floor((minutes % 1440) / 60)
      return `${days}天${remainingHours > 0 ? `${remainingHours}小时` : ''}`
    }
  }

  return {
    distanceInKm,
    walkTime: formatTime(walkMinutes),
    bikeTime: formatTime(bikeMinutes),
    driveTime: formatTime(driveMinutes),
  }
}

//防抖处理
let favTimeout: ReturnType<typeof setTimeout> | null = null
const hanldeAddFav = async (storeId) => {
  if (favTimeout) clearTimeout(favTimeout)
  favTimeout = setTimeout(async () => {
    try {
      await userFavAdd({
        appId: import.meta.env.VITE_APPID,
        pid: shopId.value,
        storeId: storeId,
        type: 10,
      })
      paging.value.refresh()
    } catch (error) {}
  }, 300)
}

// uni.reLaunch({ url: '/pages/index/index' })

const goGoodsDetail = (item: Api.Home.GoodItem) => {
  // if (item?.productStock <= 0) return
  uni.navigateTo({
    url: `/pages-sub/goods/detail/index?id=${item?.productId}&storeId=${storeId.value ?? ''}&from=store`,
  })
}

const goSupplierDetail = (id: number, storeId: any) => {
  uni.navigateTo({
    url: `/pages-sub/home/<USER>/supplier/detail/index?shopId=${id}&storeId=${storeId}`,
  })
}
const hanleGoHome = () => {
  if (fromRoute.value === 'store') {
    uni.reLaunch({ url: '/pages/index/index' })
  } else {
    uni.navigateBack({ delta: 1 })
  }
}
</script>

<style lang="scss" scoped>
//
:deep(.wd-input) {
  background: none !important;
}
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden pb-30rpx box-border h-full flex flex-col">
    <div
      class="bgTop"
      :style="{
        paddingTop: menuButtonInfo.top + 'px',
        backgroundSize: 'cover',
        backgroundPosition: 'top',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'scroll',
      }"
    >
      <div
        class="flex items-center of-hidden box-border"
        :style="{
          height: menuButtonInfo.height + 'px',
        }"
      ></div>
    </div>
    <scroll-view scroll-y enable-flex class="main-scroll flex flex-col flex-1">
      <!-- pt60rpx -->
      <div
        :style="{
          backgroundSize: 'cover',
          backgroundPositionY: `-${menuButtonInfo.top + menuButtonInfo.height}px`,
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'scroll',
        }"
        class="bgTop box-border w-full"
      >
        <div class="profile box-border pl40rpx flex items-center justify-between">
          <view @click="handleUserProfile" class="flex items-center gap-10px img">
            <wd-img
              :width="60"
              :height="60"
              radius="50%"
              mode="aspectFill"
              :src="
                userStore?.userInfo?.avatarUrl
                  ? userStore?.userInfo?.avatarUrl
                  : 'https://file.shanqianqi.com/image/2025/06/06/5e92b4f0175641d5ac8c93adb2de8175.png'
              "
            ></wd-img>
            <view v-if="userStore.isLogined" class="flex flex-col justify-start text-#ffffff;">
              <view class="text-36rpx fw-500">{{ userStore?.userInfo?.nickName }}</view>
              <view class="text-28rpx fw-400">{{ userStore?.userInfo?.mobile }}</view>
            </view>
            <view v-else class="text-28rpx text-#ffffff;">请点击授权</view>
          </view>
        </div>

        <view class="w-full pt20rpx px20rpx box-border">
          <!-- 钱包 -->
          <view class="box-border w-full bg-#FFECDF rounded-20rpx">
            <div class="w-full flex items-center justify-between w-full h-150rpx rounded-15rpx">
              <div
                v-for="item in topSetting?.data"
                :key="item?.linkUrl"
                class="flex-1 flex items-center"
              >
                <div
                  v-if="item?.isVisible === 'Y'"
                  @click="gotoPage(item?.linkUrl)"
                  class="flex-1 flex flex-col justify-center items-center h-full gap-10rpx text-24rpx"
                >
                  <div class="flex items-center gap-6rpx">
                    <image class="w52rpx h52rpx" :src="item?.imgUrl"></image>
                    <div>{{ item?.text }}</div>
                  </div>
                  <div v-if="userStore?.userInfo">
                    {{ userStore?.userInfo?.[topMap[item?.text]] }}
                  </div>
                  <div v-else>0</div>
                </div>
              </div>

              <!-- <div
                @click="gotoPage('/pages-sub/user/goodBean/myGoodBeanWallet')"
                class="flex-1 flex flex-col justify-center items-center h-full gap-15rpx text-24rpx"
              >
                <div class="flex items-center gap-6rpx">
                  <image class="w52rpx h52rpx" src="@/static/icon/goodBean.png"></image>
                  <div>善豆</div>
                </div>
                <div v-if="userStore?.userInfo">{{ userStore?.userInfo?.points }}</div>
                <div v-else>0</div>
              </div>
              <div
                @click="gotoPage('/pages-sub/user/pints/pints')"
                class="flex-1 flex flex-col justify-center items-center h-full gap-15rpx text-24rpx"
              >
                <div class="flex items-center gap-6rpx">
                  <image class="w52rpx h52rpx" src="@/static/icon/points.png"></image>
                  <div>积分</div>
                </div>
                <div v-if="userStore?.userInfo">{{ userStore?.userInfo?.credits }}</div>
                <div v-else>0</div>
              </div>
              <div
                @click="gotoPage('/pages-sub/user/redEnvelopes/index')"
                class="flex-1 flex flex-col justify-center items-center h-full gap-15rpx text-24rpx"
              >
                <div class="flex items-center gap-6rpx">
                  <image class="w52rpx h52rpx" src="@/static/icon/redEnvelopes.png"></image>
                  <div>红包</div>
                </div>
                <div v-if="userStore?.userInfo">{{ userStore?.userInfo?.redPacket }}</div>
                <div v-else>0</div>
              </div> -->
            </div>
          </view>

          <!-- 我的服订单 -->
          <view class="w-full pt-20rpx">
            <view class="">
              <wd-cell
                size="large"
                title=""
                value=""
                :is-link="true"
                @click="gotoPage('/pages-sub/myOrder/index?dataType=all')"
                custom-class="!bg-white !rounded-tl-20rpx !rounded-tr-20rpx"
              >
                <template #title>
                  <div class="text-35rpx text-#333333 font-bold">我的订单</div>
                </template>
                <template #default>
                  <div class="text-25rpx text-#999999">更多</div>
                </template>
              </wd-cell>

              <view
                class="bg-white flex items-center w-full py-20rpx rounded-bl-20rpx rounded-br-20rpx"
              >
                <view
                  v-for="(ele, index) in orderItems"
                  :key="index"
                  @click="gotoPage(ele.url)"
                  class="flex-1 gap-10rpx flex flex-col items-center"
                >
                  <view class="userOrderCount">
                    <wd-img width="40rpx" height="40rpx" mode="aspectFill" :src="ele?.icon" />
                    <template v-if="userStore?.orderCount">
                      <text
                        class="dot"
                        v-if="
                          userStore?.orderCount?.[ele.pop] != null &&
                          userStore?.orderCount?.[ele.pop] > 0
                        "
                      >
                        {{ userStore?.orderCount?.[ele.pop] }}
                      </text>
                    </template>
                  </view>
                  <text class="text-24rpx text-#000000">{{ ele.name }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </div>
      <!-- </template> -->

      <!-- 我的服务 -->
      <view class="p-20rpx box-border">
        <wd-cell size="large" title="" value="" custom-class="!rounded-tl-20rpx !rounded-tr-20rpx">
          <template #title>
            <div class="text-35rpx text-#333333 font-bold">我的服务</div>
          </template>
        </wd-cell>

        <view
          class="bg-white grid grid-cols-4 gap-15rpx w-full py-20rpx rounded-bl-20rpx rounded-br-20rpx"
        >
          <view
            v-for="item in myServiceSetting?.data"
            :key="item?.linkUrl"
            @click="goServicePage(item)"
            class="gap-10rpx flex flex-col items-center"
          >
            <wd-img width="40rpx" height="40rpx" mode="aspectFill" :src="item?.imgUrl" />
            <text class="text-24rpx text-#000000">{{ item?.text }}</text>
          </view>
          <!-- <view
            @click="gotoPage('/pages-sub/user/receivingAddress/receivingAddress')"
            class="gap-10rpx flex flex-col items-center"
          >
            <wd-img
              width="40rpx"
              height="40rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/09/bd5dd3bdb79b49d89fb09923a79132de.png"
            />
            <text class="text-24rpx text-#000000">收货地址</text>
          </view>
          <view
            @click="gotoPage('/pages-sub/user/agent/agent')"
            class="gap-10rpx flex flex-col items-center"
          >
            <wd-img
              width="40rpx"
              height="40rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/06/61c3f454a45a48fba528941b7456a3ba.png"
            />
            <text class="text-24rpx text-#000000">我的推广</text>
          </view>
          <view
            @click="gotoPage('/pages-sub/user/shop/shop')"
            class="gap-10rpx flex flex-col items-center"
          >
            <wd-img
              width="40rpx"
              height="40rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/06/7464d202c41645e4ad3fd42d98dd0c3e.png"
            />
            <text class="text-24rpx text-#000000">商户管理</text>
          </view>
          <view
            @click="gotoPage('/pages-sub/user/footPrint/index')"
            class="gap-10rpx flex flex-col items-center"
          >
            <wd-img
              width="40rpx"
              height="40rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/06/f2070cc0d2d9476bb6f36a53cdc4744f.png"
            />
            <text class="text-24rpx text-#000000">我的足迹</text>
          </view>
          <view
            @click="gotoPage('/pages-sub/user/collection/index')"
            class="gap-10rpx flex flex-col items-center"
          >
            <wd-img
              width="40rpx"
              height="40rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/06/5a428ac175b040eeb5ba49cc6400a4fc.png"
            />
            <text class="text-24rpx text-#000000">我的收藏</text>
          </view>
          <view @click="handleGoMessage" class="gap-10rpx flex flex-col items-center">
            <wd-img
              width="40rpx"
              height="40rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/06/b3bcbf982dda4ebda9ea2a734043f2f6.png"
            />
            <text class="text-24rpx text-#000000">平台客服</text>
          </view>
          <view
            class="gap-10rpx flex flex-col items-center"
            @click="gotoPage('/pages-my/userEvaluate/userEvaluate')"
          >
            <wd-img
              width="40rpx"
              height="40rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/09/45b4293c12d74905a7b5bbbc1b774fb2.png"
            />
            <text class="text-24rpx text-#000000">我的评价</text>
          </view> -->
        </view>
      </view>
      <!-- <template #top class="px20rpx pb-20rpx">
        <view
          class="box-border w-full flex bg-white items-center p-t-20rpx p-l-30rpx rounded-tl-15rpx rounded-tr-15rpx text-35rpx text-#333333 font-bold"
        >
          常看推荐
        </view>
        <div class="flex flex-col">
          <div class="box-border h-80rpx">
            <wd-tabs
              custom-class="!rounded-15rpx !overflow-hidden"
              autoLineWidth
              v-model="tab"
              color=""
              inactiveColor=""
            >
              <block v-for="ele in 10" :key="ele">
                <wd-tab :title="`标签${ele}`"></wd-tab>
              </block>
            </wd-tabs>
          </div>
        </div>
      </template> -->

      <z-paging
        ref="paging"
        v-model="dataList"
        :default-page-size="20"
        :refresher-enabled="false"
        :show-loading-more-no-more-view="false"
        :fixed="false"
        use-page-scroll
        class="flex-1"
        @query="queryList"
      >
        <div v-if="dataList.length" class="box-border px20rpx">
          <view
            class="box-border w-full flex items-center py20rpx p-l-30rpx text-35rpx text-#333333 font-bold"
          >
            常看推荐
          </view>
          <div class="grid grid-cols-2 gap-10px">
            <div
              v-for="item in dataList"
              :key="item.productId"
              @click="() => goGoodsDetail(item.productId)"
              class="flex flex-col bg-#ffffff shadow-[0rpx_4rpx_8rpx_0rpx_rgba(0,0,0,0.02)] rd-20rpx of-hidden box-border"
            >
              <div class="relative">
                <wd-img
                  :src="item?.productImage"
                  width="100%"
                  height="460rpx"
                  mode="aspectFill"
                ></wd-img>
                <div
                  v-if="item?.productStock <= 0"
                  class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center pointer-events-none"
                >
                  <span>售罄</span>
                </div>
              </div>

              <div class="w-full p-20rpx box-border grid grid-cols-1">
                <div class="text-24rpx text-#333333 line-clamp-1 font-600">
                  {{ item?.productName }}
                </div>
                <!-- <div class="text-20rpx text-#FF7D26 font-500 mt4px mb12rpx">超长续航蓝牙耳机热卖榜</div> -->
                <div class="flex items-baseline gap-x-10px text-#FF7D26">
                  <div class="flex items-baseline">
                    <span class="text-20rpx font-500">¥</span>
                    <span class="text-40rpx font-bold">
                      {{ item?.productPrice?.split('.')?.[0] }}
                    </span>
                    <span class="text-24rpx font-bold">
                      .{{ item?.productPrice?.split('.')?.[1] }}
                    </span>
                    <span
                      v-if="item?.extraRedPocketPrice && item?.extraRedPocketPrice !== '0.00'"
                      class="text-#FF7D26 text-20rpx fw-400"
                    >
                      + {{ item?.extraRedPocketPrice }}红包
                    </span>
                  </div>
                  <!-- <span class="text-20rpx text-#666666">
                    已售{{
                      item?.productSales < 10000
                        ? '1万'
                        : Math.round(item.productSales / 10000) + '万'
                    }}
                  </span> -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <template #loadingMoreNoMore>
          <div class="p20rpx">没有更多了</div>
        </template> -->
      </z-paging>
    </scroll-view>
    <!-- <wd-img-cropper
      v-model="show"
      :img-src="src"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    ></wd-img-cropper> -->
  </view>
</template>

<script lang="ts" setup>
import { fetchRecommendProduct, fetchIndexGoods, fetchRecGoodsDetail } from '@/service'

import { useUserStore, useUserSettingStore } from '@/store'
import { doLogin, getPlatform } from '@/utils'

const userStore = useUserStore()
const userSettingStore = useUserSettingStore()

const topMap = {
  余额: 'balance',
  善豆: 'points',
  积分: 'credits',
  红包: 'redPacket',
}

defineOptions({
  name: 'My',
})

const menuButtonInfo = ref({
  height: 32,
  top: 50,
  width: 0,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const { data: settingData } = useRequest(
  () =>
    fetchIndexGoods({
      source: getPlatform(),
    }),
  { immediate: true },
)

const topSetting = computed<Api.Home.PageItems>(() => {
  const originData = settingData?.value?.page?.items?.find((ele) => ele?.name === '我的红包')

  return { ...originData, data: originData?.data?.filter((item) => item?.isVisible === 'Y') }
})

const myServiceSetting = computed<Api.Home.PageItems>(() => {
  const originData = settingData?.value?.page?.items?.find((ele) => ele?.name === '我的服务')

  return { ...originData, data: originData?.data?.filter((item) => item?.isVisible === 'Y') }
})

onShow(() => {
  userStore?.fetchUserCenterConfig()
  userStore?.isLogined && userSettingStore.fetchUserSetting()
})

interface OrderItem {
  name: string
  url: string
  pop: string
  icon: string
}

// 修改变量名为更清晰的命名，并调整类型
const orderItems = ref<OrderItem[]>([
  {
    name: '待付款',
    url: '/pages-sub/myOrder/index?dataType=payment&sourceActive=全部订单',
    pop: 'payment',
    icon: 'https://file.shanqianqi.com/image/2025/06/06/d1cb79972eb6498f9657afa4d28f17f6.png',
  },
  {
    name: '待发货',
    url: '/pages-sub/myOrder/index?dataType=delivery&sourceActive=全部订单',
    pop: 'delivery',
    icon: 'https://file.shanqianqi.com/image/2025/06/06/300b8ae888ca412696f28b3aa6f4cad3.png',
  },
  {
    name: '待收货',
    url: '/pages-sub/myOrder/index?dataType=received&sourceActive=全部订单',
    pop: 'received',
    icon: 'https://file.shanqianqi.com/image/2025/06/06/729d9f00ee8348c2bcb9649efca32ab7.png',
  },
  {
    name: '待评价',
    url: '/pages-sub/myOrder/index?dataType=comment&sourceActive=全部订单',
    pop: 'comment',
    icon: 'https://file.shanqianqi.com/image/2025/06/06/b65d067c9a6840abb4a56a97bbca1fad.png',
  },
  {
    name: '退款/售后',
    url: '/pages-sub/orderInvoice/invoiceCenter',
    pop: 'refund',
    icon: 'https://file.shanqianqi.com/image/2025/06/06/1b54cff7b0084b84a90809b94a7b1bd7.png',
  },
])

const paging = ref()
const dataList = ref<Api.Home.GoodItem[]>([])

const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await fetchRecommendProduct({
      location: 10,
    })

    paging?.value?.complete(data?.list)
  } catch (err) {
    paging?.value?.complete(false)
  }
}

const goGoodsDetail = (id: number) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}` })
}

const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}

const goServicePage = async (item: any) => {
  console.log('🚀 ~ goServicePage ~ item:', item)
  if (item?.text === '平台客服') {
    const { data } = await fetchRecGoodsDetail({ appId: 10001, productId: '0' })
    console.log('🚀 ~ goServicePage ~ data:', data)
    uni.navigateTo({
      url: `/pages/message/chat/index?userId=${data?.serviceUser?.serviceUserId}&shopSupplierId=${data?.serviceUser?.shopSupplierId}&nickName=${data?.serviceUser?.nickName}`,
    })
  } else {
    uni.navigateTo({ url: item?.linkUrl })
  }
}

const handleGoMessage = () => {
  uni.redirectTo({ url: '/pages/message/index' })
}

const handleUserProfile = () => {
  if (userStore?.isLogined) {
    uni.navigateTo({ url: '/pages-sub/user/user-detail/index' })
  } else {
    doLogin()
    // uni.navigateTo({ url: '/pages-sub/login/login' })
  }
}
</script>

<style lang="scss" scoped>
.bgTop {
  background: url('https://file.shanqianqi.com/image/2025/06/20/bac7e1bbd69644e79820e62012d74d9c.png')
    no-repeat;

  background-size: contain;
}
//
.userOrderCount {
  position: relative;
}
.dot {
  position: absolute;
  top: -10rpx;
  right: -8rpx;
  height: 25rpx;
  min-width: 25rpx;
  padding: 4rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  background: linear-gradient(180deg, #fc4133, #ff7a04);
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bind_btn {
  min-width: 0 !important;
}
</style>

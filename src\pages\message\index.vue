<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full bg-#F6F6F6 grid grid-rows-[1fr]">
    <z-paging
      ref="paging"
      empty-view-text="没有聊天记录哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      :style="{ paddingBottom: `${safeBottom}rpx` }"
      :fixed="false"
    >
      <template #top>
        <!-- 最外层 -->
        <div
          class="w-full grid grid-cols-1 gap-y-40rpx"
          :style="{
            paddingTop: menuButtonInfo.top + 'px',
          }"
        >
          <!-- 导航栏 -->
          <div
            class="w-full px24rpx box-border flex items-center gap-x-5px"
            :style="{
              paddingRight: `${menuButtonInfo.width * 2 + 30}rpx`,
              height: menuButtonInfo.height + 'px',
            }"
          >
            <div class="flex items-center of-hidden">
              <div class="flex items-center gap-x-2px text-#333333">
                <span class="text-36rpx">消息</span>
                <span v-if="msgNoRead" class="text-28rpx">({{ msgNoRead }})</span>
              </div>
            </div>
            <!-- <div
              class="flex items-center h-full flex-1 bg-#ffffff of-hidden box-border pl-20rpx pr-1px gap-x-10rpx rd-30rpx"
            >
              <wd-input
                placeholder="搜索聊天记录"
                inputmode="search"
                no-border
                clearable
                v-model="msgSearch"
                confirm-type="search"
                @confirm="handleSearch"
                @clear="handleSearch"
                placeholderClass="text-#999999"
                custom-class="search !flex-1 !text-#999999 !h-full !flex !items-center !text-24rpx"
              ></wd-input>
              <wd-button
                @click="handleSearch"
                custom-class="!w-108rpx !p-unset !min-w-unset !px30rpx !py14rpx !box-border !rd-30rpx !h-58rpx"
              >
                搜索
              </wd-button>
            </div> -->
          </div>
          <!-- 分类 -->
          <!-- <div class="grid grid-cols-5 px60rpx box-border">
            <div
              v-for="item in iconImageArray"
              :key="item?.img"
              class="flex flex-col items-center gap-y-8rpx"
            >
              <wd-img :src="item?.img" width="80rpx" height="80rpx" mode="aspectFill"></wd-img>
              <span class="text-28rpx text-#333333">{{ item?.text }}</span>
            </div>
          </div> -->
        </div>
      </template>
      <div class="grid grid-cols-1 gap-y-20rpx p40rpx box-border">
        <div
          @click="goChat(item?.serviceUserId, item?.shopSupplierId, item?.nickName)"
          v-for="item in dataList"
          :key="item?.createTime"
          class="flex gap-x-14rpx"
        >
          <wd-img
            :src="item?.serviceLogo"
            width="80rpx"
            radius="20rpx"
            height="80rpx"
            mode="aspectFill"
          ></wd-img>
          <div class="flex-1 of-hidden flex flex-col gap-y-2px">
            <div class="flex items-center justify-between">
              <span class="text-28rpx text-#333333">{{ item?.nickName }}</span>
              <span class="text-24rpx text-#666666">
                {{ formatChatTime(item?.newMessage?.updateTime ?? item?.newMessage?.createTime) }}
              </span>
            </div>
            <div
              class="grid grid-cols-[1fr_auto] gap-x-20rpx pb20rpx border-b border-b-solid border-b-#DDDDDD w-full"
            >
              <div
                v-if="
                  item?.newMessage && item?.newMessage.content && item?.newMessage?.msgType === 0
                "
                class="text-24rpx text-#999999 line-clamp-1 flex-1"
              >
                {{ item?.newMessage?.content }}
              </div>
              <div
                v-if="item?.newMessage && item?.newMessage?.msgType === 1"
                class="text-24rpx text-#999999 line-clamp-1 flex-1"
              >
                [图片]
              </div>
              <div
                v-if="item?.newMessage && item?.newMessage?.msgType === 2"
                class="text-24rpx text-#999999 line-clamp-1 flex-1"
              >
                [商品]
              </div>
              <div
                v-if="item?.newMessage && item?.newMessage?.msgType === 3"
                class="text-24rpx text-#999999 line-clamp-1 flex-1"
              >
                [订单]
              </div>
              <div
                v-if="item?.noReadCount"
                class="bg-#FF0700 rd-1/2 w36rpx h36rpx text-24rpx aspect-square box-border flex items-center justify-center text-#FFFFFF"
              >
                {{ item?.noReadCount }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </z-paging>
    <wd-floating-panel
      :custom-style="`bottom:${safeBottom}rpx`"
      safeAreaInsetBottom
      :contentDraggable="false"
    >
      <div class="box-border px20rpx">
        <view
          class="box-border w-full flex items-center justify-center py20rpx p-l-30rpx text-35rpx text-#333333 font-bold"
        >
          {{ recommendData?.recommendName }}
        </view>
        <div class="grid grid-cols-2 gap-10px">
          <div
            v-for="item in recommendList"
            :key="item.productId"
            @click="() => goGoodsDetail(item.productId)"
            class="flex flex-col bg-#ffffff shadow-[0rpx_4rpx_8rpx_0rpx_rgba(0,0,0,0.02)] rd-20rpx of-hidden box-border"
          >
            <wd-img
              :src="item?.productImage"
              width="100%"
              height="460rpx"
              mode="aspectFill"
            ></wd-img>
            <div class="w-full p-20rpx box-border grid grid-cols-1">
              <div class="text-24rpx text-#333333 line-clamp-1 font-600">
                {{ item?.productName }}
              </div>
              <div class="flex items-baseline gap-x-10px text-#FF7D26">
                <div class="flex items-baseline">
                  <span class="text-20rpx font-500">¥</span>
                  <span class="text-40rpx font-bold">
                    {{ item?.productPrice?.split('.')?.[0] }}
                  </span>
                  <span class="text-24rpx font-bold">
                    .{{ item?.productPrice?.split('.')?.[1] }}
                  </span>
                  <!-- <span
                    v-if="item?.extraRedPocketPrice && item?.extraRedPocketPrice !== '0.00'"
                    class="text-#FF7D26 text-20rpx fw-400"
                  >
                    + {{ item?.extraRedPocketPrice }}红包
                  </span> -->
                </div>
                <!-- <span class="text-20rpx text-#666666">
                    已售{{
                      item?.productSales < 10000
                        ? '1万'
                        : Math.round(item.productSales / 10000) + '万'
                    }}
                  </span> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </wd-floating-panel>
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import isToday from 'dayjs/plugin/isToday'
import { fetchChatList, fetchRecommendProduct } from '@/service'
import { useMessageStore } from '@/store'
import { getNoReadCounts } from '@/utils'

const messageStore = useMessageStore()

const msgNoRead = computed(() => getNoReadCounts(messageStore?.messageData ?? []) || 0)

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()
const safeBottom = computed(() => (bottom ? bottom * 2 + 100 : 130))

const menuButtonInfo = ref({
  height: 32,
  top: 20,
  width: 0,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const iconImageArray = [
  {
    img: 'https://file.shanqianqi.com/image/2025/06/04/7201052d52ac477580c068f61773a970.png',
    text: '全部',
  },
  {
    img: 'https://file.shanqianqi.com/image/2025/06/04/402c5388c3b442a3a8bff3ca755f3fe7.png',
    text: '客服',
  },
  {
    img: 'https://file.shanqianqi.com/image/2025/06/04/12c55a59f08746cd96aa0042c6427f67.png',
    text: '物流',
  },
  {
    img: 'https://file.shanqianqi.com/image/2025/06/04/1511072e045248e0a92168f7a774a4ff.png',
    text: '提醒',
  },
  {
    img: 'https://file.shanqianqi.com/image/2025/06/04/8a868cf7f8e34856bd458f8b609385b9.png',
    text: '优惠',
  },
]
onShow(async () => {
  paging?.value?.reload()
})
// const socketUrl = ref('')
const paging = ref()
const dataList = ref<Api.Message.ChatListItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await fetchChatList()
      await nextTick()
      // if (socketUrl.value === '') {
      //   socketUrl.value = data?.url
      //   socketInit()
      // }
      paging?.value?.complete(data?.list)

      messageStore?.setMessageData(data?.list)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

dayjs.extend(isToday)
const formatChatTime = (timeStr: string) => {
  const time = dayjs(timeStr)

  if (time.isToday()) {
    return time.format('HH:mm') // 如果是今天，显示小时分钟
  } else {
    const weekMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    return weekMap[time.day()]
  }
}

const { data: recommendData } = useRequest(
  () =>
    fetchRecommendProduct({
      location: 10,
    }),
  { immediate: true },
)
const recommendList = computed(() => recommendData.value?.list)

const goGoodsDetail = (id: number) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}` })
}

const msgSearch = ref('')

const handleSearch = () => {}

// const socketTask = ref(null)
// const isOpenSocket = ref(false)
// const intervalId = ref(null) // 心跳定时器
// const isLive = ref(false) //初次进入

// const send = (data) => {
//   if (isOpenSocket.value) {
//     socketTask.value.send({
//       data: data,
//       success() {},
//     })
//   } else {
//     console.log('处于离线状态')
//     socketTask.value = null
//     isOpenSocket.value = false
//     clearInterval(intervalId.value)
//     socketInit()
//   }
// }

// const startHeart = (isLoop?: boolean) => {
//   let data = JSON.stringify({
//     type: 'ping',
//     sendType: 2,
//   })
//   if (isLoop) {
//     intervalId.value = setInterval(() => {
//       console.log('发送心跳')
//       send(data)
//     }, 10000)
//   } else {
//     console.log('发送心跳')
//     send(data)
//   }
// }

// const socketInit = () => {
//   if (isOpenSocket.value) {
//     return
//   }
//   socketTask.value = null
//   socketTask.value = uni.connectSocket({
//     url:
//       socketUrl.value +
//       '/socket?user_id=' +
//       userStore?.userInfo?.userId +
//       '&usertype=user' +
//       '&to=0',
//     success() {
//       console.log('Socket连接成功！')
//     },
//   })
//   // 消息的发送和接收必须在正常连接打开中,才能发送或接收【否则会失败】
//   socketTask.value.onOpen(() => {
//     console.log('WebSocket连接正常打开中...！')
//     isOpenSocket.value = true
//     // 开始发送心跳
//     startHeart()
//     // 注：只有连接正常打开中 ，才能正常收到消息
//     socketTask.value.onMessage((res) => {
//       console.log('收到服务器内容：')
//       console.log(res)
//       const newdata = JSON.parse(res.data)
//       console.log('🚀 ~ newdata:', newdata)
//     })
//   })
//   // 这里仅是事件监听【如果socket关闭了会执行】
//   socketTask.value.onClose(() => {
//     console.log('已经被关闭了')
//     //重连机制
//     if (!isLive.value) {
//       socketTask.value = null
//       isOpenSocket.value = false
//       clearInterval(intervalId.value)
//       !isLive.value && socketInit()
//     }
//   })
// }

// const closeSocket = () => {
//   let data = JSON.stringify({
//     type: 'close',
//     appId: 10001,
//     supplier_user_id: 0,
//     user_id: userStore?.userInfo?.userId,
//     shop_supplier_id: 0,
//     msg_type: 2,
//   })
//   send(data)
//   socketTask.value.close({
//     success(res) {
//       console.log('关闭成功', res)
//     },
//     fail(err) {
//       console.log('关闭失败', err)
//     },
//   })
//   socketTask.value = null
//   isOpenSocket.value = false
//   clearInterval(intervalId.value)
// }

// onBeforeUnmount(() => {
//   isLive.value = false
//   closeSocket()
// })

const goChat = (serviceUserId: number, shopSupplierId: number, nickName: string) => {
  if (shopSupplierId === serviceUserId) return

  uni.navigateTo({
    url: `/pages/message/chat/index?userId=${serviceUserId}&shopSupplierId=${shopSupplierId}&nickName=${nickName}`,
  })
}
</script>

<style lang="scss" scoped>
//
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden pb-20 box-border h-full">
    <l-fab axis="xy" v-model:offset="cartOffset" magnetic="x">
      <div
        @click="goCart"
        class="rd-1/2 flex items-center justify-center z-1000 transition-all-300"
      >
        <wd-badge :modelValue="getTotalProductCount(cartStore?.cartData?.productList ?? [])">
          <wd-img
            src="https://file.shanqianqi.com/image/2025/06/13/66fca48b23e545ae920896d2d3534529.png"
            width="100rpx"
            height="100rpx"
            mode="aspectFill"
          ></wd-img>
          <!-- <div class="i-carbon-shopping-cart text-#333333 text-40rpx" /> -->
        </wd-badge>
      </div>
    </l-fab>

    <div
      class="flex flex-col pt64rpx pb20rpx box-border"
      :style="{
        background: themeStore?.navColor,
        paddingTop: menuButtonInfo.top + 'px',
      }"
    >
      <!-- <HomeSubNavBar
        @update-input-value="handleSearchGoodName"
        @update-location="handleLocationChange"
        placeholder="搜索商品"
        left-title="商城"
      /> -->
      <div
        :style="{
          height: menuButtonInfo.height + 'px',
          marginRight: `${menuButtonInfo.width * 2 + 10}rpx`,
        }"
        class="px26rpx box-border flex items-center gap-x-20px"
      >
        <div
          @click="handleBack"
          class="flex items-center"
          :style="{
            height: menuButtonInfo.height + 'px',
          }"
        >
          <div class="i-carbon-chevron-left text-20px text-white font-bold"></div>
          <span class="text-20px text-white font-bold">商城</span>
        </div>
      </div>
      <div
        class="px26rpx box-border mt20rpx flex items-center"
        :style="{
          background: themeStore?.navColor,
        }"
      >
        <view
          class="flex-1 flex items-center h-full of-hidden box-border gap-x-10rpx rd-30rpx bg-[rgba(255,255,255,0.5)]"
        >
          <wd-input
            placeholder="搜索商品"
            inputmode="search"
            no-border
            v-model="searchVal"
            confirm-type="search"
            @confirm="handleSearchGoodName"
            placeholderClass="text-#999999"
            custom-class="rounded-30rpx !flex-1 !text-#999999 !h-64rpx !flex !items-center !text-24rpx"
          ></wd-input>
          <wd-button
            @click="handleSearch"
            custom-class="!w-108rpx !p-unset !min-w-unset !px30rpx !py14rpx !box-border !rd-30rpx !h-58rpx"
          >
            搜索
          </wd-button>
        </view>
      </div>
    </div>
    <!-- <div class="tabs"> -->
    <scroll-view class="tabs" scroll-x :scroll-into-view="activeTabId" scroll-with-animation>
      <view
        v-for="item in categoryList"
        :key="item.categoryId"
        :class="tab == item.categoryId ? 'activeTab tab' : 'tab'"
        @click="switchTab(item.categoryId)"
        :id="'tab-' + item.categoryId"
      >
        <text>{{ item.name }}</text>
      </view>
    </scroll-view>

    <div class="content h-full flex">
      <view class="overflow-y-auto pb-300rpx">
        <!-- 侧边栏 -->
        <wd-sidebar v-model="sidebarActive" @change="handleChange">
          <wd-sidebar-item
            v-for="item in categoryChildrenList"
            :key="item.categoryId"
            :value="item.categoryId"
            :label="item.name"
          />
        </wd-sidebar>
      </view>
      <z-paging
        ref="paging"
        v-model="goodsList"
        :default-page-size="10"
        :fixed="false"
        :auto="false"
        class="flex-1 bg-white"
        @query="queryList"
      >
        <div class="flex">
          <div class="products flex-1 bg-white">
            <div
              class="products_item flex px20rpx pt20rpx pb30rpx gap10rpx box-border"
              v-for="(ele, index) in goodsList"
              :key="index"
              @click="goGoodsDetail(ele.productId)"
            >
              <div class="relative">
                <image
                  class="w160rpx h160rpx rounded-20rpx"
                  :src="ele.productImage"
                  mode=""
                ></image>
                <div
                  v-if="ele?.productStock <= 0"
                  class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center"
                >
                  <span>售罄</span>
                </div>
              </div>

              <div class="products_item_info flex-1 flex flex-col justify-between">
                <div class="products_info_name line-clamp-1 text-24rpx text-#333333 fw-500">
                  {{ ele.productName }}
                </div>
                <div class="flex items-center justify-between text-20rpx">
                  <div class="text-#666666 line-through">¥{{ ele.linePrice }}</div>
                  <div class="flex items-baseline">
                    <span class="text-#999999">预获积分</span>
                    <span class="text-#FF7D26">{{ ele?.scoreDeductionValue }}</span>
                  </div>
                </div>
                <div class="flex">
                  <view class="flex-1 gap6rpx">
                    <div class="text-#FF7D26">
                      <!-- 价格在第一行 -->
                      <div class="flex items-baseline">
                        <span class="text-30rpx font-bold leading-none">
                          {{ ele?.productPrice?.split('.')?.[0] }}
                        </span>
                        <span class="text-24rpx font-bold leading-none">
                          .{{ ele?.productPrice?.split('.')?.[1] }}
                        </span>
                        <span class="text-20rpx font-bold ml-1 leading-none">/ 件</span>
                        <!-- 当有红包且不为0时，红包信息显示在同一行 -->
                        <span
                          v-if="ele?.extraRedPocketPrice && ele?.extraRedPocketPrice != '0.00'"
                          class="text-#FF7D26 text-20rpx fw-400 leading-none"
                        >
                          +{{ ele?.extraRedPocketPrice }}红包
                        </span>
                        <!-- 当没有红包或红包为0时，优惠信息显示在同一行 -->
                        <span
                          v-if="
                            !ele?.extraRedPocketPrice ||
                            (ele?.extraRedPocketPrice === '0.00' && ele?.isShowBeanValue === 'Y')
                          "
                          class="text-#FF7D26 text-20rpx fw-400 flex items-center leading-none"
                        >
                          <span class="mx-0.5">-</span>
                          <span>{{ ele?.beanDeductionValue }}</span>
                          <image
                            class="w28rpx h28rpx"
                            src="https://file.shanqianqi.com/image/2025/06/24/e50f41403ded42a18709e739b74e8b91.png"
                          ></image>
                        </span>
                      </div>
                      <!-- 当有红包且不为0时，优惠信息换行显示 -->
                      <div
                        v-if="
                          ele?.extraRedPocketPrice &&
                          ele?.extraRedPocketPrice != '0.00' &&
                          ele?.isShowBeanValue === 'Y'
                        "
                        class="text-#FF7D26 text-20rpx fw-400 flex items-center mt-1 leading-none"
                      >
                        <span class="leading-none">-{{ ele?.beanDeductionValue }}</span>
                        <image
                          class="w28rpx h28rpx"
                          src="https://file.shanqianqi.com/image/2025/06/24/e50f41403ded42a18709e739b74e8b91.png"
                        ></image>
                      </div>
                    </div>
                  </view>
                  <div
                    @click.stop="goodsAddCart(ele)"
                    class="w-48rpx h48rpx rd-1/2 flex items-center justify-center bg-#FF7D26"
                  >
                    <div class="i-carbon-add text-#ffffff text-40rpx"></div>
                  </div>
                  <!-- <image
                    class="w48rpx h48rpx"
                    @click.stop="goodsAddCart(ele)"
                    src="https://file.shanqianqi.com/image/2025/06/24/fc15806e763b40f3811e8a782d628600.png"
                    mode=""
                  ></image> -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <template #empty>
          <div class="flex justify-center py200rpx bg-white text-#aaaaaa w-full h-full">
            暂无数据
          </div>
        </template>
      </z-paging>
      <wd-popup
        v-model="showPrePay"
        position="bottom"
        safe-area-inset-bottom
        lockScroll
        custom-style="min-height: 200px;max-height:600px"
        @close="handleClose"
      >
        <div class="grid grid-cols-1 p-30rpx box-border gap-y-10px">
          <div
            class="flex items-start gap-x-10px pb10px box-border border-b border-b-solid border-b-#eeeeee"
          >
            <wd-img
              width="180rpx"
              :src="productDetail?.detail?.image?.[0]?.filePath"
              height="180rpx"
              mode="aspectFit"
            ></wd-img>
            <div class="flex flex-col gap-y-10px">
              <div class="flex items-baseline text-#FF7D26">
                <span class="text-20rpx font-500">¥</span>
                <span class="text-40rpx font-bold">
                  {{ showPrice?.split('.')?.[0] }}
                </span>
                <span class="text-24rpx font-bold">.{{ showPrice?.split('.')?.[1] }}</span>
                <template v-if="productDetail?.detail?.specType === 20 && !specSkuId">
                  <span class="text-40rpx font-bold mx-5px">-</span>
                  <span class="text-20rpx font-500">¥</span>
                  <span class="text-40rpx font-bold">
                    {{ productDetail?.detail?.highPrice?.split('.')?.[0] }}
                  </span>
                  <span class="text-24rpx font-bold">
                    .{{ productDetail?.detail?.highPrice?.split('.')?.[1] }}
                  </span>
                </template>
              </div>
              <div class="text-24rpx text-#999999">
                库存：{{
                  productDetail?.detail?.specType === 20
                    ? productDetail?.specData?.specList?.find(
                        (spc) => spc?.specSkuId === `${specSkuId}`,
                      )?.productStock ?? '-'
                    : productDetail?.detail?.productStock ?? '-'
                }}件
              </div>
            </div>
          </div>

          <div
            v-if="productDetail?.detail?.specType === 20 && productDetail?.specData"
            class="grid grid-cols-1 gap-y-5px grid-auto-rows-min"
          >
            <div
              class="flex flex-col gap-y-5px"
              v-for="(item, index) in productDetail?.specData?.specAttr"
              :key="item?.groupName"
            >
              <span class="text-26rpx text-#333333">{{ item?.groupName }}</span>
              <div class="flex items-center gap-10px flex-wrap">
                <button
                  v-for="(ele, skuIndex) in item?.specItems"
                  :key="ele?.specValue"
                  @click="() => handleSpecChoose(ele, item?.groupName)"
                  :disabled="!canSelectGroup(index, skuIndex)"
                  class="custom-btn"
                  :class="[
                    selectedSpecs[item.groupName] === ele.itemId ? 'btn-primary' : 'btn-info',
                    !canSelectGroup(index, skuIndex) ? 'btn-disabled' : '',
                  ]"
                  :title="ele?.specValue"
                >
                  {{ ele?.specValue }}
                </button>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="text-24rpx text-#999999">数量</div>
            <wd-input-number
              v-model="buyNubmer"
              :min="1"
              :max="productDetail?.detail?.productStock"
            />
          </div>
          <wd-button @click="goConfirm" custom-class="!w-full" block>确认</wd-button>
        </div>
      </wd-popup>
      <!-- 购物车图标 -->
      <!-- <div class="addCart_box">
        <image
          class="addCartImg w96rpx h96rpx"
          @click="goCart"
          src="@/static/icon/cart.png"
          mode=""
        ></image>
        <span class="addCart_Total">
          {{ getTotalProductCount(cartStore?.cartData?.productList ?? []) }}
        </span>
      </div> -->
    </div>
  </view>
</template>

<script lang="ts" setup>
import { getTotalProductCount } from '@/utils'
import { useConfirmOrderStore } from '@/store/confirm-order'
import { useCartStore } from '@/store/cart'
import HomeSubNavBar from '@/components/HomeSubNavBar/index.vue'
import { useLocationStore } from '@/store'
import {
  addCart,
  fetchGoodsCategory,
  fetchRecGoodsDetail,
  fetchRecGoodsByCategoryld,
  fetchSelectProductListByCategoryId,
  getProductCategoryListBylsVisible,
} from '@/service'

import { useUserStore } from '@/store'

import { useThemeStore } from '@/store/theme'

const { windowHeight, windowWidth, safeAreaInsets } = uni.getSystemInfoSync()

// 获取胶囊按钮信息（小程序环境）
let menuButtonInfo_capsule = { width: 0, height: 0, top: 0, right: 0 }
try {
  // #ifdef MP-WEIXIN
  menuButtonInfo_capsule = uni.getMenuButtonBoundingClientRect()
  // #endif
} catch (e) {
  console.log('获取胶囊信息失败:', e)
}

const cartIconSize = uni.upx2px(100) // 购物车图标大小
const badgeSize = 20 // 徽标大小估算
const initialX = windowWidth - cartIconSize - badgeSize - 10 // 确保徽标不被遮盖
const cartOffset = ref([initialX, windowHeight / 1.5])

// 监听购物车位置变化，添加胶囊避让逻辑
let isAdjusting = false
watch(
  cartOffset,
  (newOffset) => {
    console.log('购物车位置变化:', newOffset)
    if (isAdjusting) return // 防止无限循环

    const [x, y] = newOffset
    let adjustedX = x
    let adjustedY = y
    let needsAdjustment = false

    // 限制 X 轴范围，确保徽标不被屏幕边缘遮盖
    const minX = badgeSize // 左边界：给徽标留出空间
    const maxX = windowWidth - cartIconSize - badgeSize // 右边界：图标大小 + 徽标空间

    if (x < minX) {
      adjustedX = minX
      needsAdjustment = true
    }

    if (x > maxX) {
      adjustedX = maxX
      needsAdjustment = true
    }

    // 完全避开胶囊区域（只在微信小程序中）
    // #ifdef MP-WEIXIN
    if (menuButtonInfo_capsule.width > 0) {
      // 定义胶囊禁区：更大的安全区域
      const safeMargin = 50 // 安全边距
      const capsuleLeft = windowWidth - menuButtonInfo_capsule.width - safeMargin
      const capsuleTop = 0 // 从屏幕顶部开始
      const capsuleBottom = menuButtonInfo_capsule.top + menuButtonInfo_capsule.height + safeMargin
      const capsuleRight = windowWidth // 到屏幕右边缘

      console.log('胶囊禁区检测:', {
        购物车位置: { x, y },
        胶囊禁区: {
          left: capsuleLeft,
          top: capsuleTop,
          bottom: capsuleBottom,
          right: capsuleRight,
        },
        是否在禁区: y >= capsuleTop && y <= capsuleBottom && x >= capsuleLeft,
      })

      // 完全禁止进入胶囊区域
      if (y >= capsuleTop && y <= capsuleBottom && x >= capsuleLeft) {
        console.log('🚨🚨🚨 进入胶囊禁区，强制移出!')

        // 策略1：优先移到胶囊左侧
        const leftSafeX = capsuleLeft - cartIconSize - 10

        // 策略2：如果左侧空间不够，移到胶囊下方
        const bottomSafeY = capsuleBottom + 20

        if (leftSafeX >= badgeSize) {
          // 左侧有足够空间，移到左侧
          adjustedX = leftSafeX
          adjustedY = Math.max(y, capsuleTop + 20) // 确保不会太靠近顶部
          console.log('📍 移到胶囊左侧:', { adjustedX, adjustedY })
        } else {
          // 左侧空间不够，移到下方
          adjustedX = Math.min(x, capsuleLeft - 20) // 稍微往左移一点
          adjustedY = bottomSafeY
          console.log('📍 移到胶囊下方:', { adjustedX, adjustedY })
        }

        needsAdjustment = true
      }

      // 额外检查：如果 Y 轴在胶囊高度范围内，X 轴不能超过胶囊左边界
      if (
        y >= menuButtonInfo_capsule.top - 20 &&
        y <= menuButtonInfo_capsule.top + menuButtonInfo_capsule.height + 20
      ) {
        if (x >= capsuleLeft) {
          console.log('� Y轴在胶囊高度范围，限制X轴')
          adjustedX = capsuleLeft - cartIconSize - 10
          needsAdjustment = true
        }
      }
    }
    // #endif

    // 如果需要调整，更新位置
    if (needsAdjustment) {
      isAdjusting = true
      nextTick(() => {
        cartOffset.value = [adjustedX, adjustedY]
        setTimeout(() => {
          isAdjusting = false
        }, 100)
      })
    }
  },
  { deep: true },
)

const themeStore = useThemeStore()
const menuButtonInfo = ref({
  width: 0,
  height: 32,
  top: 20,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const userStore = useUserStore()
const cartStore = useCartStore()
const locationStore = useLocationStore()

const confirmOrderStore = useConfirmOrderStore()
//加购
const productDetail = ref<Api.Home.GoodDetail>()
const showPrice = ref('')
const showPrePay = ref(false)
const buyNubmer = ref(1)
const chooseSpec = ref('')
const selectedSpecs = ref<Record<string, number>>({}) // 以 groupName 为 key，itemId 为值
const specSkuId = ref<string>()
const searchVal = ref('')
//是否搜索数据
const isSearch = ref(false)
const tab = ref(0)
let activeTabId = ref('tab-0')
const sidebarActive = ref<any>(0)
const categoryList = ref<Api.Home.GoodCategoryItem[]>([])
const categoryChildrenList = ref<any>([])

const goodsList = ref<any>([])
onLoad(async (e) => {
  await fetchData()
  if (e.categoryPid) {
    // tab.value = e.categoryPid
    switchTab(e.categoryPid)
  } else {
    //初始化选中tab
    tab.value = categoryList.value[0]?.categoryId
    activeTabId.value = `tab-${categoryList.value[0]?.categoryId}`
    sidebarActive.value = categoryChildrenList.value[0]?.categoryId
  }
  console.log(' tab.value', tab.value, sidebarActive.value)
})
onMounted(() => {
  // 添加调试信息
  console.log('胶囊避让调试信息:', {
    windowHeight,
    windowWidth,
    cartIconSize,
    badgeSize,
    initialX,
    menuButtonInfo_capsule,
    menuButtonInfo: menuButtonInfo.value,
    cartOffset: cartOffset.value,
  })

  // #ifdef MP-WEIXIN
  if (menuButtonInfo_capsule.width > 0) {
    console.log('胶囊禁区范围:', {
      capsuleLeft: windowWidth - menuButtonInfo_capsule.width - 20,
      capsuleTop: menuButtonInfo_capsule.top - 10,
      capsuleBottom: menuButtonInfo_capsule.top + menuButtonInfo_capsule.height + 10,
      capsuleRight: windowWidth,
    })

    // 测试：强制触发一次胶囊区域检测
    // setTimeout(() => {
    //   const capsuleLeft = windowWidth - menuButtonInfo_capsule.width - 30
    //   const testX = capsuleLeft + 10 // 确保在胶囊区域内
    //   const testY = menuButtonInfo_capsule.top + 10
    //   console.log('🧪 测试胶囊避让:', { testX, testY, capsuleLeft, shouldTrigger: true })
    //   cartOffset.value = [testX, testY]
    // }, 3000)
  }
  // #endif
})
const switchTab = (name) => {
  isSearch.value = false
  searchVal.value = ''
  tab.value = name
  activeTabId.value = `tab-${name}`

  let obj = categoryList.value.find((item) => item.categoryId == name)
  if (!obj) {
    categoryChildrenList.value = ''
    sidebarActive.value = 0
  } else {
    // 确保 children 是数组
    const children = Array.isArray(obj.children) ? obj.children : []
    // 构造新的数组：[当前分类, 所有子分类]
    // categoryChildrenList.value = [data, ...children]
    categoryChildrenList.value = [...children]

    sidebarActive.value =
      categoryChildrenList.value.length > 0 ? categoryChildrenList.value[0]?.categoryId : 0
  }

  paging.value?.reload()
}
const handleBack = () => uni.navigateBack()
// sideBar切换
const handleChange = ({ value, label }) => {
  isSearch.value = false
  sidebarActive.value = value
  paging?.value?.reload()
}
const handleSearchGoodName = (keyword: any) => {
  isSearch.value = true
  searchVal.value = keyword.value
  paging?.value?.reload()
}
const handleSearch = () => {
  isSearch.value = true
  paging?.value?.reload()
}
const canSelectGroup = (groupIndex: number, skuIndex: number) => {
  const specData = productDetail.value?.specData
  if (!specData) return false

  const currentItemId = specData.specAttr[groupIndex].specItems[skuIndex].itemId
  const specList = specData.specList || []

  // 一级规格
  if (groupIndex === 0) {
    return specList.some((sku) => {
      const skuIds = sku.specSkuId.split('_')
      return skuIds[0] == currentItemId && sku.productStock > 0
    })
  }

  // 多级规格
  const selectedIds: (string | number)[] = []
  for (let i = 0; i < groupIndex; i++) {
    const groupName = specData.specAttr[i]?.groupName
    const selected = selectedSpecs.value[groupName]
    if (!selected) return false
    selectedIds.push(selected)
  }

  return specList.some((sku) => {
    const skuIds = sku.specSkuId.split('_')
    for (let i = 0; i < selectedIds.length; i++) {
      if (skuIds[i] != selectedIds[i]) return false
    }
    if (skuIds[groupIndex] != currentItemId) return false
    return sku.productStock > 0
  })
}
const handleSpecChoose = (item: { itemId: number; specValue: string }, groupName: string) => {
  selectedSpecs.value[groupName] = item.itemId

  // 更新价格显示
  const specAttr = productDetail.value?.specData?.specAttr || []
  const specList = productDetail.value?.specData?.specList || []

  if (Object.keys(selectedSpecs.value).length === specAttr.length) {
    const idList = specAttr.map((group) => selectedSpecs.value[group.groupName])
    const skuId = idList.join('_')
    const validSku = specList.find((sku) => sku.specSkuId === skuId && sku.productStock > 0)

    if (validSku) {
      showPrice.value = validSku.specForm.productPrice
      specSkuId.value = validSku.specSkuId
    }
  }
}
const goGoodsDetail = (id: number) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}&from=mall` })
}
//加购
const goodsAddCart = async (item) => {
  const { data } = await fetchRecGoodsDetail({
    appId: import.meta.env.VITE_APPID,
    productId: item.productId,
  })
  productDetail.value = data
  showPrice.value = data?.detail?.productPrice
  showPrePay.value = true
  confirmOrderStore.setGoodInfo(data)

  if (data?.detail?.specType === 20 && data?.specData) {
    const specAttr = data.specData.specAttr
    const specList = data.specData.specList || []

    // 找到第一个有库存的SKU
    const firstValidSku = specList.find((sku) => sku.productStock > 0)
    if (firstValidSku) {
      const skuIds = firstValidSku.specSkuId.split('_')
      // 自动选择对应的规格
      specAttr.forEach((group, index) => {
        selectedSpecs.value[group.groupName] = Number(skuIds[index])
      })
      showPrice.value = firstValidSku.specForm.productPrice
      specSkuId.value = firstValidSku.specSkuId
    } else {
      // 如果没有有库存的SKU，显示默认价格范围
      showPrice.value = `${data.detail.productPrice}-${data.detail.highPrice}`
    }
  }
  if (data?.detail?.specType === 10 && data?.detail.skuList && data?.detail.skuList.length > 0) {
    specSkuId.value = data?.detail.skuList[0].specSkuId
    console.log('无规格', data?.detail?.specType, data?.detail.skuList[0].specSkuId)
  }
}
//确认
const goConfirm = async () => {
  if (productDetail.value?.detail?.specType === 20 && !specSkuId.value) {
    uni.showToast({ title: '请选择规格', icon: 'none' })
    return
  }

  handleAddCart()
}
//加购物车
const handleAddCart = async () => {
  const dataBody = {
    appId: import.meta.env.VITE_APPID,
    productId: productDetail.value?.detail?.productId,
    totalNum: buyNubmer.value,
    specSkuId: `${specSkuId?.value ?? '0'}`,
    token: userStore?.token ?? '',
  }

  try {
    await addCart(dataBody)

    cartStore.getCart()
  } catch (error) {
    uni.showToast({
      title: error.msg || '添加失败，请稍后再试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    showPrePay.value = false
  }
}
const handleClose = () => {
  showPrePay.value = false
}
//去购物车
const goCart = () => uni.navigateTo({ url: '/pages/cart/index' })

const paging = ref()
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    //是否模糊搜索
    if (!isSearch.value) {
      let { data: goodsData } = await fetchRecGoodsByCategoryld(
        {
          appId: import.meta.env.VITE_APPID,
          pageIndex: pageNo,
          pageSize,
          keyWord: searchVal.value,
          categoryId: sidebarActive.value ? sidebarActive.value : tab.value,
          latitude: locationStore?.locationInfo?.location?.lat,
          longitude: locationStore?.locationInfo?.location?.lon,
          cityName: locationStore?.locationInfo?.cityName,
        },
        { appId: import.meta.env.VITE_APPID },
      )
      paging.value.complete(goodsData.records)
    } else {
      let obj = await fetchIndexMallData(pageNo, pageSize)
      paging.value.complete(obj?.records)
    }
  } catch (err) {
    paging.value.complete(false)
  }
}
// 查询商品列表
const fetchIndexMallData = async (pageIndex, pageSize) => {
  let { data } = await fetchSelectProductListByCategoryId({
    keyWord: searchVal.value,
    appId: import.meta.env.VITE_APPID,
    pageIndex: pageIndex,
    pageSize: pageSize,
    categoryId: sidebarActive.value ? sidebarActive.value : tab.value,
    cityName: locationStore?.locationInfo?.cityName,
    // cityName: '南京市',
    longitude: locationStore?.locationInfo?.location?.lon,
    latitude: locationStore?.locationInfo?.location?.lat,
  })
  return data
}
// 获取类别数据
const fetchData = async () => {
  try {
    const { data: cateData } = await fetchGoodsCategory()
    categoryList.value = cateData?.list ?? []
    categoryChildrenList.value = categoryList.value[0]?.children ?? []
  } catch (err) {}
}
</script>

<style lang="scss" scoped>
//
:deep(.wd-sidebar-item) {
  background: #f2f2f2 !important;
  color: #666666 !important;
  font-size: 28rpx !important;
  font-family: 'PingFang SC' !important;
  font-weight: 400 !important;
}
:deep(.wd-sidebar__padding) {
  background: #f2f2f2 !important;
}
:deep(.wd-sidebar-item__badge) {
  box-sizing: border-box !important;
  padding-bottom: 10rpx !important;
}
:deep(.wd-sidebar-item--active) {
  background: #ffecdf !important;
  font-weight: 600 !important;
  .wd-sidebar-item__badge::after {
    content: ''; /* 生成一个空的伪元素 */
    position: absolute; /* 绝对定位 */
    left: 50%; /* 水平居中 */
    bottom: 0; /* 底部对齐 */
    transform: translateX(-50%); /* 水平居中调整 */
    border-bottom: 4rpx solid #ff7d26 !important;
    width: 60%; /* 设置宽度为父元素的80%，可以根据需要调整 */
  }
}
:deep(.wd-sidebar-item--active::before) {
  display: none !important;
}
.tabs {
  width: 100%;
  // overflow-x: auto;
  white-space: nowrap;
  display: flex;
  align-items: center;
  background: #f2f2f2;
  height: 120rpx;
  line-height: 120rpx;
  .tab {
    display: inline-block;
    flex-shrink: 0; /* 防止被压缩 */
    min-width: 180rpx; /* 设置最小宽度 */
    box-sizing: border-box;
    padding: 0 40rpx;
    text {
      font-size: 32rpx;
      font-family: 'PingFang SC';
      font-weight: 400;
      text-align: LEFT;
      color: #666666;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-bottom: 10rpx;
    }
  }
  .activeTab {
    color: #333;
    text {
      display: flex;
      color: #ff7d26;
      font-weight: 600;
      font-weight: bold;
    }
  }
}
.products_info_name {
  font-family: 'PingFang SC';
}
.addCart_box {
  position: fixed;
  right: 7%;
  bottom: 29%;
  .addCart_Total {
    position: absolute;
    top: -10rpx;
    right: -8rpx;
    height: 25rpx;
    min-width: 25rpx;
    padding: 4rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    background: linear-gradient(180deg, #fc4133, #ff7a04);
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
:deep(.zp-paging-container-content) {
  padding-bottom: 300rpx;
  box-sizing: border-box;
}
:deep(.wd-input__inner) {
  background: none !important;
}
:deep(.wd-input) {
  background: none !important;
  padding-left: 30rpx;
  padding-right: 20rpx;
  overflow: hidden;
  box-sizing: border-box;
}
:deep(.l-fab) {
  background: transparent;
}
//规格按钮
.custom-btn {
  padding: 10rpx 24rpx;
  font-size: 24rpx;
  border-radius: 12rpx;
  margin: 0;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  line-height: 1.4;
  background-color: transparent;
  border: 1rpx solid #e5e5e5;
  color: #666;
  transition: all 0.2s;

  &.btn-primary {
    border-color: #ff5704;
    color: #ff5704;
    background-color: #fff7f0;
  }

  &.btn-info {
    border-color: #e5e5e5;
    color: #666;
    background-color: #f5f5f5;
  }

  &.btn-disabled {
    background-color: #cccccc !important;
    color: white !important;
    border-color: #eeeeee !important;
    cursor: not-allowed;
  }
}
</style>

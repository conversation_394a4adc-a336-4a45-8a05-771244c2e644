<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full">
    <SimpleNavBar title="设置" />
    <div v-if="data?.userInfo" class="grid grid-cols-1 grid-auto-rows-min gap-y-11px">
      <div class="w-full h361rpx bg-#ffffff flex items-center justify-center">
        <div @click="run" class="w198rpx h-198rpx rd-1/2 relative">
          <wd-img
            width="198rpx"
            height="198rpx"
            mode="aspectFill"
            radius="50%"
            :src="imgUrl?.filePath ?? data?.userInfo?.avatarUrl"
          ></wd-img>

          <div
            class="bg-[rgba(0,0,0,0.55)] w-46rpx h46rpx rd-1/2 flex items-center justify-center absolute right-0 bottom-0"
          >
            <div class="i-carbon-camera text-28rpx text-#ffffff"></div>
          </div>
        </div>
      </div>
      <div class="w-full bg-#ffffff px34rpx box-border">
        <wd-form ref="form" :model="data?.userInfo">
          <wd-cell-group border>
            <wd-input
              label="会员ID"
              label-width="100px"
              readonly
              prop="userId"
              v-model="data.userInfo.userId"
            />
            <wd-input
              label="昵称"
              label-width="100px"
              prop="nickName"
              placeholder="请输入昵称"
              clearable
              v-model="data.userInfo.nickName"
              :rules="[{ required: true, message: '请请输入昵称' }]"
            />
            <wd-cell title-width="100px" prop="mobile" title="手机">
              <div class="flex items-center justify-between">
                <wd-text
                  :text="userStore?.userInfo?.mobile ? userStore?.userInfo?.mobile : '请绑定手机号'"
                  mode="phone"
                  :format="true"
                ></wd-text>
                <wd-button
                  v-if="userStore?.userInfo?.mobile"
                  custom-class="!m-unset"
                  @click="handleChangeMobile"
                  size="small"
                >
                  修改
                </wd-button>
                <wd-button
                  v-else
                  custom-class="!m-unset"
                  size="small"
                  openType="getPhoneNumber"
                  @getphonenumber="handleGetPhoneNumber"
                >
                  绑定
                </wd-button>
              </div>
            </wd-cell>

            <wd-cell title-width="100px" prop="password" title="支付密码">
              <div class="flex items-center justify-between">
                <span v-if="data?.userInfo?.paymentPassword && !paymentVisible">已设置</span>
                <wd-input
                  v-if="paymentVisible"
                  no-border
                  placeholder=""
                  showPassword
                  v-model="paymentPassword"
                  readonly
                  suffix-icon=" "
                ></wd-input>
                <wd-keyboard
                  v-model="paymentPassword"
                  :maxlength="6"
                  mode="custom"
                  safeAreaInsetBottom
                  v-model:visible="paymentVisible"
                  title="六位数字密码"
                  close-text="完成"
                  @close="handlePasswordConfirm"
                ></wd-keyboard>
                <wd-button
                  custom-class="!m-unset"
                  @click="paymentVisible = !paymentVisible"
                  size="small"
                >
                  {{ data?.userInfo?.paymentPassword ? '修改' : '设置' }}
                </wd-button>
              </div>
            </wd-cell>

            <wd-cell title-width="100px" prop="gender" title="性别">
              <wd-radio-group
                v-model="data.userInfo.gender"
                custom-class="!flex"
                shape="dot"
                inline
              >
                <wd-radio :value="1">男</wd-radio>
                <wd-radio :value="2">女</wd-radio>
              </wd-radio-group>
            </wd-cell>
          </wd-cell-group>
        </wd-form>
      </div>
      <view class="footer">
        <wd-button type="primary" custom-class="!mx-26rpx" size="large" @click="handleSubmit" block>
          保存
        </wd-button>
      </view>
    </div>
    <div
      @click="userStore.logOut"
      class="w-full text-26rpx text-#999999 flex items-center justify-center fixed left-0"
      :style="{ bottom: `${safeAreaInsets?.bottom || 15}px` }"
    >
      退出登录
    </div>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { updateMobile, userSetting, userUpdate, setUserPaymentPassword } from '@/service'
import { useUserStore } from '@/store'
import { useMessage } from 'wot-design-uni'

const { safeAreaInsets } = uni.getSystemInfoSync()

const message = useMessage()

const userStore = useUserStore()

const { data, run: runData } = useRequest(
  () => userSetting({ appId: import.meta.env.VITE_APPID, token: userStore.token }),
  {
    immediate: true,
  },
)

const { data: imgUrl, run } = useUpload<{ fileId: number; filePath: string }>({
  appId: import.meta.env.VITE_APPID,
})

watch(
  () => imgUrl.value,
  async (v) => {
    if (v) {
      data.value.userInfo.avatarUrl = v.filePath
      await userUpdate({ ...data.value.userInfo, token: userStore.token })
      runData()
      userStore.fetchUserCenterConfig()
      uni.showToast({ title: '修改成功', icon: 'success' })
    }
  },
)

const form = ref()

const paymentVisible = ref(false)
const paymentPassword = ref('')

const handlePasswordConfirm = async () => {
  if (!paymentPassword.value) {
    return
  }
  if (paymentPassword.value.length < 6) {
    uni.showToast({
      title: '请输入6位支付密码',
      icon: 'none',
    })
    return
  }
  await setUserPaymentPassword({ paymentPassword: paymentPassword.value })
  runData()
  userStore.fetchUserCenterConfig()
}

const handleGetPhoneNumber = (e: {
  cloudID: string
  code: string
  encryptedData: string
  errMsg: string
  iv: string
}) => {
  userStore.fetchUserMobile(e)
}

const mobileChangeValue = ref('')

const handleChangeMobile = () => {
  message
    .prompt({
      title: '请输入手机号',
      inputValue: mobileChangeValue.value,
      inputPlaceholder: '请输入手机号',
      inputPattern: /^1[3-9]\d{9}$/,
      inputError: '手机号输入不正确',
    })
    .then(async (resp) => {
      await updateMobile({
        token: userStore.token,
        appId: import.meta.env.VITE_APPID,
        mobile: resp?.value,
      })
      runData()
      userStore.fetchUserCenterConfig()
      uni.showToast({ title: '修改成功', icon: 'success' })
    })
    .catch((error) => {
      console.log(error)
    })
}

function handleSubmit() {
  form.value
    .validate()
    .then(async ({ valid, errors }) => {
      if (valid) {
        await userUpdate({ ...data.value.userInfo, token: userStore.token })
        runData()
        userStore.fetchUserCenterConfig()
        uni.showToast({ title: '修改成功', icon: 'success', duration: 1000 })
        setTimeout(() => {
          uni.navigateBack()
        }, 1300)
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
</script>

<style lang="scss" scoped>
:deep(.wd-input__label::after) {
  display: none !important;
}
:deep(.wd-input__label) {
  padding-left: unset !important;
}
</style>

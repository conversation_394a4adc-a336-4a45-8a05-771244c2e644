<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '订单核销',
  },
}
</route>

<template>
  <view class="pb-150rpx">
    <!--详情状态-->
    <view class="p30rpx bg-#ff5704 color-white flex items-center gap-x-20rpx">
      <wd-icon name="warning" color="white" size="50rpx"></wd-icon>
      <view class="state-cont flex-1">
        <view class="state-txt flex items-center">
          <text class="desc text-34rpx">{{ detail.stateText ? detail.stateText : '待核销' }}</text>
        </view>
      </view>
      <view class="dot-bg"></view>
    </view>

    <!-- 上门自提：自提门店 -->
    <view
      class="order-express p30rpx mb20rpx box-border flex items-center"
      v-if="detail.deliveryType == 20"
    >
      <view class="flow-delivery__title text-32rpx text-#cccccc">
        <wd-icon name="warning" color="#cccccc" size="32rpx"></wd-icon>
        <span class="">自提门店</span>
      </view>
      <view class="cont-text ml20rpx">
        <view class="text-#000000 text-24rpx">
          <view classs=" flex items-center gap20rpx">
            <text>
              {{ extractStore.storeName }}
            </text>
            <text>
              {{ extractStore.phone }}
            </text>
          </view>
          <view class="text-#999 pt10rpx">
            {{ extractStore.province }} {{ extractStore.city }}
            {{ extractStore.region }}
            {{ extractStore.address }}
          </view>
        </view>
      </view>
    </view>

    <!--购物列表-->
    <view class="px30rpx py10rpx mb20rpx box-border bg-white">
      <view class="group-hd h90rpx flex items-center" style="border-bottom: 1rpx solid #eeeeee">
        <view class="left text-32rpx gap10rpx flex">
          <text class="i-carbon-store"></text>
          <text class="min-name">{{ detail?.supplierName }}</text>
          {{ detail?.extractStore?.storeName ? `(${detail?.extractStore?.storeName})` : '' }}

          <text class="i-carbon-chevron-right"></text>
        </view>
      </view>
      <view class="list">
        <view class="one-product py20rpx" v-for="(item, index) in detail.product" :key="index">
          <view class="flex gap-30rpx">
            <view class="w160rpx h-160rpx">
              <image :src="item.productImage" mode="aspectFit"></image>
            </view>
            <view class="flex-1">
              <view class="text-28rpx text-#666">{{ item.productName }}</view>
              <view class="pt10rpx px30rpx flex justify-between items-center">
                <view class="price text-22rpx">
                  ¥
                  <text class="text-40rpx">{{ item.productPrice }}</text>
                </view>
                <view class="text-24rpx text-#999">x{{ item.totalNum }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!--订单信息-->
    <view class="px30rpx py10rpx box-border bg-white text-28rpx">
      <view class="py20rpx">
        <text class="text-#999">订单编号：</text>
        <text>{{ detail.orderNo }}</text>
      </view>
      <view class="py20rpx">
        <text class="text-#999">下单时间：</text>
        <text>{{ detail.createTime }}</text>
      </view>
      <view class="py20rpx">
        <text class="text-#999">支付方式：</text>
        <text>{{ detail.payTypeText }}</text>
      </view>
      <view class="py20rpx">
        <text class="text-#999">配送方式：</text>
        <text>{{ detail.deliveryTypeText }}</text>
      </view>
      <view class="py20rpx flex items-center justify-between">
        <text class="text-#999">商品金额</text>
        <text>¥ {{ detail.orderPrice }}</text>
      </view>
      <!-- <view class="py20rpx flex items-center justify-between">
        <text class="text-#999">运费</text>
        <text>+ ¥ {{ detail.expressPrice }}</text>
      </view> -->
      <view class="py20rpx fw-bold text-34rpx flex justify-end" @click="handleShow">
        应付金额：
        <text class="text-red">¥ {{ detail.orderPrice }}</text>
        <view class="i-carbon-chevron-right"></view>
      </view>
    </view>

    <!-- 操作栏 -->
    <view v-if="detail.orderStatus != 20" class="flow-fixed-footer b-f">
      <!-- 订单核销 -->
      <view
        v-if="detail.payStatus == 20 && detail.deliveryType == 20 && detail.deliveryStatus == 10"
        class="box-border px20rpx"
      >
        <button
          class="rounded-30rpx bg-red text-28rpx m0 text-white"
          @click="onSubmitExtract(detail.orderId)"
        >
          确认核销
        </button>
      </view>
    </view>

    <!-- 付款明细 -->
    <wd-popup
      v-model="showPaymentDetails"
      position="bottom"
      custom-style="min-height: 200px;max-height:600px;"
      custom-class="p30rpx rounded-30rpx flex flex-col justify-between "
      :safe-area-inset-bottom="true"
      @close="handleClose"
    >
      <view class="">
        <view class="py20rpx" v-if="detail.orderSource == 80">
          <text class="">定金：</text>
          <text>￥{{ detail.advance.payPrice }}</text>
        </view>

        <view class="py20rpx flex justify-between">
          <text class="">运费</text>
          <text>¥ {{ detail.expressPrice }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">善豆实付</text>
          <text>¥ {{ detail.payShanBean }}</text>
        </view>
        <view v-if="detail.balance != '0.00'" class="py20rpx flex justify-between">
          <text class="">余额实付</text>
          <text>¥ {{ detail.balance }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">银行卡实付</text>
          <text>¥ {{ detail.onlineMoney }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">红包实付</text>
          <text>¥ {{ detail.payRedPocket }}</text>
        </view>
        <view class="py20rpx flex justify-between">
          <text class="">实付</text>
          <text>¥ {{ detail.payPrice }}</text>
        </view>
        <view class="py20rpx" v-if="detail.payPrice && detail.orderSource == 80">
          <text class="">尾款：</text>
          <text>￥{{ detail.payPrice }}</text>
        </view>
        <view class="py20rpx" v-if="detail.advance && detail.advance.reduceMoney > 0">
          <text class="">尾款立减</text>
          <text>-¥ {{ detail.advance.reduceMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.orderSource == 20">
          <text class="">扣除{{ pointsName }}数：</text>
          <text>-{{ detail.pointsNum }}{{ pointsName }}</text>
        </view>
        <!-- <view class="py20rpx">
				<text class="">订单总额</text>
				<text>¥ {{ detail.totalPrice }}</text>
			</view> -->
        <view class="py20rpx" v-if="detail.updatePrice != '0.00'">
          <text class="gray9">订单差价</text>
          <text>¥ {{ detail.updatePrice }}</text>
        </view>
        <view class="py20rpx" v-if="detail.productReduceMoney > 0">
          <text class="">商品立减</text>
          <text>-¥ {{ detail.productReduceMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.fullreduceMoney > 0">
          <text class="">满减</text>
          <text>-¥ {{ detail.fullreduceMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.pointsMoney > 0">
          <text class="">{{ pointsName }}抵扣</text>
          <text>-¥ {{ detail.pointsMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.couponMoney > 0">
          <text class="">商户优惠券</text>
          <text class="theme-price">- ¥ {{ detail.couponMoney }}</text>
        </view>
        <view class="py20rpx" v-if="detail.couponMoneySys > 0">
          <text class="">平台优惠券</text>
          <text class="theme-price">- ¥ {{ detail.couponMoneySys }}</text>
        </view>
      </view>
      <view class="flex justify-end">
        <wd-button @click="handleClose" custom-class="!mx-25rpx">确认</wd-button>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { fetchStoreOrderDetail, storeOrderExtract } from '@/service'
//
import { ref } from 'vue'
let showPaymentDetails = ref(false)

// 定义订单详情类型
interface OrderProduct {
  productImage: string
  productName: string
  productPrice: string
  totalNum: number
}

interface ExtractStore {
  storeName: string
  phone: string
  province: string
  city: string
  region: string
  address: string
}

interface OrderDetail {
  orderId: string
  stateText: string
  deliveryType: number
  deliveryTypeText: string
  orderNo: string
  createTime: string
  payTypeText: string
  orderPrice: string
  expressPrice: string
  product: OrderProduct[]
  extractStore?: ExtractStore
  orderStatus: number
  payStatus: number
  deliveryStatus: number
}
// 定义类型
interface ExtractStore {
  storeName: string
  phone: string
  province: string
  city: string
  region: string
  address: string
}

// 模拟数据
const extractStore = ref<any>(null)
let pointsName = ref('善豆')

// 模拟数据
const detail = ref<any>(null)
let orderNo = ref('')
onLoad((options) => {
  orderNo.value = options.orderNo
  getData()
})
const getData = async () => {
  uni.showLoading({
    title: '加载中',
  })
  try {
    const { data } = await fetchStoreOrderDetail({
      appId: import.meta.env.VITE_APPID,
      orderNo: orderNo.value,
    })
    detail.value = data
    extractStore.value = data.extractStore
    uni.hideLoading()
  } catch (err) {
    console.log('err', err)

    uni.hideLoading()
  }
}
// 核销方法
const onSubmitExtract = async (orderId: string) => {
  wx.showModal({
    title: '提示',
    content: '您确定要核销吗?',
    success: async function (o) {
      if (o.confirm) {
        try {
          const res = await storeOrderExtract({
            appId: import.meta.env.VITE_APPID,
            orderId,
            source: 'wx',
          })
          getData()
          uni.showToast({
            title: res.msg || '核销成功',
            duration: 2000,
            icon: 'success',
          })
        } catch (err) {
          uni.showToast({
            title: err.data.msg || '核销失败',
            duration: 2000,
            icon: 'none',
          })
          console.error('核销异常:', err)
        }
      }
    },
  })
}

//关闭付款明细模态框
const handleClose = () => {
  showPaymentDetails.value = false
  console.log('handleClose', showPaymentDetails.value)
}
const handleShow = () => {
  showPaymentDetails.value = true
}
</script>

<style lang="scss" scoped>
//
.order-express {
  background: #ffffff;
}

.order-express .icon-box .iconfont {
  font-size: 50rpx;
}
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full">
    <z-paging
      :default-page-size="10"
      @query="queryList"
      :show-loading-more-no-more-view="false"
      v-model="dataList"
      :paging-style="{
        padding: '24rpx',
        'box-sizing': 'border-box',
        background: '#ffffff',
        paddingBottom: '60rpx',
      }"
      ref="paging"
    >
      <template #top>
        <SimpleNavBar title="直播" />
        <wd-config-provider :themeVars="{ colorTheme: '#479BFF' }">
          <wd-tabs @change="handleChange" auto-line-width>
            <block v-for="item in tabs" :key="item.key">
              <wd-tab :title="`${item.label}`" :name="item.key"></wd-tab>
            </block>
          </wd-tabs>
        </wd-config-provider>
      </template>
      <div class="grid grid-cols-2 gap-20rpx pt-20rpx box-border">
        <div
          @click="gotoShiPinLive(item?.sphId)"
          v-for="item in dataList"
          :key="item?.id"
          class="w-full h380rpx rd-20rpx relative"
        >
          <wd-img
            width="100%"
            height="380rpx"
            radius="20rpx"
            mode="aspectFill"
            :src="item?.coverImgPath"
          />

          <!-- 状态 -->
          <div
            class="h-36rpx lh-36rpx border-rd-40rpx bg-[#00000066] absolute right-10rpx top-14rpx flex items-center px20rpx box-border gap-x-8rpx"
          >
            <template v-if="item?.online === 2">
              <div class="w-12rpx h-12rpx bg-[#18eb89] rd-1/2"></div>
              <span class="text-20rpx text-#ffffff">正在直播</span>
            </template>
            <template v-if="item?.online === 1">
              <div class="w-12rpx h-12rpx bg-[#EB9A18] rd-1/2"></div>
              <span class="text-20rpx text-#ffffff">未开始</span>
            </template>
            <template v-if="item?.online === 3">
              <div class="w-12rpx h-12rpx bg-[#999999] rd-1/2"></div>
              <span class="text-20rpx text-#ffffff">已结束</span>
            </template>
          </div>

          <!-- 信息 -->
          <div
            class="flex items-center justify-between w-full absolute bottom-10rpx left-0 px-20rpx box-border text-#ffffff"
          >
            <div class="text-28rpx">@{{ item?.sphName }}</div>
            <div class="flex items-center gap-x-5rpx">
              <!-- <div class="i-carbon-user text-20rpx text-#ffffff"></div>
              <div class="text-20rpx">234人</div> -->
            </div>
          </div>

          <!-- button -->
          <div
            class="text-20rpx text-#ffffff w-180rpx h-52rpx border-rd-40rpx border-2rpx border-solid border-#ffffff box-border flex items-center justify-center absolute top-50% left-50% translate-x--50% translate-y--50%"
          >
            点击进入直播间
          </div>

          <!-- 分类 -->
          <div
            v-if="item?.type === 1"
            class="w-144rpx h-52rpx flex items-center justify-center gap-x-8rpx rd-tl-20rpx rd-br-20rpx absolute top-0 left-0 bg-white/20"
          >
            <wd-img
              width="38rpx"
              height="38rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/07/01/9b6c6853ae4c4dcdab6f29963a5fd4b9.png"
            ></wd-img>
            <span class="text-#479BFF text-20rpx">官方频道</span>
          </div>
        </div>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { fetchLiveList } from '@/service'

const tab = ref(1)

const tabs = ref([
  {
    label: '官方直播',
    key: 1,
  },
  {
    label: '商家直播',
    key: 2,
  },
])

const paging = ref()
const dataList = ref<Api.Live.UserLiveListItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await fetchLiveList({ pageIndex, pageSize, type: tab.value })

      // #ifdef MP-WEIXIN
      const records = data?.list?.records || []

      const promises = records.map((item) => {
        return new Promise((resolve) => {
          uni.getChannelsLiveInfo({
            finderUserName: item?.sphId,
            success: (res) => {
              if (res.errMsg === 'getChannelsLiveInfo:ok') {
                console.log('getChannelsLiveInfo成功', res)
                item.online = res.status
              }
              resolve(item)
            },
            fail: (res) => {
              console.log('getChannelsLiveInfo失败', res)
              item.online = -1
              resolve(item)
            },
          })
        })
      })

      await Promise.all(promises)
      // #endif

      paging?.value?.complete(data?.list?.records)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

const handleLive = (res) => {
  uni.openChannelsLive({
    finderUserName: res.finderUserName,
    feedId: res.feedId,
    nonceId: res.nonceId,
    success: (res) => {
      console.log('openChannelsLive成功', res)
    },
    fail: (res) => {
      console.log('openChannelsLive失败', res)
    },
  })
}

const gotoShiPinLive = (finderUserName: string) => {
  // #ifdef  MP-WEIXIN
  if (finderUserName) {
    // 视频号直播
    uni.getChannelsLiveInfo({
      //视频号ID,在登录视频号中获取
      finderUserName: finderUserName,
      success: (res) => {
        if (res.errMsg === 'getChannelsLiveInfo:ok') {
          console.log('getChannelsLiveInfo成功', res)
          if (res.status == 2 || res.status == 3) {
            const result: UniApp.GetChannelsLiveInfoSuccessCallbackResult & {
              finderUserName: string
            } = res as UniApp.GetChannelsLiveInfoSuccessCallbackResult & {
              finderUserName: string
            }
            result.finderUserName = finderUserName
            handleLive(res)
          }
          // 返回内容
          // feedId:直播 feedId
          // nonceId:直播 nonceId
          // description:直播主题
          // status:直播状态，2直播中，3直播结束
          // headUrl:直播封面
          // nickname:视频号昵称
        }
      },
      fail: (res) => {
        console.log('getChannelsLiveInfo失败', res)
      },
    })
  }
  // #endif
}

const handleChange = ({ name }) => {
  tab.value = name
  paging.value?.reload()
}
</script>

<style lang="scss" scoped>
//
</style>

<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '钱包明细',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden p30rpx box-border h-full">
    <z-paging
      ref="paging"
      v-model="tableData"
      :default-page-size="10"
      class="flex-1"
      :fixed="false"
      :auto="false"
      @query="queryList"
    >
      <view class="d-b-c border-b p-30-0" v-for="(item, index) in tableData" :key="item.logId">
        <view class="d-s-s f-w d-c flex-1">
          <text class="f30">{{ item.sceneText }}</text>
          <text class="pt10 gray9 f22">{{ item.createTime }}</text>
        </view>
        <view class="red !text-red" v-if="Number(item.money) > 0">+{{ item.money }}元</view>
        <view class="red !text-#333333" v-else>{{ item.money }}元</view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { fetchUserBalanceLogList } from '@/service'
import { computed, ref } from 'vue'

// 响应式状态
const tableData = ref<Api.User.BalanceLogEntry[]>([])
const type = ref<string>('all')

// 生命周期 - 页面加载
onLoad((e: Record<string, string>) => {
  type.value = e.type || 'all'
  queryList(1, 10)
})
onMounted(() => {})

const paging = ref()
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await fetchUserBalanceLogList({
      appId: import.meta.env.VITE_APPID,
      pageIndex: pageNo,
      pageSize: pageSize,
      type: type.value,
    })
    paging.value.complete(data?.records)
  } catch (err) {
    paging.value.complete(false)
  }
}
</script>

<style lang="scss" scoped>
/* 保持原有样式不变 */
.p-0-30 {
  padding: 0 30rpx;
}
.bg-white {
  background-color: #ffffff;
}
.d-b-c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.border-b {
  border-bottom: 1rpx solid #f2f2f2;
}
.p-30-0 {
  padding: 30rpx 0;
}
.d-s-s {
  display: flex;
  flex-direction: column;
  justify-content: start;
}
.f-w {
  flex-wrap: wrap;
}
.f30 {
  font-size: 30rpx;
}
.pt10 {
  padding-top: 10rpx;
}
.gray9 {
  color: #999999;
}
.f22 {
  font-size: 22rpx;
}
.red {
  color: #ff4d4f;
}
.d-c-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
.p30 {
  padding: 30rpx;
}
</style>
